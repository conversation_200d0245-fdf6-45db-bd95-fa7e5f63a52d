{"version": 3, "sources": ["src/app/components/workout-programs/workout-program-list.component.css"], "sourcesContent": ["/* Workout Program List Specific Styles */\n\n/* Page Header */\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: var(--spacing-lg);\n  padding: var(--spacing-lg);\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\n  border-radius: var(--border-radius-lg);\n  color: white;\n}\n\n.page-title-container {\n  flex: 1;\n}\n\n.page-title {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: 1.75rem;\n  font-weight: 700;\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n\n.page-icon {\n  font-size: 1.5rem;\n}\n\n.page-subtitle {\n  margin: 0;\n  opacity: 0.9;\n  font-size: 1rem;\n}\n\n.page-actions {\n  display: flex;\n  gap: var(--spacing-sm);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .page-header {\n    flex-direction: column;\n    gap: var(--spacing-md);\n  }\n\n  .page-actions {\n    width: 100%;\n    justify-content: stretch;\n  }\n\n  .page-actions .modern-btn {\n    flex: 1;\n  }\n}\n\n@media (max-width: 480px) {\n  .page-header {\n    padding: var(--spacing-md);\n  }\n\n  .page-title {\n    font-size: 1.5rem;\n  }\n}\n\n/* Dark Mode Adjustments */\n[data-theme=\"dark\"] .page-header {\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\n}\n\n/* Existing styles below */\n\n.text-truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.table th {\n  font-weight: 600;\n  color: var(--text-secondary);\n  border-bottom: 2px solid var(--border-color);\n  padding: 1rem 0.75rem;\n}\n\n.table td {\n  padding: 1rem 0.75rem;\n  vertical-align: middle;\n  border-bottom: 1px solid var(--border-color);\n}\n\n.modern-btn-sm {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n}\n\n.pagination .page-link {\n  color: var(--primary);\n  background-color: var(--bg-primary);\n  border: 1px solid var(--border-color);\n  padding: 0.5rem 0.75rem;\n  margin: 0 0.125rem;\n  border-radius: var(--border-radius-md);\n  transition: all var(--transition-speed) var(--transition-timing);\n}\n\n.pagination .page-link:hover {\n  color: var(--white);\n  background-color: var(--primary);\n  border-color: var(--primary);\n  transform: translateY(-1px);\n}\n\n.pagination .page-item.active .page-link {\n  color: var(--white);\n  background-color: var(--primary);\n  border-color: var(--primary);\n}\n\n.pagination .page-item.disabled .page-link {\n  color: var(--text-secondary);\n  background-color: var(--bg-secondary);\n  border-color: var(--border-color);\n  cursor: not-allowed;\n}\n\n.spinner-border {\n  width: 3rem;\n  height: 3rem;\n}\n\n/* Badge Styles */\n.modern-badge-secondary {\n  background-color: var(--secondary-light);\n  color: var(--secondary);\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .table-responsive {\n    font-size: 0.875rem;\n  }\n  \n  .modern-btn-sm {\n    padding: 0.125rem 0.25rem;\n    font-size: 0.75rem;\n  }\n  \n  .d-flex.gap-1 {\n    flex-direction: column;\n    gap: 0.25rem !important;\n  }\n  \n  .text-truncate {\n    max-width: 120px !important;\n  }\n}\n\n/* Dark mode specific adjustments */\n[data-theme=\"dark\"] .pagination .page-link {\n  background-color: var(--bg-secondary);\n  border-color: var(--border-color);\n  color: var(--text-primary);\n}\n\n[data-theme=\"dark\"] .pagination .page-link:hover {\n  background-color: var(--primary);\n  border-color: var(--primary);\n  color: var(--white);\n}\n\n[data-theme=\"dark\"] .pagination .page-item.disabled .page-link {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n  color: var(--text-secondary);\n}\n"], "mappings": ";AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,iBAAe,IAAI;AACnB,WAAS,IAAI;AACb;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,IAAI,gBAAgB;AAC3E,iBAAe,IAAI;AACnB,SAAO;AACT;AAEA,CAAC;AACC,QAAM;AACR;AAEA,CAAC;AACC,UAAQ,EAAE,EAAE,IAAI,cAAc;AAC9B,aAAW;AACX,eAAa;AACb,WAAS;AACT,eAAa;AACb,OAAK,IAAI;AACX;AAEA,CAAC;AACC,aAAW;AACb;AAEA,CAAC;AACC,UAAQ;AACR,WAAS;AACT,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,OAAK,IAAI;AACX;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAzCD;AA0CG,oBAAgB;AAChB,SAAK,IAAI;AACX;AAEA,GAZD;AAaG,WAAO;AACP,qBAAiB;AACnB;AAEA,GAjBD,aAiBe,CAAC;AACb,UAAM;AACR;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAzDD;AA0DG,aAAS,IAAI;AACf;AAEA,GA9CD;AA+CG,eAAW;AACb;AACF;AAGA,CAAC,iBAAmB,CAnEnB;AAoEC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,IAAI,gBAAgB;AAC7E;AAIA,CAAC;AACC,YAAU;AACV,iBAAe;AACf,eAAa;AACf;AAEA,CAAC,MAAM;AACL,eAAa;AACb,SAAO,IAAI;AACX,iBAAe,IAAI,MAAM,IAAI;AAC7B,WAAS,KAAK;AAChB;AAEA,CAPC,MAOM;AACL,WAAS,KAAK;AACd,kBAAgB;AAChB,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CAAC;AACC,WAAS,QAAQ;AACjB,aAAW;AACb;AAEA,CAAC,WAAW,CAAC;AACX,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,UAAQ,IAAI,MAAM,IAAI;AACtB,WAAS,OAAO;AAChB,UAAQ,EAAE;AACV,iBAAe,IAAI;AACnB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAVC,WAUW,CAVC,SAUS;AACpB,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,aAAW,WAAW;AACxB;AAEA,CAjBC,WAiBW,CAAC,SAAS,CAAC,OAAO,CAjBjB;AAkBX,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAvBC,WAuBW,CANC,SAMS,CAAC,SAAS,CAvBnB;AAwBX,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,UAAQ;AACV;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACV;AAGA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAAC;AACC,eAAW;AACb;AAEA,GApDD;AAqDG,aAAS,SAAS;AAClB,eAAW;AACb;AAEA,GAAC,MAAM,CAAC;AACN,oBAAgB;AAChB,SAAK;AACP;AAEA,GAjFD;AAkFG,eAAW;AACb;AACF;AAGA,CAAC,iBAAmB,CA/DnB,WA+D+B,CA/DnB;AAgEX,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CArEnB,WAqE+B,CArEnB,SAqE6B;AACxC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA3EnB,WA2E+B,CA1DnB,SA0D6B,CApDnB,SAoD6B,CA3EvC;AA4EX,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;", "names": []}