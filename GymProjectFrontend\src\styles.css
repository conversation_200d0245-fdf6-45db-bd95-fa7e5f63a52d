/* You can add global styles to this file, and also import other style files */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
@import './app/styles/modern-components.css';

/* Token refresh handling - prevents login page flash */
body.refreshing-token,
body.initializing {
  overflow: hidden;
}

body.refreshing-token::before,
body.initializing::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background-color, #f8f9fa);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

body.refreshing-token::after,
body.initializing::after {
  content: '';
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: var(--primary-color, #4361ee);
  border-bottom-color: var(--primary-color, #4361ee);
  z-index: 10000;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
:root {
    /* Light Theme Variables */
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #4895ef;
    --background-color: #f8f9fa;
    --card-bg-color: #ffffff;
    --text-color: #212529;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --sidebar-bg: #ffffff;
    --sidebar-text: #212529;
    --sidebar-hover: #f1f3f5;
    --sidebar-active: #e9ecef;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --input-text: #212529;
    --btn-primary-bg: #4361ee;
    --btn-primary-text: #ffffff;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --spinner-color: #4361ee;
}

[data-theme="dark"] {
    /* Dark Theme Variables */
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #4895ef;
    --background-color: #121212;
    --card-bg-color: #1e1e1e;
    --text-color: #e9ecef;
    --text-muted: #adb5bd;
    --border-color: #343a40;
    --sidebar-bg: #1e1e1e;
    --sidebar-text: #e9ecef;
    --sidebar-hover: #2d2d2d;
    --sidebar-active: #343a40;
    --input-bg: #2d2d2d;
    --input-border: #495057;
    --input-text: #e9ecef;
    --btn-primary-bg: #4361ee;
    --btn-primary-text: #ffffff;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --spinner-color: #4895ef;

    /* --- Dark Mode Styles for Material Components (Moved from expense-dialog.component.css) --- */

    /* Target the dialog container specifically for dark mode */
    .mat-mdc-dialog-container .mdc-dialog__surface {
      background-color: var(--bg-secondary) !important; /* Use secondary background for dialog */
      color: var(--text-primary) !important; /* Use primary text color */
    }

    /* Adjust title color */
    .mat-mdc-dialog-title {
       color: var(--text-primary) !important;
       border-bottom-color: var(--border-color) !important; /* Adjust border if needed */
    }

    /* Adjust action button colors if needed (usually handled by theme) */
     /* .mat-mdc-dialog-actions {} - Boş kural kaldırıldı */

     /* Ensure form field labels and inputs inherit colors correctly in dark mode */
     .mat-mdc-form-field .mdc-floating-label,
     .mat-mdc-form-field .mat-mdc-form-field-label,
     .mat-mdc-select-value,
     .mat-mdc-option, .mat-mdc-option .mdc-list-item__primary-text { /* Seçenek metnini daha güçlü hedefle */
         color: var(--text-color) !important; /* --text-primary yerine --text-color deneyelim */
     }

     /* Force placeholder visibility in dark mode */
     input::placeholder,
     select::placeholder,
     textarea::placeholder,
     .form-control::placeholder {
         color: #e9ecef !important;
         opacity: 0.8 !important;
     }

     /* Select dropdown panel for dark mode */
     .mat-mdc-select-panel {
         background-color: var(--bg-secondary) !important;
         border: 1px solid var(--border-color) !important;
     }

     /* Select options for dark mode */
     .mat-mdc-option {
         color: var(--text-primary) !important;
         background-color: transparent !important;
     }

     .mat-mdc-option:hover {
         background-color: var(--bg-tertiary) !important;
     }

     .mat-mdc-option.mdc-list-item--selected {
         background-color: var(--primary) !important;
         color: white !important;
     }

     /* Datepicker panel for dark mode */
     .mat-datepicker-popup {
         background-color: var(--bg-secondary) !important;
         border: 1px solid var(--border-color) !important;
     }

     .mat-calendar {
         background-color: var(--bg-secondary) !important;
         color: var(--text-primary) !important;
     }

     .mat-calendar-header {
         background-color: var(--bg-tertiary) !important;
         color: var(--text-primary) !important;
     }

     .mat-calendar-body-cell-content {
         color: var(--text-primary) !important;
     }

     .mat-calendar-body-selected {
         background-color: var(--primary) !important;
         color: white !important;
     }

     .mat-calendar-body-today:not(.mat-calendar-body-selected) {
         border-color: var(--primary) !important;
     }
     /* Style the select dropdown panel */
     .mat-mdc-select-panel, .mat-mdc-autocomplete-panel { /* Hem select hem autocomplete panelini hedefle */
        background-color: var(--card-bg-color) !important; /* --bg-tertiary yerine --card-bg-color deneyelim */
     }
     /* Autocomplete için ek metin rengi kuralı (daha önce eklediğimizi buraya taşıyalım) */
     .mat-mdc-autocomplete-panel .mat-mdc-option {
        color: var(--text-color) !important;
     }
     .mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text {
        color: var(--primary) !important; /* Color for selected option text */
     }
      .mat-mdc-option:hover:not(.mdc-list-item--disabled),
      .mat-mdc-option:focus:not(.mdc-list-item--disabled) {
        background-color: rgba(var(--primary-rgb), 0.1) !important; /* Hover/focus background */
      }


     .mat-mdc-form-field .mdc-text-field__input {
         color: var(--text-primary) !important;
         caret-color: var(--text-primary) !important; /* Ensure cursor is visible */
     }
      /* Adjust filled form field background */
      .mat-mdc-form-field-type-mat-input .mat-mdc-form-field-flex,
      .mat-mdc-form-field-type-mat-select .mat-mdc-form-field-flex {
         background-color: transparent !important; /* Tekrar transparent yapıldı */
      }
       /* Adjust form field outline/underline color */
      .mat-mdc-form-field .mdc-line-ripple::before,
      .mat-mdc-form-field .mdc-notched-outline__leading,
      .mat-mdc-form-field .mdc-notched-outline__notch,
      .mat-mdc-form-field .mdc-notched-outline__trailing {
          border-color: var(--input-border) !important;
      }
       /* Adjust focused outline/underline color */
       .mat-mdc-form-field.mat-focused .mdc-line-ripple::after,
       .mat-mdc-form-field.mat-focused .mdc-notched-outline__leading,
       .mat-mdc-form-field.mat-focused .mdc-notched-outline__notch,
       .mat-mdc-form-field.mat-focused .mdc-notched-outline__trailing {
           border-color: var(--primary-color) !important;
           border-width: 2px !important;
       }
        /* Adjust floating label color when focused */
       .mat-mdc-form-field.mat-focused .mdc-floating-label {
           color: var(--primary-color) !important;
       }
       /* Hint text color in dark mode */
       .mat-mdc-form-field-hint {
           color: var(--text-muted) !important; /* Use muted text color from theme */
           opacity: 0.85 !important; /* Slightly increase opacity for better visibility */
       }


     /* Adjust datepicker colors */
      .mat-datepicker-content {
          background-color: var(--bg-tertiary) !important;
          color: var(--text-primary) !important;
      }
      .mat-calendar-body-label,
      .mat-calendar-table-header,
      .mat-calendar-body-cell-content,
      .mat-calendar-arrow,
      .mat-datepicker-toggle {
          color: var(--text-primary) !important;
      }
       .mat-calendar-body-selected {
          background-color: var(--primary) !important;
          color: var(--btn-primary-text) !important;
      }
       .mat-calendar-body-today:not(.mat-calendar-body-selected) {
          border-color: var(--text-secondary) !important;
      }
       /* Datepicker toggle icon color */
       .mat-datepicker-toggle-default-icon {
           fill: var(--text-primary) !important;
       }
       /* Datepicker input icon color */
        .mat-mdc-form-field-icon-suffix .mat-icon {
             color: var(--text-primary) !important;
        }

     /* Adjust button colors if default theme is not sufficient */
     /* .mat-mdc-button.mat-primary .mdc-button__label {} - Boş kural kaldırıldı */
     .mat-mdc-button.mat-stroked-button .mdc-button__label {
        color: var(--text-primary); /* Ensure stroked button text is visible */
     }
      .mat-mdc-button.mat-stroked-button {
         border-color: var(--border-color); /* Adjust border color */
      }

    /* --- Mat Autocomplete Dark Mode Styles --- */
    .mat-autocomplete-panel { /* Hedef panel */
      background-color: var(--bg-tertiary) !important; /* Select panel ile aynı arka plan */
    }

    .mat-autocomplete-panel .mat-mdc-option { /* Hedef seçenekler (MDC stili) */
      color: var(--text-primary) !important; /* Ana metin rengi */
    }

    /* Seçili seçenek metin rengi */
    .mat-autocomplete-panel .mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-option-multiple) {
       color: var(--primary-color) !important; /* Ana tema rengi */
       background-color: var(--bg-tertiary) !important; /* Arka planı değiştirmeyelim */
    }

     /* Üzerine gelinen/odaklanılan seçenek arka planı */
     .mat-autocomplete-panel .mat-mdc-option:hover:not(.mdc-list-item--disabled),
     .mat-autocomplete-panel .mat-mdc-option.mat-active:not(.mdc-list-item--disabled) { /* .mat-active ekledik */
       background-color: rgba(var(--primary-rgb), 0.1) !important; /* Select ile aynı hover */
       color: var(--text-primary) !important; /* Hover'da metin rengi değişmesin */
     }
    /* --- End of Mat Autocomplete Styles --- */

    /* --- Dark Mode Styles for Mat Table --- */
    .mat-mdc-table { /* Ana tablo konteyneri */
       background-color: var(--card-bg-color) !important;
       color: var(--text-color) !important;
    }
    .mat-mdc-header-row { /* Başlık satırı */
       background-color: var(--bg-secondary) !important; /* Başlık için biraz farklı arka plan */
    }
    .mat-mdc-header-cell { /* Başlık hücreleri */
       color: var(--text-muted) !important; /* Başlık metni */
       border-bottom-color: var(--border-color) !important;
    }
    .mat-mdc-row { /* Normal veri satırları */
       border-bottom-color: var(--border-color) !important;
    }
     .mat-mdc-row:hover { /* Satır üzerine gelince */
        background-color: var(--bg-tertiary) !important;
     }
    .mat-mdc-cell { /* Normal veri hücreleri */
       color: var(--text-color) !important;
       border-bottom-color: var(--border-color) !important;
    }
    /* --- End of Mat Table Styles --- */

    /* Ensure .text-muted uses the dark theme variable */
    .text-muted {
       color: var(--text-muted) !important;
    }

    /* Ensure card header titles are visible in dark mode */
    .card-header h1,
    .card-header h2,
    .card-header h3,
    .card-header h4,
    .card-header h5,
    .card-header h6,
    .card-header .title { /* .title sınıfını da hedefleyelim */
       color: var(--text-color) !important;
    }

    /* --- End of Moved Styles --- */
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Responsive Breakpoints */
/* Extra small devices (phones) */
@media (max-width: 575.98px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }

    /* Responsive form adjustments */
    .form-row {
        flex-direction: column;
    }

    .form-group {
        width: 100% !important;
        margin-right: 0 !important;
    }

    /* Card adjustments */
    .card-header, .card-body, .card-footer {
        padding: 15px;
    }

    /* Button adjustments */
    .btn {
        padding: 8px 15px;
        font-size: 14px;
    }

    /* Table adjustments */
    .table-responsive {
        overflow-x: auto;
    }

    /* Dialog adjustments */
    .mat-mdc-dialog-container .mdc-dialog__surface {
        width: 95% !important;
        max-width: 95vw !important;
    }

    /* Member Detail Dialog Mobile Adjustments */
    .member-detail-dialog-container {
        padding: 10px !important;
    }

    .member-detail-dialog-container .mat-mdc-dialog-container {
        max-width: 95vw !important;
    }
}

/* Small devices (tablets) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    /* Card adjustments */
    .card-header, .card-body, .card-footer {
        padding: 18px;
    }

    /* Dialog adjustments */
    .mat-mdc-dialog-container .mdc-dialog__surface {
        width: 90% !important;
        max-width: 90vw !important;
    }

    /* Member Detail Dialog Tablet Adjustments */
    .member-detail-dialog-container {
        padding: 15px !important;
    }

    .member-detail-dialog-container .mat-mdc-dialog-container {
        max-width: 90vw !important;
    }
}

/* Medium devices (desktops) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .container {
        padding-left: 20px;
        padding-right: 20px;
    }

    /* Dialog adjustments */
    .mat-mdc-dialog-container .mdc-dialog__surface {
        width: 80% !important;
        max-width: 80vw !important;
    }

    /* Member Detail Dialog Medium Adjustments */
    .member-detail-dialog-container .mat-mdc-dialog-container {
        max-width: 85vw !important;
    }
}

/* Large devices (large desktops) */
@media (min-width: 992px) {
    .container {
        padding-left: 25px;
        padding-right: 25px;
    }
}

/* Dialog Styles */
.custom-dialog-container {
    display: flex !important;
    justify-content: center !important;
}

.custom-dialog-container .mat-dialog-container {
    padding: 0 !important;
    overflow: hidden !important;
    border-radius: 8px !important;
}

/* Member Detail Dialog Styles */
.member-detail-dialog-container {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 20px !important;
}

.member-detail-dialog-container .mat-mdc-dialog-container {
    padding: 0 !important;
    overflow: hidden !important;
    border-radius: 12px !important;
    background-color: var(--card-bg-color) !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
    margin: 0 !important;
    width: 100% !important;
    max-width: 1000px !important;
}

.member-detail-dialog-container .mdc-dialog__surface {
    background-color: var(--card-bg-color) !important;
    color: var(--text-color) !important;
    border-radius: 12px !important;
    width: 100% !important;
    margin: 0 !important;
}

/* Common Utility Classes */
.card {
    background-color: var(--card-bg-color);
    border-radius: 8px;
    box-shadow: 0 4px 6px var(--shadow-color);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.btn {
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--btn-primary-bg);
    border-color: var(--btn-primary-bg);
    color: var(--btn-primary-text);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

input, select, textarea {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--input-text);
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    background-color: var(--input-bg);
    color: var(--input-text);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}
