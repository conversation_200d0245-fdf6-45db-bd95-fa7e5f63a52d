{"version": 3, "sources": ["src/app/components/crud/company-unified-add/company-unified-add.component.css"], "sourcesContent": ["/* Blur effect for loading state */\r\n.content-blur {\r\n  filter: blur(3px);\r\n  pointer-events: none;\r\n}\r\n\r\n/* Full width form fields */\r\nmat-form-field {\r\n  width: 100%;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n/* Section styling */\r\n.form-section {\r\n  margin-bottom: 1.5rem;\r\n  padding: 1.25rem;\r\n  background-color: var(--bg-secondary);\r\n  border-radius: var(--border-radius-md);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.form-section:hover {\r\n  background-color: var(--bg-tertiary);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.form-section-title {\r\n  font-weight: 600;\r\n  margin-bottom: 1rem;\r\n  color: var(--primary);\r\n  border-bottom: 2px solid var(--primary-light);\r\n  padding-bottom: 0.5rem;\r\n}\r\n\r\n/* Table styling */\r\n.table {\r\n  width: 100%;\r\n  margin-bottom: 1rem;\r\n  color: var(--text-primary);\r\n}\r\n\r\n.table th,\r\n.table td {\r\n  padding: 0.75rem;\r\n  vertical-align: middle;\r\n  border-top: 1px solid var(--border-color);\r\n}\r\n\r\n.table thead th {\r\n  vertical-align: bottom;\r\n  border-bottom: 2px solid var(--border-color);\r\n  background-color: var(--bg-secondary);\r\n}\r\n\r\n.table tbody tr:hover {\r\n  background-color: var(--primary-light);\r\n}\r\n\r\n/* Button spacing */\r\n.btn {\r\n  margin-right: 0.5rem;\r\n}\r\n\r\n.btn:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n/* Card styling */\r\n.card {\r\n  margin-bottom: 1.5rem;\r\n  border: none;\r\n  border-radius: var(--border-radius-lg);\r\n  box-shadow: var(--shadow-sm);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.card:hover {\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.card-header {\r\n  background-color: var(--bg-secondary);\r\n  border-bottom: 1px solid var(--border-color);\r\n  padding: 1rem 1.25rem;\r\n}\r\n\r\n.card-body {\r\n  padding: 1.25rem;\r\n}\r\n\r\n/* Responsive table */\r\n@media (max-width: 767.98px) {\r\n  .table-responsive {\r\n    display: block;\r\n    width: 100%;\r\n    overflow-x: auto;\r\n    -webkit-overflow-scrolling: touch;\r\n  }\r\n}\r\n"], "mappings": ";AACA,CAAC;AACC,UAAQ,KAAK;AACb,kBAAgB;AAClB;AAGA;AACE,SAAO;AACP,iBAAe;AACjB;AAGA,CAAC;AACC,iBAAe;AACf,WAAS;AACT,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY,IAAI,KAAK;AACvB;AAEA,CARC,YAQY;AACX,oBAAkB,IAAI;AACtB,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,eAAa;AACb,iBAAe;AACf,SAAO,IAAI;AACX,iBAAe,IAAI,MAAM,IAAI;AAC7B,kBAAgB;AAClB;AAGA,CAAC;AACC,SAAO;AACP,iBAAe;AACf,SAAO,IAAI;AACb;AAEA,CANC,MAMM;AACP,CAPC,MAOM;AACL,WAAS;AACT,kBAAgB;AAChB,cAAY,IAAI,MAAM,IAAI;AAC5B;AAEA,CAbC,MAaM,MAAM;AACX,kBAAgB;AAChB,iBAAe,IAAI,MAAM,IAAI;AAC7B,oBAAkB,IAAI;AACxB;AAEA,CAnBC,MAmBM,MAAM,EAAE;AACb,oBAAkB,IAAI;AACxB;AAGA,CAAC;AACC,gBAAc;AAChB;AAEA,CAJC,GAIG;AACF,gBAAc;AAChB;AAGA,CAAC;AACC,iBAAe;AACf,UAAQ;AACR,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,cAAY,IAAI,KAAK;AACvB;AAEA,CARC,IAQI;AACH,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,iBAAe,IAAI,MAAM,IAAI;AAC7B,WAAS,KAAK;AAChB;AAEA,CAAC;AACC,WAAS;AACX;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAAC;AACC,aAAS;AACT,WAAO;AACP,gBAAY;AACZ,gCAA4B;AAC9B;AACF;", "names": []}