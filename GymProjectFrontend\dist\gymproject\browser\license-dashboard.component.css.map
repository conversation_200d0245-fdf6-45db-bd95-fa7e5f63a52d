{"version": 3, "sources": ["src/app/components/license-dashboard/license-dashboard.component.css"], "sourcesContent": ["/* license-dashboard.component.css */\r\n.card {\r\n    border-radius: 8px;\r\n    box-shadow: 0 4px 6px var(--shadow-color); /* Değişken kullanıldı */\r\n    margin-bottom: 20px;\r\n    background-color: var(--card-bg-color); /* Arka plan rengi eklendi */\r\n    color: var(--text-color); /* Metin rengi eklendi */\r\n    border: 1px solid var(--border-color); /* Kenarlık eklendi */\r\n  }\r\n  \r\n  .card-header {\r\n    border-bottom: 1px solid var(--border-color); /* Değişken kullanıldı */\r\n    background-color: var(--card-bg-color); /* Değişken kullanıldı */\r\n    color: var(--text-color); /* Metin rengi eklendi */\r\n    font-weight: 500;\r\n    padding: 0.75rem 1.25rem; /* Padding eklendi (Bootstrap benzeri) */\r\n  }\r\n  \r\n  .btn-block {\r\n    width: 100%;\r\n    margin-bottom: 10px;\r\n  }\r\n  \r\n  /* .table th stilleri global stiller tarafından kapsandığı için kaldırıldı */\r\n  \r\n  .bg-primary, .bg-warning, .bg-success, .bg-info {\r\n    position: relative;\r\n    overflow: hidden;\r\n  }\r\n  \r\n  .opacity-50 {\r\n    opacity: 0.5;\r\n  }\r\n  \r\n  /* license-packages-list.component.css */\r\n  /* .mat-mdc-row:hover stili global stiller tarafından kapsandığı için kaldırıldı */\r\n  \r\n  /* .card stili yukarıda zaten güncellendi, bu tekrarı kaldırabiliriz. */\r\n  \r\n  .table-responsive {\r\n    overflow-x: auto;\r\n  }\r\n  \r\n  /* license-package-add-edit.component.css */\r\n  mat-form-field {\r\n    width: 100%;\r\n    margin-bottom: 15px;\r\n  }\r\n  \r\n  .spinner {\r\n    margin: 0 auto;\r\n    width: 70px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .spinner > div {\r\n    width: 12px;\r\n    height: 12px;\r\n    background-color: var(--text-color); /* Spinner rengi için değişken */\r\n    border-radius: 100%;\r\n    display: inline-block;\r\n    animation: sk-bouncedelay 1.4s infinite ease-in-out both;\r\n  }\r\n  \r\n  .spinner .bounce1 {\r\n    animation-delay: -0.32s;\r\n  }\r\n  \r\n  .spinner .bounce2 {\r\n    animation-delay: -0.16s;\r\n  }\r\n  \r\n  @keyframes sk-bouncedelay {\r\n    0%, 80%, 100% { \r\n      transform: scale(0);\r\n    } 40% { \r\n      transform: scale(1.0);\r\n    }\r\n  }\r\n  \r\n  /* user-licenses-list.component.css */\r\n  /* .text-danger, .text-warning, .text-success renk tanımları kaldırıldı. */\r\n  /* Global değişken tanımlarına ve potansiyel global utility sınıflarına güveniliyor. */\r\n  \r\n  .actions-column {\r\n    width: 120px;\r\n  }\r\n  \r\n  /* license-purchase.component.css */\r\n  .user-info {\r\n    background-color: var(--card-bg-color); /* Değişken kullanıldı */\r\n    color: var(--text-color); /* Metin rengi eklendi */\r\n    padding: 15px;\r\n    border-radius: 5px;\r\n    margin-bottom: 20px;\r\n    border: 1px solid var(--border-color); /* Kenarlık eklendi */\r\n  }\r\n  \r\n  .user-info h4 {\r\n    margin-top: 0;\r\n    margin-bottom: 5px;\r\n  }\r\n  \r\n  /* license-transactions.component.css */\r\n  .filter-section {\r\n    padding: 15px;\r\n    background-color: var(--card-bg-color); /* Değişken kullanıldı */\r\n    color: var(--text-color); /* Metin rengi eklendi */\r\n    border-radius: 5px;\r\n    margin-bottom: 20px;\r\n    border: 1px solid var(--border-color); /* Kenarlık eklendi */\r\n  }\r\n  \r\n  .filter-button {\r\n    margin-top: 32px;\r\n  }"], "mappings": ";AACA,CAAC;AACG,iBAAe;AACf,cAAY,EAAE,IAAI,IAAI,IAAI;AAC1B,iBAAe;AACf,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,iBAAe,IAAI,MAAM,IAAI;AAC7B,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,eAAa;AACb,WAAS,QAAQ;AACnB;AAEA,CAAC;AACC,SAAO;AACP,iBAAe;AACjB;AAIA,CAAC;AAAY,CAAC;AAAY,CAAC;AAAY,CAAC;AACtC,YAAU;AACV,YAAU;AACZ;AAEA,CAAC;AACC,WAAS;AACX;AAOA,CAAC;AACC,cAAY;AACd;AAGA;AACE,SAAO;AACP,iBAAe;AACjB;AAEA,CAAC;AACC,UAAQ,EAAE;AACV,SAAO;AACP,cAAY;AACd;AAEA,CANC,QAMQ,EAAE;AACT,SAAO;AACP,UAAQ;AACR,oBAAkB,IAAI;AACtB,iBAAe;AACf,WAAS;AACT,aAAW,eAAe,KAAK,SAAS,YAAY;AACtD;AAEA,CAfC,QAeQ,CAAC;AACR,mBAAiB;AACnB;AAEA,CAnBC,QAmBQ,CAAC;AACR,mBAAiB;AACnB;AAEA,WAXa;AAYX;AACE,eAAW,MAAM;AACnB;AAAE;AACA,eAAW,MAAM;AACnB;AACF;AAMA,CAAC;AACC,SAAO;AACT;AAGA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,WAAS;AACT,iBAAe;AACf,iBAAe;AACf,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CATC,UASU;AACT,cAAY;AACZ,iBAAe;AACjB;AAGA,CAAC;AACC,WAAS;AACT,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,iBAAe;AACf,iBAAe;AACf,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,cAAY;AACd;", "names": []}