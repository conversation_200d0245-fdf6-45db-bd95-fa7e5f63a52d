{"version": 3, "sources": ["angular:styles/component:css;1457aaf2e7924d236ace36a7d1579d7a41640bf925a942358e41497690f10418;C:/Users/<USER>/Desktop/GymProject/GymProjectFrontend/src/app/components/freeze-membership-dialog/freeze-membership-dialog.component.ts"], "sourcesContent": ["\n    .modern-dialog {\n      min-width: 320px;\n      max-width: 100%;\n      overflow: hidden;\n      border-radius: var(--border-radius-lg);\n      box-shadow: var(--shadow-md);\n      background-color: var(--bg-primary);\n      color: var(--text-primary);\n    }\n    \n    .modern-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: var(--spacing-md) var(--spacing-lg);\n      border-bottom: 1px solid var(--border-color);\n      background-color: var(--info-light);\n      color: var(--text-primary);\n    }\n    \n    .modern-card-header h2 {\n      margin: 0;\n      font-size: 1.25rem;\n      font-weight: 600;\n      color: var(--text-primary);\n    }\n    \n    .modern-card-body {\n      padding: var(--spacing-lg);\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      text-align: center;\n    }\n    \n    .dialog-icon {\n      width: 64px;\n      height: 64px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: var(--spacing-md);\n      font-size: 1.75rem;\n      background-color: var(--info-light);\n      color: var(--info);\n    }\n    \n    .member-info {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      margin-bottom: var(--spacing-md);\n    }\n    \n    .modern-avatar {\n      width: 48px;\n      height: 48px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-weight: 600;\n      margin-bottom: var(--spacing-xs);\n    }\n    \n    .member-name {\n      font-weight: 600;\n      color: var(--text-primary);\n    }\n    \n    .dialog-message {\n      font-size: 1rem;\n      margin: 0 0 var(--spacing-md);\n      line-height: 1.5;\n      max-width: 400px;\n      color: var(--text-primary);\n    }\n    \n    .modern-card-footer {\n      padding: var(--spacing-md) var(--spacing-lg);\n      border-top: 1px solid var(--border-color);\n      background-color: var(--bg-secondary);\n      display: flex;\n      justify-content: flex-end;\n      gap: var(--spacing-sm);\n    }\n    \n    .modern-btn-icon {\n      background: none;\n      border: none;\n      cursor: pointer;\n      color: var(--text-secondary);\n      font-size: 1rem;\n      padding: 0.25rem;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      transition: color 0.3s ease;\n    }\n    \n    .modern-btn-icon:hover {\n      color: var(--text-primary);\n    }\n    \n    .modern-form-group {\n      width: 100%;\n      margin-bottom: var(--spacing-md);\n    }\n    \n    .modern-form-label {\n      display: block;\n      margin-bottom: var(--spacing-xs);\n      font-weight: 500;\n      text-align: left;\n    }\n    \n    .input-group {\n      display: flex;\n    }\n    \n    .input-group-text {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: 40px;\n      height: 40px;\n      background-color: var(--info-light);\n      color: var(--info);\n      border: 1px solid var(--border-color);\n      border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);\n    }\n    \n    .modern-form-control {\n      flex: 1;\n      padding: 0.5rem 0.75rem;\n      border: 1px solid var(--border-color);\n      border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;\n      background-color: var(--bg-primary);\n      color: var(--text-primary);\n      transition: border-color 0.3s ease, box-shadow 0.3s ease;\n    }\n    \n    .modern-form-control:focus {\n      border-color: var(--info);\n      outline: none;\n      box-shadow: 0 0 0 0.2rem var(--info-light);\n    }\n    \n    .error-message {\n      color: var(--danger);\n      font-size: 0.875rem;\n      margin-top: 0.25rem;\n      text-align: left;\n    }\n    \n    .freeze-info {\n      width: 100%;\n      margin-top: var(--spacing-md);\n      padding: var(--spacing-sm);\n      background-color: var(--bg-secondary);\n      border-radius: var(--border-radius-md);\n      text-align: left;\n    }\n    \n    .freeze-info-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: var(--spacing-xs);\n      font-size: 0.875rem;\n      color: var(--text-primary);\n    }\n    \n    .freeze-info-item:last-child {\n      margin-bottom: 0;\n    }\n    \n    .freeze-info-item i {\n      margin-right: var(--spacing-xs);\n      color: var(--info);\n    }\n    \n    /* Dark mode specific styles */\n    [data-theme=\"dark\"] .freeze-dialog {\n      background-color: var(--bg-secondary);\n      color: var(--text-primary);\n    }\n    \n    [data-theme=\"dark\"] .modern-card-header {\n      background-color: rgba(100, 181, 246, 0.2);\n      color: var(--text-primary);\n    }\n    \n    [data-theme=\"dark\"] .dialog-message,\n    [data-theme=\"dark\"] .member-name,\n    [data-theme=\"dark\"] .modern-form-label {\n      color: var(--text-primary);\n    }\n    \n    [data-theme=\"dark\"] .freeze-info {\n      background-color: var(--bg-tertiary);\n      border: 1px solid var(--border-color);\n    }\n    \n    [data-theme=\"dark\"] .freeze-info-item {\n      color: var(--text-primary);\n    }\n    \n    [data-theme=\"dark\"] .modern-card-footer {\n      background-color: var(--bg-tertiary);\n    }\n    \n    @media screen and (max-width: 480px) {\n      .modern-dialog {\n        min-width: 280px;\n      }\n      \n      .modern-card-header {\n        padding: var(--spacing-sm) var(--spacing-md);\n      }\n      \n      .modern-card-body {\n        padding: var(--spacing-md);\n      }\n      \n      .modern-card-footer {\n        padding: var(--spacing-sm) var(--spacing-md);\n      }\n      \n      .dialog-icon {\n        width: 48px;\n        height: 48px;\n        font-size: 1.25rem;\n      }\n    }\n  "], "mappings": ";AACI,CAAC;AACC,aAAW;AACX,aAAW;AACX,YAAU;AACV,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS,IAAI,cAAc,IAAI;AAC/B,iBAAe,IAAI,MAAM,IAAI;AAC7B,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAVC,mBAUmB;AAClB,UAAQ;AACR,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS,IAAI;AACb,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,cAAY;AACd;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,iBAAe,IAAI;AACnB,aAAW;AACX,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,eAAa;AACb,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAAC;AACC,aAAW;AACX,UAAQ,EAAE,EAAE,IAAI;AAChB,eAAa;AACb,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS,IAAI,cAAc,IAAI;AAC/B,cAAY,IAAI,MAAM,IAAI;AAC1B,oBAAkB,IAAI;AACtB,WAAS;AACT,mBAAiB;AACjB,OAAK,IAAI;AACX;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,UAAQ;AACR,SAAO,IAAI;AACX,aAAW;AACX,WAAS;AACT,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,cAAY,MAAM,KAAK;AACzB;AAEA,CAbC,eAae;AACd,SAAO,IAAI;AACb;AAEA,CAAC;AACC,SAAO;AACP,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,WAAS;AACT,iBAAe,IAAI;AACnB,eAAa;AACb,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACX;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,UAAQ;AACR,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI,oBAAoB,EAAE,EAAE,IAAI;AACjD;AAEA,CAAC;AACC,QAAM;AACN,WAAS,OAAO;AAChB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,EAAE,IAAI,oBAAoB,IAAI,oBAAoB;AACjE,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,cAAY,aAAa,KAAK,IAAI,EAAE,WAAW,KAAK;AACtD;AAEA,CAVC,mBAUmB;AAClB,gBAAc,IAAI;AAClB,WAAS;AACT,cAAY,EAAE,EAAE,EAAE,OAAO,IAAI;AAC/B;AAEA,CAAC;AACC,SAAO,IAAI;AACX,aAAW;AACX,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,SAAO;AACP,cAAY,IAAI;AAChB,WAAS,IAAI;AACb,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,iBAAe,IAAI;AACnB,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CARC,gBAQgB;AACf,iBAAe;AACjB;AAEA,CAZC,iBAYiB;AAChB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAGA,CAAC,iBAAmB,CAAC;AACnB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAnLnB;AAoLC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA1HnB;AA2HD,CAAC,iBAAmB,CAhInB;AAiID,CAAC,iBAAmB,CArFnB;AAsFC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA3CnB;AA4CC,oBAAkB,IAAI;AACtB,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC,iBAAmB,CAvCnB;AAwCC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAjInB;AAkIC,oBAAkB,IAAI;AACxB;AAEA,OAAO,OAAO,IAAI,CAAC,SAAS,EAAE;AAC5B,GAtND;AAuNG,eAAW;AACb;AAEA,GAhND;AAiNG,aAAS,IAAI,cAAc,IAAI;AACjC;AAEA,GAnMD;AAoMG,aAAS,IAAI;AACf;AAEA,GAlJD;AAmJG,aAAS,IAAI,cAAc,IAAI;AACjC;AAEA,GAnMD;AAoMG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AACF;", "names": []}