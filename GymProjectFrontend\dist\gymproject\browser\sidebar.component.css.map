{"version": 3, "sources": ["src/app/components/navi/sidebar.component.css"], "sourcesContent": ["/* Sidebar Container */\n.sidebar {\n  width: 280px;\n  height: 100%;\n  background-color: var(--sidebar-bg);\n  color: var(--text-primary);\n  display: flex;\n  flex-direction: column;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: 0 0 20px var(--shadow-color);\n  overflow-x: hidden;\n  overflow-y: auto;\n  position: relative;\n  z-index: 1000;\n}\n\n.sidebar.collapsed {\n  width: 80px;\n}\n\n/* Sidebar Header */\n.sidebar-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20px;\n  border-bottom: 1px solid var(--border-color);\n  height: 80px;\n}\n\n.logo-container {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.logo-container i {\n  font-size: 24px;\n  color: var(--primary-color);\n  transition: transform 0.3s ease;\n}\n\n.logo-container:hover i {\n  transform: scale(1.1);\n}\n\n.logo-text {\n  font-size: 20px;\n  font-weight: 700;\n  color: var(--text-primary);\n  letter-spacing: 0.5px;\n  transition: opacity 0.3s ease;\n}\n\n.sidebar.collapsed .logo-text {\n  opacity: 0;\n}\n\n.toggle-btn {\n  background: none;\n  border: none;\n  color: var(--text-muted);\n  font-size: 18px;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.toggle-btn:hover {\n  background-color: var(--sidebar-hover);\n  color: var(--primary-color);\n  transform: scale(1.1);\n}\n\n/* Sidebar Content */\n.sidebar-content {\n  flex: 1;\n  padding: 20px 0;\n  overflow-y: auto;\n}\n\n/* Menu Sections */\n.menu-section {\n  margin-bottom: 20px;\n}\n\n.menu-header {\n  display: flex;\n  align-items: center;\n  padding: 12px 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border-radius: 10px;\n  margin: 0 10px;\n  position: relative;\n}\n\n.menu-header:hover {\n  background-color: var(--sidebar-hover);\n  transform: translateX(5px);\n}\n\n.sidebar.collapsed .menu-header {\n  justify-content: center;\n  padding: 12px;\n  margin: 0 5px;\n}\n\n.sidebar.collapsed .menu-header:hover {\n  transform: scale(1.05);\n}\n\n.menu-icon {\n  width: 42px;\n  height: 42px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 10px;\n  background: linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, rgba(63, 55, 201, 0.1) 100%);\n  margin-right: 12px;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\n}\n\n.menu-header:hover .menu-icon {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);\n}\n\n.sidebar.collapsed .menu-icon {\n  margin-right: 0;\n  width: 38px;\n  height: 38px;\n}\n\n.menu-icon i {\n  font-size: 18px;\n  color: var(--primary-color);\n  transition: transform 0.3s ease;\n}\n\n.menu-header:hover .menu-icon i {\n  transform: scale(1.1);\n}\n\n.menu-title {\n  flex: 1;\n  font-weight: 600;\n  font-size: 15px;\n  letter-spacing: 0.3px;\n}\n\n.menu-arrow {\n  transition: transform 0.3s ease;\n  color: var(--text-muted);\n  font-size: 12px;\n}\n\n.menu-arrow.rotated {\n  transform: rotate(180deg);\n}\n\n/* Menu Items */\n.menu-items {\n  max-height: 0;\n  overflow: hidden;\n  transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);\n}\n\n.menu-items.expanded {\n  max-height: 2000px; /* Increased for more items */\n  transition: max-height 1s ease-in-out;\n}\n\n.sidebar.collapsed .menu-items {\n  max-height: 2000px;\n}\n\n.menu-item {\n  display: flex;\n  align-items: center;\n  padding: 10px 20px 10px 10px;\n\n  color: var(--text-primary);\n  text-decoration: none;\n  transition: all 0.3s ease;\n  white-space: nowrap;\n  border-radius: 8px;\n  margin: 4px 8px;\n  position: relative;\n}\n\n.menu-item::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 100%;\n  width: 0;\n  background: linear-gradient(90deg, var(--primary-color), transparent);\n  opacity: 0;\n  transition: all 0.3s ease;\n  border-radius: 8px;\n}\n\n.sidebar.collapsed .menu-item {\n  padding: 10px;\n  justify-content: center;\n  margin: 4px;\n}\n\n.menu-item:hover {\n  background-color: var(--sidebar-hover);\n  color: var(--text-color);\n  transform: translateX(5px);\n}\n\n.sidebar.collapsed .menu-item:hover {\n  transform: scale(1.05);\n}\n\n.menu-item.active {\n  background-color: var(--sidebar-active);\n  color: var(--primary-color);\n  font-weight: 600;\n}\n\n.menu-item.active::before {\n  width: 4px;\n  opacity: 1;\n}\n\n.menu-item i {\n  font-size: 16px;\n  width: 20px;\n  text-align: center;\n  margin-right: 12px;\n  position: relative;\n  z-index: 1;\n}\n\n.sidebar.collapsed .menu-item i {\n  margin-right: 0;\n  font-size: 18px;\n}\n\n/* Sidebar Footer */\n.sidebar-footer {\n  padding: 20px;\n  border-top: 1px solid var(--border-color);\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.user-profile-container {\n  margin-bottom: 10px;\n}\n\n.profile-btn {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 15px;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-decoration: none;\n  color: var(--text-color);\n  background: linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, rgba(63, 55, 201, 0.1) 100%);\n  font-weight: 500;\n  width: 100%;\n}\n\n.profile-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n  background: linear-gradient(135deg, rgba(67, 97, 238, 0.2) 0%, rgba(63, 55, 201, 0.2) 100%);\n}\n\n/* Profile Image Styles */\n.profile-image-container {\n  position: relative;\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.profile-image {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 2px solid var(--primary-color);\n  transition: all 0.3s ease;\n}\n\n.profile-image:hover {\n  transform: scale(1.1);\n  box-shadow: 0 4px 8px rgba(67, 97, 238, 0.3);\n}\n\n.profile-icon {\n  font-size: 32px;\n  color: var(--primary-color);\n  transition: all 0.3s ease;\n}\n\n.profile-icon:hover {\n  transform: scale(1.1);\n  color: var(--primary-color);\n}\n\n.sidebar.collapsed .profile-btn {\n  justify-content: center;\n  padding: 12px;\n}\n\n.sidebar.collapsed .profile-image-container {\n  width: 36px;\n  height: 36px;\n}\n\n.sidebar.collapsed .profile-image {\n  width: 36px;\n  height: 36px;\n}\n\n.sidebar.collapsed .profile-icon {\n  font-size: 36px;\n}\n\n.theme-toggle-container {\n  margin-bottom: 10px;\n}\n\n.theme-toggle-btn, .logout-btn {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 15px;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-decoration: none;\n  color: var(--text-color);\n  background: transparent;\n  border: none;\n  font-weight: 500;\n  width: 100%;\n}\n\n.theme-toggle-btn {\n  background: linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, rgba(63, 55, 201, 0.1) 100%);\n}\n\n.logout-btn {\n  background-color: rgba(220, 53, 69, 0.1);\n  color: #dc3545;\n}\n\n.theme-toggle-btn:hover, .logout-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n}\n\n.theme-toggle-btn:hover {\n  background: linear-gradient(135deg, rgba(67, 97, 238, 0.2) 0%, rgba(63, 55, 201, 0.2) 100%);\n}\n\n.logout-btn:hover {\n  background-color: rgba(220, 53, 69, 0.2);\n}\n\n.theme-toggle-btn.icon-only, .logout-btn.icon-only {\n  justify-content: center;\n  padding: 12px;\n}\n\n.theme-toggle-btn i {\n  font-size: 18px;\n  color: var(--primary-color);\n}\n\n.logout-btn i {\n  font-size: 18px;\n  color: #dc3545;\n}\n\n/* Responsive Styles */\n@media (max-width: 991.98px) {\n  .sidebar {\n    position: fixed;\n    z-index: 1000;\n    width: 260px;\n  }\n\n  .sidebar.collapsed {\n    transform: translateX(-100%);\n  }\n}\n\n/* Tooltip for collapsed sidebar */\n.sidebar.collapsed .menu-header,\n.sidebar.collapsed .menu-item {\n  position: relative;\n}\n\n.sidebar.collapsed .menu-header::after,\n.sidebar.collapsed .menu-item::after {\n  content: attr(data-title);\n  position: absolute;\n  left: 100%;\n  top: 50%;\n  transform: translateY(-50%);\n  background-color: var(--card-bg-color);\n  color: var(--text-color);\n  padding: 5px 10px;\n  border-radius: 5px;\n  white-space: nowrap;\n  box-shadow: 0 2px 5px var(--shadow-color);\n  opacity: 0;\n  pointer-events: none;\n  transition: opacity 0.3s ease, transform 0.3s ease;\n  z-index: 1001;\n}\n\n.sidebar.collapsed .menu-header:hover::after,\n.sidebar.collapsed .menu-item:hover::after {\n  opacity: 1;\n  transform: translateY(-50%) translateX(10px);\n}\n\n/* Logo Link Styling */\n.logo-link {\n  text-decoration: none;\n  color: inherit;\n  display: inline-block;\n  cursor: pointer;\n}\n\n.logo-link:hover,\n.logo-link:focus {\n  text-decoration: none;\n  color: inherit;\n}\n"], "mappings": ";AACA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,WAAS;AACT,kBAAgB;AAChB,cAAY,IAAI,KAAK,aAAa,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;AAC/C,cAAY,EAAE,EAAE,KAAK,IAAI;AACzB,cAAY;AACZ,cAAY;AACZ,YAAU;AACV,WAAS;AACX;AAEA,CAfC,OAeO,CAAC;AACP,SAAO;AACT;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,WAAS;AACT,iBAAe,IAAI,MAAM,IAAI;AAC7B,UAAQ;AACV;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CANC,eAMe;AACd,aAAW;AACX,SAAO,IAAI;AACX,cAAY,UAAU,KAAK;AAC7B;AAEA,CAZC,cAYc,OAAO;AACpB,aAAW,MAAM;AACnB;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACX,kBAAgB;AAChB,cAAY,QAAQ,KAAK;AAC3B;AAEA,CArDC,OAqDO,CAtCC,UAsCU,CARlB;AASC,WAAS;AACX;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,SAAO,IAAI;AACX,aAAW;AACX,UAAQ;AACR,WAAS;AACT,iBAAe;AACf,cAAY,IAAI,KAAK;AACvB;AAEA,CAXC,UAWU;AACT,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,aAAW,MAAM;AACnB;AAGA,CAAC;AACC,QAAM;AACN,WAAS,KAAK;AACd,cAAY;AACd;AAGA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS,KAAK;AACd,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,iBAAe;AACf,UAAQ,EAAE;AACV,YAAU;AACZ;AAEA,CAXC,WAWW;AACV,oBAAkB,IAAI;AACtB,aAAW,WAAW;AACxB;AAEA,CAtGC,OAsGO,CAvFC,UAuFU,CAhBlB;AAiBC,mBAAiB;AACjB,WAAS;AACT,UAAQ,EAAE;AACZ;AAEA,CA5GC,OA4GO,CA7FC,UA6FU,CAtBlB,WAsB8B;AAC7B,aAAW,MAAM;AACnB;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,iBAAe;AACf;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,EAA/C;AAAA,MAAmD,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK;AACtF,gBAAc;AACd,cAAY,IAAI,KAAK;AACrB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAvCC,WAuCW,OAAO,CAblB;AAcC,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CAlIC,OAkIO,CAnHC,UAmHU,CAlBlB;AAmBC,gBAAc;AACd,SAAO;AACP,UAAQ;AACV;AAEA,CAxBC,UAwBU;AACT,aAAW;AACX,SAAO,IAAI;AACX,cAAY,UAAU,KAAK;AAC7B;AAEA,CAxDC,WAwDW,OAAO,CA9BlB,UA8B6B;AAC5B,aAAW,MAAM;AACnB;AAEA,CAAC;AACC,QAAM;AACN,eAAa;AACb,aAAW;AACX,kBAAgB;AAClB;AAEA,CAAC;AACC,cAAY,UAAU,KAAK;AAC3B,SAAO,IAAI;AACX,aAAW;AACb;AAEA,CANC,UAMU,CAAC;AACV,aAAW,OAAO;AACpB;AAGA,CAAC;AACC,cAAY;AACZ,YAAU;AACV,cAAY,WAAW,KAAK,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpD;AAEA,CANC,UAMU,CAAC;AACV,cAAY;AACZ,cAAY,WAAW,GAAG;AAC5B;AAEA,CA/KC,OA+KO,CAhKC,UAgKU,CAXlB;AAYC,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS,KAAK,KAAK,KAAK;AAExB,SAAO,IAAI;AACX,mBAAiB;AACjB,cAAY,IAAI,KAAK;AACrB,eAAa;AACb,iBAAe;AACf,UAAQ,IAAI;AACZ,YAAU;AACZ;AAEA,CAdC,SAcS;AACR,WAAS;AACT,YAAU;AACV,QAAM;AACN,OAAK;AACL,UAAQ;AACR,SAAO;AACP;AAAA,IAAY;AAAA,MAAgB,KAAK;AAAA,MAAE,IAAI,gBAAgB;AAAA,MAAE;AACzD,WAAS;AACT,cAAY,IAAI,KAAK;AACrB,iBAAe;AACjB;AAEA,CA9MC,OA8MO,CA/LC,UA+LU,CA3BlB;AA4BC,WAAS;AACT,mBAAiB;AACjB,UAAQ;AACV;AAEA,CAjCC,SAiCS;AACR,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,aAAW,WAAW;AACxB;AAEA,CA1NC,OA0NO,CA3MC,UA2MU,CAvClB,SAuC4B;AAC3B,aAAW,MAAM;AACnB;AAEA,CA3CC,SA2CS,CAAC;AACT,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,eAAa;AACf;AAEA,CAjDC,SAiDS,CANC,MAMM;AACf,SAAO;AACP,WAAS;AACX;AAEA,CAtDC,UAsDU;AACT,aAAW;AACX,SAAO;AACP,cAAY;AACZ,gBAAc;AACd,YAAU;AACV,WAAS;AACX;AAEA,CAlPC,OAkPO,CAnOC,UAmOU,CA/DlB,UA+D6B;AAC5B,gBAAc;AACd,aAAW;AACb;AAGA,CAAC;AACC,WAAS;AACT,cAAY,IAAI,MAAM,IAAI;AAC1B,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,WAAS,KAAK;AACd,iBAAe;AACf,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,mBAAiB;AACjB,SAAO,IAAI;AACX;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,EAA/C;AAAA,MAAmD,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK;AACtF,eAAa;AACb,SAAO;AACT;AAEA,CAfC,WAeW;AACV,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,EAA/C;AAAA,MAAmD,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK;AACxF;AAGA,CAAC;AACC,YAAU;AACV,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACnB;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,cAAY;AACZ,UAAQ,IAAI,MAAM,IAAI;AACtB,cAAY,IAAI,KAAK;AACvB;AAEA,CATC,aASa;AACZ,aAAW,MAAM;AACjB,cAAY,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AAC1C;AAEA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACX,cAAY,IAAI,KAAK;AACvB;AAEA,CANC,YAMY;AACX,aAAW,MAAM;AACjB,SAAO,IAAI;AACb;AAEA,CA5TC,OA4TO,CA7SC,UA6SU,CAxDlB;AAyDC,mBAAiB;AACjB,WAAS;AACX;AAEA,CAjUC,OAiUO,CAlTC,UAkTU,CAvClB;AAwCC,SAAO;AACP,UAAQ;AACV;AAEA,CAtUC,OAsUO,CAvTC,UAuTU,CAnClB;AAoCC,SAAO;AACP,UAAQ;AACV;AAEA,CA3UC,OA2UO,CA5TC,UA4TU,CA1BlB;AA2BC,aAAW;AACb;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AAAkB,CAAC;AAClB,WAAS;AACT,eAAa;AACb,OAAK;AACL,WAAS,KAAK;AACd,iBAAe;AACf,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,mBAAiB;AACjB,SAAO,IAAI;AACX,cAAY;AACZ,UAAQ;AACR,eAAa;AACb,SAAO;AACT;AAEA,CAhBC;AAiBC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,EAA/C;AAAA,MAAmD,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK;AACxF;AAEA,CApBoB;AAqBlB,oBAAkB,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AACpC,SAAO;AACT;AAEA,CAzBC,gBAyBgB;AAAQ,CAzBL,UAyBgB;AAClC,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CA9BC,gBA8BgB;AACf;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,EAA/C;AAAA,MAAmD,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK;AACxF;AAEA,CAlCoB,UAkCT;AACT,oBAAkB,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AACtC;AAEA,CAtCC,gBAsCgB,CAAC;AAAW,CAtCT,UAsCoB,CAAtB;AAChB,mBAAiB;AACjB,WAAS;AACX;AAEA,CA3CC,iBA2CiB;AAChB,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CAhDoB,WAgDR;AACV,aAAW;AACX,SAAO;AACT;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA1YD;AA2YG,cAAU;AACV,aAAS;AACT,WAAO;AACT;AAEA,GAhZD,OAgZS,CAjYD;AAkYL,eAAW,WAAW;AACxB;AACF;AAGA,CAtZC,OAsZO,CAvYC,UAuYU,CAhUlB;AAiUD,CAvZC,OAuZO,CAxYC,UAwYU,CApOlB;AAqOC,YAAU;AACZ;AAEA,CA3ZC,OA2ZO,CA5YC,UA4YU,CArUlB,WAqU8B;AAC/B,CA5ZC,OA4ZO,CA7YC,UA6YU,CAzOlB,SAyO4B;AAC3B,WAAS,KAAK;AACd,YAAU;AACV,QAAM;AACN,OAAK;AACL,aAAW,WAAW;AACtB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,WAAS,IAAI;AACb,iBAAe;AACf,eAAa;AACb,cAAY,EAAE,IAAI,IAAI,IAAI;AAC1B,WAAS;AACT,kBAAgB;AAChB,cAAY,QAAQ,KAAK,IAAI,EAAE,UAAU,KAAK;AAC9C,WAAS;AACX;AAEA,CA9aC,OA8aO,CA/ZC,UA+ZU,CAxVlB,WAwV8B,MAAM;AACrC,CA/aC,OA+aO,CAhaC,UAgaU,CA5PlB,SA4P4B,MAAM;AACjC,WAAS;AACT,aAAW,WAAW,MAAM,WAAW;AACzC;AAGA,CAAC;AACC,mBAAiB;AACjB,SAAO;AACP,WAAS;AACT,UAAQ;AACV;AAEA,CAPC,SAOS;AACV,CARC,SAQS;AACR,mBAAiB;AACjB,SAAO;AACT;", "names": []}