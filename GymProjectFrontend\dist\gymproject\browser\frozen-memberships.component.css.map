{"version": 3, "sources": ["src/app/components/frozen-memberships/frozen-memberships.component.css"], "sourcesContent": ["/* Frozen Memberships Component Styles */\r\n\r\n/* Content Blur Effect */\r\n.content-blur {\r\n  filter: blur(3px);\r\n  pointer-events: none;\r\n}\r\n\r\n/* Toggle Button */\r\n.toggle-button {\r\n  background-color: var(--primary);\r\n  color: white;\r\n  padding: 12px 24px;\r\n  transition: all 0.3s ease;\r\n  width: 100%;\r\n  max-width: 400px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 10px;\r\n  border-radius: var(--border-radius-md);\r\n  font-weight: 500;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n  border: none;\r\n}\r\n\r\n.toggle-button:hover {\r\n  background-color: var(--primary-dark);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.toggle-button:active {\r\n  transform: translateY(0);\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* Card Styles */\r\n.frozen-card, .history-card {\r\n  margin-bottom: 2rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.frozen-card:hover, .history-card:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n/* Frozen Summary */\r\n.frozen-summary {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.summary-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  background-color: rgba(0, 0, 0, 0.03);\r\n  padding: 0.75rem 1.25rem;\r\n  border-radius: var(--border-radius-md);\r\n}\r\n\r\n.summary-value {\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  color: var(--primary);\r\n}\r\n\r\n.summary-label {\r\n  font-size: 0.85rem;\r\n  color: var(--secondary);\r\n}\r\n\r\n/* Table Container */\r\n.table-container {\r\n  overflow-x: auto;\r\n  margin-bottom: 1.5rem;\r\n  border-radius: var(--border-radius-md);\r\n}\r\n\r\n/* Member Name with Avatar */\r\n.member-name {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n}\r\n\r\n/* Remaining Days Styling */\r\n.remaining-days {\r\n  font-weight: 500;\r\n}\r\n\r\n/* Action Buttons */\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n/* Modern Modal */\r\n.modern-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1050;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.modern-modal.show {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n.modern-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  z-index: -1;\r\n}\r\n\r\n.modern-modal-container {\r\n  background-color: white;\r\n  border-radius: var(--border-radius-lg);\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\r\n  width: 90%;\r\n  max-width: 900px;\r\n  max-height: 90vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  transform: translateY(20px);\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.modern-modal.show .modern-modal-container {\r\n  transform: translateY(0);\r\n}\r\n\r\n.modern-modal-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 1.25rem 1.5rem;\r\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.modern-modal-title {\r\n  font-weight: 600;\r\n  margin: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.modern-modal-close {\r\n  background: none;\r\n  border: none;\r\n  font-size: 1.25rem;\r\n  cursor: pointer;\r\n  color: var(--secondary);\r\n  transition: color 0.2s ease;\r\n}\r\n\r\n.modern-modal-close:hover {\r\n  color: var(--danger);\r\n}\r\n\r\n.modern-modal-body {\r\n  padding: 1.5rem;\r\n  overflow-y: auto;\r\n  max-height: calc(90vh - 130px);\r\n}\r\n\r\n.modern-modal-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 1.25rem 1.5rem;\r\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\r\n  gap: 0.75rem;\r\n}\r\n\r\n/* Modern Search Input */\r\n.search-container {\r\n  position: relative;\r\n  width: 300px;\r\n}\r\n\r\n.modern-search-input {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.modern-search-input input {\r\n  width: 100%;\r\n  padding: 0.75rem 1rem 0.75rem 2.5rem;\r\n  border: none;\r\n  border-radius: var(--border-radius-md);\r\n  background-color: rgba(0, 0, 0, 0.03);\r\n  transition: all 0.3s ease;\r\n  font-size: 0.95rem;\r\n}\r\n\r\n.modern-search-input input:focus {\r\n  outline: none;\r\n  background-color: rgba(0, 0, 0, 0.05);\r\n  box-shadow: 0 0 0 3px var(--primary-light);\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: 0.75rem;\r\n  color: var(--secondary);\r\n  font-size: 0.95rem;\r\n  pointer-events: none;\r\n}\r\n\r\n/* Animations */\r\n.slide-in-left {\r\n  animation: slideInLeft 0.5s forwards;\r\n}\r\n\r\n@keyframes slideInLeft {\r\n  from {\r\n    transform: translateX(-20px);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.fade-in {\r\n  animation: fadeIn 0.5s forwards;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.zoom-in {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.zoom-in:hover {\r\n  transform: scale(1.02);\r\n}\r\n\r\n/* Dark Mode Support */\r\n[data-theme=\"dark\"] .summary-item {\r\n  background-color: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-search-input input {\r\n  background-color: rgba(255, 255, 255, 0.05);\r\n  color: var(--text-primary);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-search-input input:focus {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-modal-container {\r\n  background-color: var(--background-dark);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-modal-header, \r\n[data-theme=\"dark\"] .modern-modal-footer {\r\n  border-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n/* Responsive Adjustments */\r\n@media (max-width: 767.98px) {\r\n  .modern-card-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .frozen-summary {\r\n    width: 100%;\r\n    margin-top: 1rem;\r\n  }\r\n  \r\n  .search-container {\r\n    width: 100%;\r\n    margin-top: 1rem;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .action-buttons button {\r\n    width: 100%;\r\n    margin-left: 0 !important;\r\n    margin-top: 0.5rem;\r\n  }\r\n  \r\n  .action-buttons button:first-child {\r\n    margin-top: 0;\r\n  }\r\n  \r\n  .member-name {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 0.5rem;\r\n  }\r\n  \r\n  .toggle-button {\r\n    width: 100%;\r\n    max-width: none;\r\n  }\r\n}\r\n"], "mappings": ";AAGA,CAAC;AACC,UAAQ,KAAK;AACb,kBAAgB;AAClB;AAGA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO;AACP,WAAS,KAAK;AACd,cAAY,IAAI,KAAK;AACrB,SAAO;AACP,aAAW;AACX,UAAQ,EAAE;AACV,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,OAAK;AACL,iBAAe,IAAI;AACnB,eAAa;AACb,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,UAAQ;AACV;AAEA,CAlBC,aAkBa;AACZ,oBAAkB,IAAI;AACtB,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CAxBC,aAwBa;AACZ,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAGA,CAAC;AAAa,CAAC;AACb,iBAAe;AACf,cAAY,IAAI,KAAK;AACvB;AAEA,CALC,WAKW;AAAQ,CALL,YAKkB;AAC/B,aAAW,WAAW;AACxB;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,WAAS,QAAQ;AACjB,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACb;AAGA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,iBAAe,IAAI;AACrB;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAGA,CAAC;AACC,eAAa;AACf;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,OAAK;AACP;AAGA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,WAAS;AACT,WAAS;AACT,cAAY;AACZ,cAAY,IAAI,KAAK;AACvB;AAEA,CAfC,YAeY,CAAC;AACZ,WAAS;AACT,cAAY;AACd;AAEA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,WAAS;AACX;AAEA,CAAC;AACC,oBAAkB;AAClB,iBAAe,IAAI;AACnB,cAAY,EAAE,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC,SAAO;AACP,aAAW;AACX,cAAY;AACZ,WAAS;AACT,kBAAgB;AAChB,aAAW,WAAW;AACtB,cAAY,UAAU,KAAK;AAC7B;AAEA,CA3CC,YA2CY,CA5BC,KA4BK,CAblB;AAcC,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,WAAS,QAAQ;AACjB,iBAAe,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACzC;AAEA,CAAC;AACC,eAAa;AACb,UAAQ;AACR,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,aAAW;AACX,UAAQ;AACR,SAAO,IAAI;AACX,cAAY,MAAM,KAAK;AACzB;AAEA,CATC,kBASkB;AACjB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS;AACT,cAAY;AACZ,cAAY,KAAK,KAAK,EAAE;AAC1B;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,WAAS,QAAQ;AACjB,cAAY,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,OAAK;AACP;AAGA,CAAC;AACC,YAAU;AACV,SAAO;AACT;AAEA,CAAC;AACC,YAAU;AACV,WAAS;AACT,eAAa;AACf;AAEA,CANC,oBAMoB;AACnB,SAAO;AACP,WAAS,QAAQ,KAAK,QAAQ;AAC9B,UAAQ;AACR,iBAAe,IAAI;AACnB,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,cAAY,IAAI,KAAK;AACrB,aAAW;AACb;AAEA,CAhBC,oBAgBoB,KAAK;AACxB,WAAS;AACT,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,cAAY,EAAE,EAAE,EAAE,IAAI,IAAI;AAC5B;AAEA,CAAC;AACC,YAAU;AACV,QAAM;AACN,SAAO,IAAI;AACX,aAAW;AACX,kBAAgB;AAClB;AAGA,CAAC;AACC,aAAW,YAAY,KAAK;AAC9B;AAEA,WAHa;AAIX;AACE,eAAW,WAAW;AACtB,aAAS;AACX;AACA;AACE,eAAW,WAAW;AACtB,aAAS;AACX;AACF;AAEA,CAAC;AACC,aAAW,OAAO,KAAK;AACzB;AAEA,WAHa;AAIX;AACE,aAAS;AACX;AACA;AACE,aAAS;AACX;AACF;AAEA,CAAC;AACC,cAAY,UAAU,KAAK;AAC7B;AAEA,CAJC,OAIO;AACN,aAAW,MAAM;AACnB;AAGA,CAAC,iBAAmB,CAnNnB;AAoNC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,CAAC,iBAAmB,CAxEnB,oBAwEwC;AACvC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA7EnB,oBA6EwC,KAAK;AAC5C,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,CAAC,iBAAmB,CAnJnB;AAoJC,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CAtInB;AAuID,CAAC,iBAAmB,CApGnB;AAqGC,gBAAc,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACpC;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAAC;AACC,oBAAgB;AAChB,iBAAa;AACf;AAEA,GAtPD;AAuPG,WAAO;AACP,gBAAY;AACd;AAEA,GA3GD;AA4GG,WAAO;AACP,gBAAY;AACd;AAEA,GAlND;AAmNG,oBAAgB;AAClB;AAEA,GAtND,eAsNiB;AACd,WAAO;AACP,iBAAa;AACb,gBAAY;AACd;AAEA,GA5ND,eA4NiB,MAAM;AACpB,gBAAY;AACd;AAEA,GA5OD;AA6OG,oBAAgB;AAChB,iBAAa;AACb,SAAK;AACP;AAEA,GA5TD;AA6TG,WAAO;AACP,eAAW;AACb;AACF;", "names": []}