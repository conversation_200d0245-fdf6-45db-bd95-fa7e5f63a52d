{"version": 3, "sources": ["src/app/components/crud/member-add/member-add.component.css"], "sourcesContent": ["/* Member Add Component Styles */\r\n\r\n/* Loading Spinner */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(255, 255, 255, 0.8);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(3px);\r\n}\r\n\r\n.spinner-container {\r\n  text-align: center;\r\n}\r\n\r\n/* Fade In Animation */\r\n.fade-in {\r\n  animation: fadeIn 0.5s ease-out;\r\n}\r\n\r\n/* Card Styles */\r\n.modern-card {\r\n  border-radius: 0.75rem;\r\n  border: none;\r\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\r\n  transition: all 0.3s ease;\r\n  overflow: hidden;\r\n  background-color: var(--card-bg-color);\r\n}\r\n\r\n.modern-card:hover {\r\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-header {\r\n  background-color: transparent;\r\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\r\n  padding: 1rem 1.5rem;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-body {\r\n  padding: 1.5rem;\r\n}\r\n\r\n.card-footer {\r\n  background-color: transparent;\r\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\r\n  padding: 1rem 1.5rem;\r\n}\r\n\r\n/* Form Styles */\r\n.form-section {\r\n  margin-bottom: 2rem;\r\n  padding: 1.5rem;\r\n  background-color: rgba(0, 0, 0, 0.02);\r\n  border-radius: 0.5rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.form-section:hover {\r\n  background-color: rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.section-title {\r\n  margin-bottom: 1.25rem;\r\n  font-weight: 600;\r\n  color: var(--primary-color);\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.modern-form-group {\r\n  margin-bottom: 1rem;\r\n  position: relative;\r\n}\r\n\r\n/* Hata durumunda form grubu */\r\n.modern-form-group.has-error {\r\n  animation: highlight 1s ease-in-out;\r\n}\r\n\r\n@keyframes highlight {\r\n  0% { background-color: rgba(220, 53, 69, 0.1); }\r\n  100% { background-color: transparent; }\r\n}\r\n\r\n/* Hata mesajı için stil */\r\n.error-message {\r\n  color: #dc3545;\r\n  font-size: 0.85rem;\r\n  margin-top: 0.25rem;\r\n  display: block;\r\n  animation: fadeIn 0.3s ease-in-out;\r\n}\r\n\r\n.modern-form-label {\r\n  display: block;\r\n  margin-bottom: 0.5rem;\r\n  font-weight: 500;\r\n  color: var(--text-color);\r\n}\r\n\r\n/* Zorunlu alan etiketi için stil */\r\n.modern-form-label.required::after {\r\n  content: '*';\r\n  color: #dc3545;\r\n  margin-left: 4px;\r\n}\r\n\r\n/* Hata durumunda etiket rengi */\r\n.form-error .modern-form-label {\r\n  color: #dc3545;\r\n  font-weight: 600;\r\n}\r\n\r\n.modern-form-control {\r\n  display: block;\r\n  width: 100%;\r\n  padding: 0.5rem 0.75rem;\r\n  font-size: 1rem;\r\n  line-height: 1.5;\r\n  color: var(--input-text);\r\n  background-color: var(--input-bg);\r\n  background-clip: padding-box;\r\n  border: 1px solid var(--input-border);\r\n  border-radius: 0.5rem;\r\n  transition: border-color 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n/* Zorunlu alanlar için hata durumu */\r\n.modern-form-control.ng-invalid.ng-touched {\r\n  border-color: #dc3545;\r\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\r\n}\r\n\r\n/* Titreşim animasyonu için sınıf */\r\n.shake-animation {\r\n  animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;\r\n  transform: translate3d(0, 0, 0);\r\n  backface-visibility: hidden;\r\n  perspective: 1000px;\r\n  border-color: #dc3545 !important;\r\n  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5) !important;\r\n}\r\n\r\n@keyframes shake {\r\n  0%, 100% { transform: translateX(0); }\r\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-6px); }\r\n  20%, 40%, 60%, 80% { transform: translateX(6px); }\r\n}\r\n\r\n.modern-form-control:focus {\r\n  border-color: var(--primary-color);\r\n  outline: 0;\r\n  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);\r\n}\r\n\r\n.input-group {\r\n  position: relative;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: stretch;\r\n  width: 100%;\r\n}\r\n\r\n.input-group-text {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0.5rem 0.75rem;\r\n  font-size: 1rem;\r\n  font-weight: 400;\r\n  line-height: 1.5;\r\n  color: var(--text-muted);\r\n  text-align: center;\r\n  white-space: nowrap;\r\n  background-color: var(--input-bg);\r\n  border: 1px solid var(--input-border);\r\n  border-radius: 0.5rem 0 0 0.5rem;\r\n  border-right: none;\r\n}\r\n\r\n.input-group .modern-form-control {\r\n  border-top-left-radius: 0;\r\n  border-bottom-left-radius: 0;\r\n  position: relative;\r\n  flex: 1 1 auto;\r\n  width: 1%;\r\n  min-width: 0;\r\n}\r\n\r\n/* Button Styles */\r\n.modern-btn {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0.5rem 1rem;\r\n  font-weight: 500;\r\n  border-radius: 0.5rem;\r\n  transition: all 0.3s ease;\r\n  border: none;\r\n  cursor: pointer;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.modern-btn-primary {\r\n  background-color: var(--primary-color);\r\n  color: white;\r\n}\r\n\r\n.modern-btn-primary:hover {\r\n  background-color: var(--secondary-color);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.modern-btn-primary:disabled {\r\n  background-color: #a0aec0;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.modern-btn-success {\r\n  background-color: #28a745;\r\n  color: white;\r\n}\r\n\r\n.modern-btn-success:hover {\r\n  background-color: #218838;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.modern-btn-warning {\r\n  background-color: #ffc107;\r\n  color: #212529;\r\n}\r\n\r\n.modern-btn-warning:hover {\r\n  background-color: #e0a800;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.modern-btn-outline-primary {\r\n  background-color: transparent;\r\n  color: var(--primary-color);\r\n  border: 1px solid var(--primary-color);\r\n}\r\n\r\n.modern-btn-outline-primary:hover {\r\n  background-color: var(--primary-color);\r\n  color: white;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* Progress Bar */\r\n.progress {\r\n  display: flex;\r\n  height: 6px;\r\n  overflow: hidden;\r\n  font-size: 0.75rem;\r\n  background-color: #e9ecef;\r\n  border-radius: 0.25rem;\r\n}\r\n\r\n.progress-bar {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  overflow: hidden;\r\n  color: #fff;\r\n  text-align: center;\r\n  white-space: nowrap;\r\n  background-color: var(--primary-color);\r\n  transition: width 0.6s ease;\r\n}\r\n\r\n/* Animations */\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(10px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n/* Dark Mode Support */\r\n@media (prefers-color-scheme: dark) {\r\n  .form-section {\r\n    background-color: rgba(255, 255, 255, 0.05);\r\n  }\r\n\r\n  .form-section:hover {\r\n    background-color: rgba(255, 255, 255, 0.08);\r\n  }\r\n\r\n  .input-group-text {\r\n    background-color: var(--bg-tertiary);\r\n    border-color: var(--border-color);\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  .progress {\r\n    background-color: #4a5568;\r\n  }\r\n\r\n  .modern-btn-outline-primary {\r\n    border-color: var(--primary-color);\r\n  }\r\n\r\n  .modern-btn-primary:disabled {\r\n    background-color: #4a5568;\r\n  }\r\n}\r\n\r\n/* Responsive Adjustments */\r\n@media (max-width: 767.98px) {\r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .card-header button {\r\n    margin-top: 1rem;\r\n    width: 100%;\r\n  }\r\n\r\n  .card-footer {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .card-footer button,\r\n  .card-footer a {\r\n    width: 100%;\r\n  }\r\n}\r\n"], "mappings": ";AAGA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACxB;AAEA,CAAC;AACC,cAAY;AACd;AAGA,CAAC;AACC,aAAW,OAAO,KAAK;AACzB;AAGA,CAAC;AACC,iBAAe;AACf,UAAQ;AACR,cAAY,EAAE,SAAS,QAAQ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC7C,cAAY,IAAI,KAAK;AACrB,YAAU;AACV,oBAAkB,IAAI;AACxB;AAEA,CATC,WASW;AACV,cAAY,EAAE,OAAO,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC1C;AAEA,CAAC;AACC,oBAAkB;AAClB,iBAAe,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC,WAAS,KAAK;AACd,WAAS;AACT,mBAAiB;AACjB,eAAa;AACf;AAEA,CAAC;AACC,WAAS;AACX;AAEA,CAAC;AACC,oBAAkB;AAClB,cAAY,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,WAAS,KAAK;AAChB;AAGA,CAAC;AACC,iBAAe;AACf,WAAS;AACT,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,iBAAe;AACf,cAAY,IAAI,KAAK;AACvB;AAEA,CARC,YAQY;AACX,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAClC;AAEA,CAAC;AACC,iBAAe;AACf,eAAa;AACb,SAAO,IAAI;AACX,WAAS;AACT,eAAa;AACf;AAEA,CAAC;AACC,iBAAe;AACf,YAAU;AACZ;AAGA,CANC,iBAMiB,CAAC;AACjB,aAAW,UAAU,GAAG;AAC1B;AAEA,WAHa;AAIX;AAAK,sBAAkB,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAAM;AAC/C;AAAO,sBAAkB;AAAa;AACxC;AAGA,CAAC;AACC,SAAO;AACP,aAAW;AACX,cAAY;AACZ,WAAS;AACT,aAAW,OAAO,KAAK;AACzB;AAEA,CAAC;AACC,WAAS;AACT,iBAAe;AACf,eAAa;AACb,SAAO,IAAI;AACb;AAGA,CARC,iBAQiB,CAAC,QAAQ;AACzB,WAAS;AACT,SAAO;AACP,eAAa;AACf;AAGA,CAAC,WAAW,CAfX;AAgBC,SAAO;AACP,eAAa;AACf;AAEA,CAAC;AACC,WAAS;AACT,SAAO;AACP,WAAS,OAAO;AAChB,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,mBAAiB;AACjB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe;AACf,cAAY,aAAa,KAAK,IAAI,EAAE,WAAW,KAAK;AACtD;AAGA,CAfC,mBAemB,CAAC,UAAU,CAAC;AAC9B,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,OAAO,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC7C;AAGA,CAAC;AACC,aAAW,MAAM,KAAK,aAAa,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK;AACpD,aAAW,YAAY,CAAC,EAAE,CAAC,EAAE;AAC7B,uBAAqB;AACrB,eAAa;AACb,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,QAAQ,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC9C;AAEA,WARa;AASX;AAAW,eAAW,WAAW;AAAI;AACrC;AAA0B,eAAW,WAAW;AAAO;AACvD;AAAqB,eAAW,WAAW;AAAM;AACnD;AAEA,CApCC,mBAoCmB;AAClB,gBAAc,IAAI;AAClB,WAAS;AACT,cAAY,EAAE,EAAE,EAAE,OAAO,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AAC7C;AAEA,CAAC;AACC,YAAU;AACV,WAAS;AACT,aAAW;AACX,eAAa;AACb,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS,OAAO;AAChB,aAAW;AACX,eAAa;AACb,eAAa;AACb,SAAO,IAAI;AACX,cAAY;AACZ,eAAa;AACb,oBAAkB,IAAI;AACtB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,OAAO,EAAE,EAAE;AAC1B,gBAAc;AAChB;AAEA,CAxBC,YAwBY,CAlEZ;AAmEC,0BAAwB;AACxB,6BAA2B;AAC3B,YAAU;AACV,QAAM,EAAE,EAAE;AACV,SAAO;AACP,aAAW;AACb;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,WAAS,OAAO;AAChB,eAAa;AACb,iBAAe;AACf,cAAY,IAAI,KAAK;AACrB,UAAQ;AACR,UAAQ;AACR,OAAK;AACP;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO;AACT;AAEA,CALC,kBAKkB;AACjB,oBAAkB,IAAI;AACtB,aAAW,WAAW;AACxB;AAEA,CAVC,kBAUkB;AACjB,oBAAkB;AAClB,UAAQ;AACR,aAAW;AACb;AAEA,CAAC;AACC,oBAAkB;AAClB,SAAO;AACT;AAEA,CALC,kBAKkB;AACjB,oBAAkB;AAClB,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,oBAAkB;AAClB,SAAO;AACT;AAEA,CALC,kBAKkB;AACjB,oBAAkB;AAClB,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,oBAAkB;AAClB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CANC,0BAM0B;AACzB,oBAAkB,IAAI;AACtB,SAAO;AACP,aAAW,WAAW;AACxB;AAGA,CAAC;AACC,WAAS;AACT,UAAQ;AACR,YAAU;AACV,aAAW;AACX,oBAAkB;AAClB,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,mBAAiB;AACjB,YAAU;AACV,SAAO;AACP,cAAY;AACZ,eAAa;AACb,oBAAkB,IAAI;AACtB,cAAY,MAAM,KAAK;AACzB;AAGA,WArQa;AAsQX;AAAO,aAAS;AAAG,eAAW,WAAW;AAAO;AAChD;AAAK,aAAS;AAAG,eAAW,WAAW;AAAI;AAC7C;AAGA,OAAO,CAAC,oBAAoB,EAAE;AAC5B,GAvOD;AAwOG,sBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,GA3OD,YA2Oc;AACX,sBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,GA7HD;AA8HG,sBAAkB,IAAI;AACtB,kBAAc,IAAI;AAClB,WAAO,IAAI;AACb;AAEA,GA3CD;AA4CG,sBAAkB;AACpB;AAEA,GA5DD;AA6DG,kBAAc,IAAI;AACpB;AAEA,GApGD,kBAoGoB;AACjB,sBAAkB;AACpB;AACF;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAxRD;AAyRG,oBAAgB;AAChB,iBAAa;AACf;AAEA,GA7RD,YA6Rc;AACX,gBAAY;AACZ,WAAO;AACT;AAEA,GArRD;AAsRG,oBAAgB;AAChB,SAAK;AACP;AAEA,GA1RD,YA0Rc;AAAA,EACb,CA3RD,YA2Rc;AACX,WAAO;AACT;AACF;", "names": []}