{"version": 3, "sources": ["src/app/components/profile/profile.component.css"], "sourcesContent": ["/* <PERSON>il <PERSON> */\r\n.profile-container {\r\n  padding: 20px 0;\r\n}\r\n\r\n/* Profil Fotoğrafı Stilleri */\r\n.profile-image-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.profile-image-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.profile-image-display {\r\n  position: relative;\r\n  width: 200px;\r\n  height: 200px;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  border: 4px solid var(--primary-color);\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.profile-image-display:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.profile-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.default-profile-icon {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\r\n  color: white;\r\n  font-size: 4rem;\r\n}\r\n\r\n.profile-image-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n}\r\n\r\n.image-preview-section {\r\n  width: 100%;\r\n  max-width: 400px;\r\n  background-color: var(--bg-secondary);\r\n  border-radius: 10px;\r\n  padding: 20px;\r\n  border: 2px dashed var(--border-color);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.image-preview-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.image-preview {\r\n  width: 120px;\r\n  height: 120px;\r\n  object-fit: cover;\r\n  border-radius: 10px;\r\n  border: 2px solid var(--primary-color);\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.preview-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-info {\r\n  text-align: center;\r\n  margin-top: 10px;\r\n}\r\n\r\n.upload-rules {\r\n  text-align: center;\r\n  margin-top: 15px;\r\n  padding: 10px;\r\n  background-color: var(--info-light);\r\n  border-radius: 5px;\r\n  border-left: 4px solid var(--info);\r\n}\r\n\r\n\r\n\r\n.upload-status {\r\n  text-align: center;\r\n  margin: 15px 0;\r\n  padding: 15px;\r\n  background-color: var(--bg-secondary);\r\n  border-radius: 8px;\r\n  border: 1px solid var(--border-color);\r\n}\r\n\r\n.profile-section {\r\n  background-color: var(--bg-primary);\r\n  border-radius: 10px;\r\n  box-shadow: var(--shadow-sm);\r\n  padding: 20px;\r\n  margin-bottom: 30px;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid var(--border-color);\r\n}\r\n\r\n.profile-section:hover {\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.section-title {\r\n  color: var(--primary);\r\n  font-size: 1.5rem;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid var(--border-color);\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.section-title i {\r\n  margin-right: 10px;\r\n  font-size: 1.2em;\r\n  color: var(--primary);\r\n}\r\n\r\n.profile-info {\r\n  padding: 10px 0;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid var(--border-color);\r\n}\r\n\r\n.info-row:last-child {\r\n  border-bottom: none;\r\n  margin-bottom: 0;\r\n  padding-bottom: 0;\r\n}\r\n\r\n.info-label {\r\n  font-weight: 600;\r\n  width: 150px;\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.info-value {\r\n  flex: 1;\r\n  color: var(--text-primary);\r\n}\r\n\r\n.alert-info {\r\n  background-color: var(--info-light);\r\n  border-color: var(--info);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.alert-info i {\r\n  color: var(--info);\r\n}\r\n\r\n/* Şifre Değiştirme Formu */\r\n.password-form-container {\r\n  background-color: var(--bg-secondary);\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-top: 20px;\r\n  border: 1px solid var(--border-color);\r\n}\r\n\r\n.password-field-container {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 15px;\r\n  position: relative;\r\n}\r\n\r\n.password-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: var(--primary);\r\n  color: white;\r\n  border-radius: 5px;\r\n  margin-right: 10px;\r\n  margin-top: 30px;\r\n  position: absolute;\r\n  left: 0;\r\n  z-index: 1;\r\n}\r\n\r\n.password-input-wrapper {\r\n  flex: 1;\r\n  padding-left: 50px; /* İkon genişliği + margin */\r\n}\r\n\r\n.modern-form-label {\r\n  color: var(--text-secondary);\r\n  font-weight: 500;\r\n  margin-bottom: 5px;\r\n  display: block;\r\n}\r\n\r\n.modern-form-control {\r\n  width: 100%;\r\n  padding: 10px 15px;\r\n  border: 1px solid var(--border-color);\r\n  border-radius: 5px;\r\n  background-color: var(--bg-primary);\r\n  color: var(--text-primary);\r\n  transition: all 0.3s ease;\r\n  padding-right: 40px; /* Göz ikonu için yer açma */\r\n}\r\n\r\n.modern-form-control:focus {\r\n  border-color: var(--primary);\r\n  box-shadow: 0 0 0 0.2rem var(--primary-light);\r\n  outline: none;\r\n}\r\n\r\n.modern-form-control::placeholder {\r\n  color: var(--text-secondary);\r\n  opacity: 0.7;\r\n}\r\n\r\n.input-group {\r\n  display: flex;\r\n  width: 100%;\r\n  position: relative;\r\n}\r\n\r\n.password-toggle {\r\n  background: none;\r\n  border: none;\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  padding: 0;\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  z-index: 2;\r\n}\r\n\r\n.password-toggle:hover {\r\n  color: var(--primary);\r\n}\r\n\r\n.password-toggle:focus {\r\n  outline: none;\r\n}\r\n\r\n.btn-outline-secondary {\r\n  border-color: var(--border-color);\r\n  color: var(--text-secondary);\r\n  background-color: transparent;\r\n}\r\n\r\n.btn-outline-secondary:hover {\r\n  background-color: var(--bg-secondary);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.invalid-feedback {\r\n  color: #dc3545;\r\n  font-size: 0.875rem;\r\n  margin-top: 5px;\r\n}\r\n\r\n/* Şifre Gereksinimleri */\r\n.password-requirements {\r\n  background-color: var(--bg-secondary);\r\n  border: 1px solid var(--border-color);\r\n  margin-top: 20px;\r\n  border-radius: 5px;\r\n}\r\n\r\n.password-requirements .card-title {\r\n  color: var(--text-primary);\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.requirements-list {\r\n  list-style-type: none;\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.requirements-list li {\r\n  margin-bottom: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  color: var(--text-primary);\r\n}\r\n\r\n.requirements-list li i {\r\n  margin-right: 10px;\r\n  font-size: 1.1em;\r\n}\r\n\r\n.text-success {\r\n  color: #4caf50 !important;\r\n}\r\n\r\n.text-danger {\r\n  color: #f44336 !important;\r\n}\r\n\r\n.fulfilled {\r\n  color: #4caf50;\r\n}\r\n\r\n/* Butonlar */\r\n.btn-success {\r\n  background-color: #4caf50;\r\n  border-color: #4caf50;\r\n  color: white;\r\n}\r\n\r\n.btn-success:hover {\r\n  background-color: #3d8b40;\r\n  border-color: #3d8b40;\r\n}\r\n\r\n.btn-success:disabled {\r\n  background-color: #81c784;\r\n  border-color: #81c784;\r\n}\r\n\r\n/* Responsive Styles */\r\n@media (max-width: 768px) {\r\n  .info-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .info-label {\r\n    width: 100%;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .info-value {\r\n    width: 100%;\r\n  }\r\n\r\n  .card-header h2 {\r\n    font-size: 1.3rem;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 1.2rem;\r\n  }\r\n\r\n  .password-field-container {\r\n    margin-bottom: 25px;\r\n  }\r\n\r\n  .password-icon {\r\n    width: 35px;\r\n    height: 35px;\r\n    margin-top: 32px;\r\n  }\r\n\r\n  .password-input-wrapper {\r\n    padding-left: 45px;\r\n  }\r\n\r\n  .modern-form-control {\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* Profil fotoğrafı responsive */\r\n  .profile-image-display {\r\n    width: 120px;\r\n    height: 120px;\r\n  }\r\n\r\n  .default-profile-icon {\r\n    font-size: 3rem;\r\n  }\r\n\r\n  .profile-image-actions {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n\r\n  .image-preview-section {\r\n    max-width: 100%;\r\n    padding: 15px;\r\n  }\r\n\r\n  .image-preview {\r\n    width: 100px;\r\n    height: 100px;\r\n  }\r\n}\r\n\r\n@media (max-width: 576px) {\r\n  .profile-section {\r\n    padding: 15px;\r\n  }\r\n\r\n  .password-form-container {\r\n    padding: 15px;\r\n  }\r\n\r\n  .card-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .card-body {\r\n    padding: 15px;\r\n  }\r\n\r\n  .password-icon {\r\n    width: 30px;\r\n    height: 30px;\r\n    margin-top: 33px;\r\n  }\r\n\r\n  .password-input-wrapper {\r\n    padding-left: 40px;\r\n  }\r\n\r\n  .modern-form-control {\r\n    padding: 8px 10px;\r\n    padding-right: 35px;\r\n  }\r\n\r\n  .password-toggle {\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n\r\n  /* Profil fotoğrafı mobile */\r\n  .profile-image-display {\r\n    width: 100px;\r\n    height: 100px;\r\n  }\r\n\r\n  .default-profile-icon {\r\n    font-size: 2.5rem;\r\n  }\r\n\r\n  .profile-image-actions button {\r\n    font-size: 12px;\r\n    padding: 8px 12px;\r\n  }\r\n\r\n  .image-preview {\r\n    width: 80px;\r\n    height: 80px;\r\n  }\r\n\r\n  .upload-rules {\r\n    font-size: 12px;\r\n    padding: 8px;\r\n  }\r\n}"], "mappings": ";AACA,CAAC;AACC,WAAS,KAAK;AAChB;AAGA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,YAAU;AACV,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,YAAU;AACV,UAAQ,IAAI,MAAM,IAAI;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACrC,cAAY,IAAI,KAAK;AACvB;AAEA,CAXC,qBAWqB;AACpB,aAAW,MAAM;AACjB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,cAAY;AACZ,cAAY,IAAI,KAAK;AACvB;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,gBAAgB;AAAA,MAAE,IAAI;AAC9D,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,aAAW;AACX,mBAAiB;AACnB;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACX,oBAAkB,IAAI;AACtB,iBAAe;AACf,WAAS;AACT,UAAQ,IAAI,OAAO,IAAI;AACvB,cAAY,IAAI,KAAK;AACvB;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,cAAY;AACZ,iBAAe;AACf,UAAQ,IAAI,MAAM,IAAI;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,mBAAiB;AACnB;AAEA,CAAC;AACC,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,cAAY;AACZ,WAAS;AACT,oBAAkB,IAAI;AACtB,iBAAe;AACf,eAAa,IAAI,MAAM,IAAI;AAC7B;AAIA,CAAC;AACC,cAAY;AACZ,UAAQ,KAAK;AACb,WAAS;AACT,oBAAkB,IAAI;AACtB,iBAAe;AACf,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,iBAAe;AACf,cAAY,IAAI;AAChB,WAAS;AACT,iBAAe;AACf,cAAY,IAAI,KAAK;AACrB,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAVC,eAUe;AACd,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,SAAO,IAAI;AACX,aAAW;AACX,iBAAe;AACf,kBAAgB;AAChB,iBAAe,IAAI,MAAM,IAAI;AAC7B,WAAS;AACT,eAAa;AACf;AAEA,CAVC,cAUc;AACb,gBAAc;AACd,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS,KAAK;AAChB;AAEA,CAAC;AACC,WAAS;AACT,iBAAe;AACf,kBAAgB;AAChB,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CAPC,QAOQ;AACP,iBAAe;AACf,iBAAe;AACf,kBAAgB;AAClB;AAEA,CAAC;AACC,eAAa;AACb,SAAO;AACP,SAAO,IAAI;AACb;AAEA,CAAC;AACC,QAAM;AACN,SAAO,IAAI;AACb;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CANC,WAMW;AACV,SAAO,IAAI;AACb;AAGA,CAAC;AACC,oBAAkB,IAAI;AACtB,iBAAe;AACf,WAAS;AACT,cAAY;AACZ,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,iBAAe;AACf,YAAU;AACZ;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,oBAAkB,IAAI;AACtB,SAAO;AACP,iBAAe;AACf,gBAAc;AACd,cAAY;AACZ,YAAU;AACV,QAAM;AACN,WAAS;AACX;AAEA,CAAC;AACC,QAAM;AACN,gBAAc;AAChB;AAEA,CAAC;AACC,SAAO,IAAI;AACX,eAAa;AACb,iBAAe;AACf,WAAS;AACX;AAEA,CAAC;AACC,SAAO;AACP,WAAS,KAAK;AACd,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe;AACf,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,cAAY,IAAI,KAAK;AACrB,iBAAe;AACjB;AAEA,CAXC,mBAWmB;AAClB,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,OAAO,IAAI;AAC7B,WAAS;AACX;AAEA,CAjBC,mBAiBmB;AAClB,SAAO,IAAI;AACX,WAAS;AACX;AAEA,CAAC;AACC,WAAS;AACT,SAAO;AACP,YAAU;AACZ;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,SAAO,IAAI;AACX,UAAQ;AACR,WAAS;AACT,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,YAAU;AACV,SAAO;AACP,OAAK;AACL,WAAS;AACX;AAEA,CAjBC,eAiBe;AACd,SAAO,IAAI;AACb;AAEA,CArBC,eAqBe;AACd,WAAS;AACX;AAEA,CAAC;AACC,gBAAc,IAAI;AAClB,SAAO,IAAI;AACX,oBAAkB;AACpB;AAEA,CANC,qBAMqB;AACpB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACX,cAAY;AACd;AAGA,CAAC;AACC,oBAAkB,IAAI;AACtB,UAAQ,IAAI,MAAM,IAAI;AACtB,cAAY;AACZ,iBAAe;AACjB;AAEA,CAPC,sBAOsB,CAAC;AACtB,SAAO,IAAI;AACX,aAAW;AACX,eAAa;AACb,iBAAe;AACjB;AAEA,CAAC;AACC,mBAAiB;AACjB,gBAAc;AACd,iBAAe;AACjB;AAEA,CANC,kBAMkB;AACjB,iBAAe;AACf,WAAS;AACT,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAbC,kBAakB,GAAG;AACpB,gBAAc;AACd,aAAW;AACb;AAEA,CAAC;AACC,SAAO;AACT;AAEA,CAAC;AACC,SAAO;AACT;AAEA,CAAC;AACC,SAAO;AACT;AAGA,CAAC;AACC,oBAAkB;AAClB,gBAAc;AACd,SAAO;AACT;AAEA,CANC,WAMW;AACV,oBAAkB;AAClB,gBAAc;AAChB;AAEA,CAXC,WAWW;AACV,oBAAkB;AAClB,gBAAc;AAChB;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GApND;AAqNG,oBAAgB;AAClB;AAEA,GA3MD;AA4MG,WAAO;AACP,mBAAe;AACjB;AAEA,GA1MD;AA2MG,WAAO;AACT;AAEA,GAAC,YAAY;AACX,eAAW;AACb;AAEA,GAzPD;AA0PG,eAAW;AACb;AAEA,GA9LD;AA+LG,mBAAe;AACjB;AAEA,GA3LD;AA4LG,WAAO;AACP,YAAQ;AACR,gBAAY;AACd;AAEA,GAjLD;AAkLG,kBAAc;AAChB;AAEA,GAzKD;AA0KG,eAAW;AACb;AAGA,GAhYD;AAiYG,WAAO;AACP,YAAQ;AACV;AAEA,GA9WD;AA+WG,eAAW;AACb;AAEA,GAvWD;AAwWG,oBAAgB;AAChB,iBAAa;AACf;AAEA,GArWD;AAsWG,eAAW;AACX,aAAS;AACX;AAEA,GAzVD;AA0VG,WAAO;AACP,YAAQ;AACV;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAxTD;AAyTG,aAAS;AACX;AAEA,GAvPD;AAwPG,aAAS;AACX;AAEA,GA7DC;AA8DC,aAAS;AACX;AAEA,GAAC;AACC,aAAS;AACX;AAEA,GApPD;AAqPG,WAAO;AACP,YAAQ;AACR,gBAAY;AACd;AAEA,GA1OD;AA2OG,kBAAc;AAChB;AAEA,GAlOD;AAmOG,aAAS,IAAI;AACb,mBAAe;AACjB;AAEA,GA3MD;AA4MG,WAAO;AACP,YAAQ;AACV;AAGA,GA/bD;AAgcG,WAAO;AACP,YAAQ;AACV;AAEA,GA7aD;AA8aG,eAAW;AACb;AAEA,GAtaD,sBAsawB;AACrB,eAAW;AACX,aAAS,IAAI;AACf;AAEA,GAnZD;AAoZG,WAAO;AACP,YAAQ;AACV;AAEA,GApYD;AAqYG,eAAW;AACX,aAAS;AACX;AACF;", "names": []}