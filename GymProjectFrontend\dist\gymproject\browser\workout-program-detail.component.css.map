{"version": 3, "sources": ["src/app/components/workout-programs/workout-program-detail.component.css"], "sourcesContent": ["/* Workout Program Detail Specific Styles */\n\n.program-info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n}\n\n.info-item {\n  background: var(--bg-secondary);\n  padding: 1.5rem;\n  border-radius: var(--border-radius-lg);\n  border: 1px solid var(--border-color);\n  transition: all var(--transition-speed) var(--transition-timing);\n}\n\n.info-item:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-sm);\n}\n\n.info-item.description {\n  grid-column: 1 / -1;\n}\n\n.info-header {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.info-content {\n  font-size: 1.05rem;\n  color: var(--text-primary);\n}\n\n/* Badge Styles */\n.program-badge,\n.modern-badge {\n  display: inline-block;\n  padding: 0.75rem 1.25rem;\n  font-size: 1rem;\n  font-weight: 500;\n  border-radius: var(--border-radius-md);\n  transition: all var(--transition-speed) var(--transition-timing);\n}\n\n.program-badge:hover,\n.modern-badge:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-xs);\n}\n\n.program-badge-primary,\n.modern-badge-primary {\n  background: var(--primary-light);\n  color: var(--primary);\n}\n\n.program-badge-warning,\n.modern-badge-warning {\n  background: var(--warning-light);\n  color: var(--warning);\n}\n\n.program-badge-success,\n.modern-badge-success {\n  background: var(--success-light);\n  color: var(--success);\n}\n\n.program-badge-danger,\n.modern-badge-danger {\n  background: var(--danger-light);\n  color: var(--danger);\n}\n\n.program-badge-info,\n.modern-badge-info {\n  background: var(--info-light);\n  color: var(--info);\n}\n\n.program-badge-secondary,\n.modern-badge-secondary {\n  background: var(--secondary-light);\n  color: var(--secondary);\n}\n\n.program-description {\n  line-height: 1.6;\n  margin: 0;\n  color: var(--text-primary);\n}\n\n\n.day-detail-card {\n  border: 1px solid var(--border-color);\n  border-radius: var(--border-radius-lg);\n  background-color: var(--bg-primary);\n  transition: all var(--transition-speed) var(--transition-timing);\n  height: 100%;\n}\n\n.day-detail-card:hover {\n  box-shadow: var(--shadow-sm);\n  transform: translateY(-2px);\n}\n\n.day-detail-header {\n  padding: 1rem 1.25rem;\n  border-bottom: 1px solid var(--border-color);\n  background-color: var(--bg-secondary);\n  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;\n}\n\n.day-detail-body {\n  padding: 1.25rem;\n}\n\n.day-number {\n  font-weight: 600;\n  color: var(--primary);\n}\n\n.day-name {\n  font-weight: 500;\n  color: var(--text-primary);\n  margin-left: 0.5rem;\n}\n\n.exercise-list {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.exercise-item {\n  padding: 0.75rem;\n  border-bottom: 1px solid var(--border-color);\n  transition: all var(--transition-speed) var(--transition-timing);\n}\n\n.exercise-item:last-child {\n  border-bottom: none;\n}\n\n.exercise-item:hover {\n  background-color: var(--bg-secondary);\n  border-radius: var(--border-radius-md);\n}\n\n.exercise-name {\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: 0.25rem;\n}\n\n.exercise-details {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 0.25rem;\n}\n\n.exercise-sets,\n.exercise-reps,\n.exercise-rest {\n  font-size: 0.875rem;\n  color: var(--text-secondary);\n  background-color: var(--bg-secondary);\n  padding: 0.25rem 0.5rem;\n  border-radius: var(--border-radius-sm);\n  border: 1px solid var(--border-color);\n}\n\n.exercise-notes {\n  margin-top: 0.5rem;\n  padding: 0.5rem;\n  background-color: var(--bg-secondary);\n  border-radius: var(--border-radius-sm);\n  border-left: 3px solid var(--primary);\n}\n\n.spinner-border {\n  width: 3rem;\n  height: 3rem;\n}\n\n/* Badge Styles */\n.modern-badge-secondary {\n  background-color: var(--secondary-light);\n  color: var(--secondary);\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  /* Removed stats-grid styles as stats section is removed */\n  \n  .day-detail-header {\n    padding: 0.75rem 1rem;\n  }\n  \n  .day-detail-body {\n    padding: 1rem;\n  }\n  \n  .exercise-details {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n  \n  .exercise-sets,\n  .exercise-reps,\n  .exercise-rest {\n    align-self: flex-start;\n  }\n}\n\n/* Dark mode specific adjustments */\n[data-theme=\"dark\"] .program-description {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n}\n\n/* Removed dark mode styles for stat-item as stats section is removed */\n\n[data-theme=\"dark\"] .day-detail-card {\n  background-color: var(--bg-secondary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .day-detail-header {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .exercise-item:hover {\n  background-color: var(--bg-tertiary);\n}\n\n[data-theme=\"dark\"] .exercise-sets,\n[data-theme=\"dark\"] .exercise-reps,\n[data-theme=\"dark\"] .exercise-rest {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .exercise-notes {\n  background-color: var(--bg-tertiary);\n  border-left-color: var(--primary);\n}\n"], "mappings": ";AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,QAAQ,EAAE,OAAO,KAAK,EAAE;AACtD,OAAK;AACP;AAEA,CAAC;AACC,cAAY,IAAI;AAChB,WAAS;AACT,iBAAe,IAAI;AACnB,UAAQ,IAAI,MAAM,IAAI;AACtB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CARC,SAQS;AACR,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAEA,CAbC,SAaS,CAAC;AACT,eAAa,EAAE,EAAE;AACnB;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACX,iBAAe;AACf,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACb;AAGA,CAAC;AACD,CAAC;AACC,WAAS;AACT,WAAS,QAAQ;AACjB,aAAW;AACX,eAAa;AACb,iBAAe,IAAI;AACnB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAVC,aAUa;AACd,CAVC,YAUY;AACX,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAEA,CAAC;AACD,CAAC;AACC,cAAY,IAAI;AAChB,SAAO,IAAI;AACb;AAEA,CAAC;AACD,CAAC;AACC,cAAY,IAAI;AAChB,SAAO,IAAI;AACb;AAEA,CAAC;AACD,CAAC;AACC,cAAY,IAAI;AAChB,SAAO,IAAI;AACb;AAEA,CAAC;AACD,CAAC;AACC,cAAY,IAAI;AAChB,SAAO,IAAI;AACb;AAEA,CAAC;AACD,CAAC;AACC,cAAY,IAAI;AAChB,SAAO,IAAI;AACb;AAEA,CAAC;AACD,CAAC;AACC,cAAY,IAAI;AAChB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,eAAa;AACb,UAAQ;AACR,SAAO,IAAI;AACb;AAGA,CAAC;AACC,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACtB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,UAAQ;AACV;AAEA,CARC,eAQe;AACd,cAAY,IAAI;AAChB,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,WAAS,KAAK;AACd,iBAAe,IAAI,MAAM,IAAI;AAC7B,oBAAkB,IAAI;AACtB,iBAAe,IAAI,oBAAoB,IAAI,oBAAoB,EAAE;AACnE;AAEA,CAAC;AACC,WAAS;AACX;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACX,eAAa;AACf;AAEA,CAAC;AACC,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,iBAAe,IAAI,MAAM,IAAI;AAC7B,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CANC,aAMa;AACZ,iBAAe;AACjB;AAEA,CAVC,aAUa;AACZ,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACX,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,iBAAe;AACjB;AAEA,CAAC;AACD,CAAC;AACD,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,WAAS,QAAQ;AACjB,iBAAe,IAAI;AACnB,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,eAAa,IAAI,MAAM,IAAI;AAC7B;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACV;AAGA,CAxGC;AAyGC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAGA,OAAO,CAAC,SAAS,EAAE;AAGjB,GAxFD;AAyFG,aAAS,QAAQ;AACnB;AAEA,GArFD;AAsFG,aAAS;AACX;AAEA,GAhDD;AAiDG,oBAAgB;AAChB,SAAK;AACP;AAEA,GA/CD;AAAA,EAgDC,CA/CD;AAAA,EAgDC,CA/CD;AAgDG,gBAAY;AACd;AACF;AAGA,CAAC,iBAAmB,CAjInB;AAkIC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAIA,CAAC,iBAAmB,CAjInB;AAkIC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAzHnB;AA0HC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAnGnB,aAmGiC;AAChC,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CA5EnB;AA6ED,CAAC,iBAAmB,CA5EnB;AA6ED,CAAC,iBAAmB,CA5EnB;AA6EC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAxEnB;AAyEC,oBAAkB,IAAI;AACtB,qBAAmB,IAAI;AACzB;", "names": []}