{"version": 3, "sources": ["angular:styles/component:css;ac7e85838aaaf0253d6f265777cdeedb0c3b47efc6224ae37dd22a7c42659a85;C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectFrontend\\src\\app\\components\\devices\\devices.component.html"], "sourcesContent": ["\n  .bg-primary-light {\n    background-color: var(--primary-light);\n  }\n  \n  .bg-info-light {\n    background-color: var(--info-light);\n  }\n  \n  .bg-warning-light {\n    background-color: var(--warning-light);\n  }\n  \n  .bg-primary {\n    background-color: var(--primary);\n  }\n  \n  .bg-info {\n    background-color: var(--info);\n  }\n  \n  .bg-warning {\n    background-color: var(--warning);\n  }\n  \n  .device-icon {\n    width: 32px;\n    height: 32px;\n    border-radius: 50%;\n    background-color: var(--primary-light);\n    color: var(--primary);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .content-blur {\n    filter: blur(3px);\n    pointer-events: none;\n  }\n"], "mappings": ";AACE,CAAC;AACC,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,WAAS;AACT,eAAa;AACb,mBAAiB;AACnB;AAEA,CAAC;AACC,UAAQ,KAAK;AACb,kBAAgB;AAClB;", "names": []}