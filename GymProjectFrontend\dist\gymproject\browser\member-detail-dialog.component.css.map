{"version": 3, "sources": ["src/app/components/member-detail-dialog/member-detail-dialog.component.css"], "sourcesContent": ["/* Member Detail Dialog Component Styles */\r\n\r\n/* Main Dialog Container */\r\n.member-detail-dialog {\r\n  background-color: var(--card-bg-color);\r\n  border-radius: var(--border-radius-lg);\r\n  box-shadow: var(--shadow-md);\r\n  width: 100%;\r\n  max-width: 100%;\r\n  margin: 0;\r\n  overflow: hidden;\r\n  animation: zoomIn 0.3s var(--transition-timing);\r\n  border: 1px solid var(--border-color);\r\n  position: relative;\r\n}\r\n\r\n/* Dialog Header */\r\n.dialog-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: var(--spacing-md) var(--spacing-lg);\r\n  background-color: var(--primary-color);\r\n  color: white;\r\n}\r\n\r\n.dialog-title {\r\n  margin: 0;\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.btn-close {\r\n  background: transparent;\r\n  border: none;\r\n  color: white;\r\n  font-size: 1.25rem;\r\n  cursor: pointer;\r\n  width: 32px;\r\n  height: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 50%;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.btn-close:hover {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* Dialog Content */\r\n.dialog-content {\r\n  padding: var(--spacing-md);\r\n  max-height: 75vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n/* Loading Container */\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 300px;\r\n}\r\n\r\n/* Error Container */\r\n.error-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 300px;\r\n  text-align: center;\r\n  color: var(--danger);\r\n}\r\n\r\n/* Member Details */\r\n.member-details {\r\n  animation: fadeIn 0.5s var(--transition-timing);\r\n}\r\n\r\n/* Member Header Card */\r\n.member-header-card {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: var(--spacing-lg);\r\n  background-color: var(--bg-secondary);\r\n  border-radius: var(--border-radius-lg);\r\n  margin-bottom: var(--spacing-lg);\r\n  box-shadow: var(--shadow-sm);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.member-header-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.member-avatar {\r\n  margin-right: var(--spacing-lg);\r\n}\r\n\r\n.avatar-circle-lg {\r\n  width: 120px;\r\n  height: 120px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 3rem;\r\n  font-weight: 600;\r\n  color: white;\r\n  overflow: hidden;\r\n  position: relative;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.profile-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  border-radius: 50%;\r\n}\r\n\r\n.profile-icon {\r\n  font-size: 3rem;\r\n  color: white;\r\n}\r\n\r\n.member-header-info {\r\n  flex: 1;\r\n}\r\n\r\n.member-header-info h3 {\r\n  margin: 0 0 var(--spacing-xs) 0;\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: var(--text-color);\r\n}\r\n\r\n\r\n.member-status {\r\n  display: inline-block;\r\n  padding: 0.35rem 0.75rem;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  border-radius: var(--border-radius-pill);\r\n  margin-bottom: var(--spacing-sm);\r\n}\r\n\r\n.status-active {\r\n  background-color: var(--success-light);\r\n  color: var(--success);\r\n}\r\n\r\n.status-expired {\r\n  background-color: var(--danger-light);\r\n  color: var(--danger);\r\n}\r\n\r\n.status-frozen {\r\n  background-color: var(--info-light);\r\n  color: var(--info);\r\n}\r\n\r\n.member-contact {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: var(--spacing-md);\r\n  color: var(--text-muted);\r\n}\r\n\r\n.member-contact div {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.member-contact i {\r\n  margin-right: var(--spacing-xs);\r\n}\r\n\r\n/* Tabs Container */\r\n.tabs-container {\r\n  background-color: var(--card-bg-color);\r\n  border-radius: var(--border-radius-lg);\r\n  overflow: hidden;\r\n  box-shadow: var(--shadow-sm);\r\n  border: 1px solid var(--border-color);\r\n}\r\n\r\n.tabs-header {\r\n  display: flex;\r\n  overflow-x: auto;\r\n  background-color: var(--bg-secondary);\r\n  border-bottom: 1px solid var(--border-color);\r\n}\r\n\r\n.tab {\r\n  padding: var(--spacing-md) var(--spacing-lg);\r\n  cursor: pointer;\r\n  white-space: nowrap;\r\n  display: flex;\r\n  align-items: center;\r\n  transition: background-color 0.3s ease, color 0.3s ease;\r\n  font-weight: 500;\r\n  color: var(--text-muted);\r\n  position: relative;\r\n}\r\n\r\n.tab i {\r\n  margin-right: var(--spacing-xs);\r\n}\r\n\r\n.tab.active {\r\n  color: var(--primary-color);\r\n  font-weight: 600;\r\n}\r\n\r\n.tab.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 3px;\r\n  background-color: var(--primary-color);\r\n}\r\n\r\n.tab:hover:not(.active) {\r\n  background-color: var(--bg-tertiary);\r\n}\r\n\r\n.badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-width: 20px;\r\n  height: 20px;\r\n  padding: 0 6px;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  line-height: 1;\r\n  border-radius: 10px;\r\n  background-color: var(--primary-light);\r\n  color: var(--primary-color);\r\n  margin-left: var(--spacing-xs);\r\n}\r\n\r\n/* Tab Content */\r\n.tab-content {\r\n  padding: var(--spacing-lg);\r\n  animation: fadeIn 0.3s var(--transition-timing);\r\n}\r\n\r\n/* Info Section */\r\n.info-section {\r\n  margin-bottom: var(--spacing-lg);\r\n}\r\n\r\n.info-section h4 {\r\n  margin-top: 0;\r\n  margin-bottom: var(--spacing-md);\r\n  font-weight: 600;\r\n  color: var(--text-color);\r\n  border-bottom: 1px solid var(--border-color);\r\n  padding-bottom: var(--spacing-xs);\r\n}\r\n\r\n/* Info Grid */\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n  gap: var(--spacing-md);\r\n}\r\n\r\n.info-item {\r\n  padding: var(--spacing-md);\r\n  background-color: var(--bg-secondary);\r\n  border-radius: var(--border-radius-md);\r\n  transition: transform 0.3s ease, background-color 0.3s ease;\r\n}\r\n\r\n.info-item:hover {\r\n  transform: translateY(-3px);\r\n  background-color: var(--bg-tertiary);\r\n}\r\n\r\n.info-label {\r\n  font-size: 0.875rem;\r\n  color: var(--text-muted);\r\n  margin-bottom: var(--spacing-xs);\r\n}\r\n\r\n.info-value {\r\n  font-weight: 500;\r\n  color: var(--text-color);\r\n}\r\n\r\n/* Table Styles */\r\n.table-responsive {\r\n  overflow-x: auto;\r\n  margin-bottom: var(--spacing-md);\r\n}\r\n\r\n.modern-table {\r\n  width: 100%;\r\n  border-collapse: separate;\r\n  border-spacing: 0;\r\n  margin-bottom: var(--spacing-md);\r\n}\r\n\r\n.modern-table th {\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  font-size: 0.75rem;\r\n  letter-spacing: 0.5px;\r\n  padding: var(--spacing-md);\r\n  border-bottom: 2px solid var(--border-color);\r\n  background-color: var(--bg-secondary);\r\n  color: var(--text-muted);\r\n  text-align: left;\r\n}\r\n\r\n.modern-table td {\r\n  padding: var(--spacing-md);\r\n  vertical-align: middle;\r\n  border-bottom: 1px solid var(--border-color);\r\n}\r\n\r\n.modern-table tbody tr {\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.modern-table tbody tr:hover {\r\n  background-color: var(--bg-tertiary);\r\n}\r\n\r\n/* Status Badge */\r\n.status-badge {\r\n  display: inline-block;\r\n  padding: 0.25rem 0.5rem;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  border-radius: var(--border-radius-pill);\r\n  text-align: center;\r\n}\r\n\r\n/* No Data Message */\r\n.no-data {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: var(--spacing-lg);\r\n  color: var(--text-muted);\r\n  background-color: var(--bg-secondary);\r\n  border-radius: var(--border-radius-md);\r\n  font-style: italic;\r\n}\r\n\r\n.no-data i {\r\n  margin-right: var(--spacing-xs);\r\n}\r\n\r\n/* Responsive Styles */\r\n@media (max-width: 768px) {\r\n  .member-header-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n  }\r\n\r\n  .member-avatar {\r\n    margin-right: 0;\r\n    margin-bottom: var(--spacing-md);\r\n  }\r\n\r\n  .avatar-circle-lg {\r\n    width: 100px;\r\n    height: 100px;\r\n    font-size: 2.5rem;\r\n  }\r\n\r\n  .profile-icon {\r\n    font-size: 2.5rem;\r\n  }\r\n\r\n  .member-contact {\r\n    justify-content: center;\r\n  }\r\n\r\n  .tabs-header {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .tab {\r\n    flex: 1 1 auto;\r\n    justify-content: center;\r\n    padding: var(--spacing-md) var(--spacing-sm);\r\n  }\r\n\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n@media (max-width: 576px) {\r\n  .dialog-content {\r\n    padding: var(--spacing-sm);\r\n  }\r\n\r\n  .tab {\r\n    font-size: 0.875rem;\r\n  }\r\n\r\n  .tab i {\r\n    margin-right: 0;\r\n  }\r\n\r\n  .tab span:not(.badge) {\r\n    display: none;\r\n  }\r\n\r\n  .badge {\r\n    margin-left: 0;\r\n  }\r\n}\r\n\r\n/* Kalan gün bilgisi için stil */\r\n.remaining-days {\r\n  font-size: 0.9em; /* Biraz daha küçük font */\r\n  color: var(--text-muted); /* Soluk renk */\r\n  font-weight: 400; /* Normal kalınlık */\r\n  margin-left: 8px; /* İsimden biraz boşluk */\r\n}\r\n\r\n/* Dark Mode Specific Adjustments */\r\n[data-theme=\"dark\"] .member-header-card {\r\n  background-color: var(--bg-tertiary);\r\n}\r\n\r\n[data-theme=\"dark\"] .info-item {\r\n  background-color: var(--bg-tertiary);\r\n}\r\n\r\n[data-theme=\"dark\"] .info-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-table tbody tr:hover {\r\n  background-color: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n[data-theme=\"dark\"] .no-data {\r\n  background-color: var(--bg-tertiary);\r\n}\r\n"], "mappings": ";AAGA,CAAC;AACC,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,SAAO;AACP,aAAW;AACX,UAAQ;AACR,YAAU;AACV,aAAW,OAAO,KAAK,IAAI;AAC3B,UAAQ,IAAI,MAAM,IAAI;AACtB,YAAU;AACZ;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS,IAAI,cAAc,IAAI;AAC/B,oBAAkB,IAAI;AACtB,SAAO;AACT;AAEA,CAAC;AACC,UAAQ;AACR,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,SAAO;AACP,aAAW;AACX,UAAQ;AACR,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,iBAAe;AACf,cAAY,iBAAiB,KAAK;AACpC;AAEA,CAfC,SAeS;AACR,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAGA,CAAC;AACC,WAAS,IAAI;AACb,cAAY;AACZ,cAAY;AACd;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,cAAY;AACd;AAGA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,mBAAiB;AACjB,cAAY;AACZ,cAAY;AACZ,SAAO,IAAI;AACb;AAGA,CAAC;AACC,aAAW,OAAO,KAAK,IAAI;AAC7B;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS,IAAI;AACb,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,cAAY,UAAU,KAAK,IAAI,EAAE,WAAW,KAAK;AACnD;AAEA,CAXC,kBAWkB;AACjB,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,gBAAc,IAAI;AACpB;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW;AACX,eAAa;AACb,SAAO;AACP,YAAU;AACV,YAAU;AACV,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,cAAY;AACZ,iBAAe;AACjB;AAEA,CAAC;AACC,aAAW;AACX,SAAO;AACT;AAEA,CAAC;AACC,QAAM;AACR;AAEA,CAJC,mBAImB;AAClB,UAAQ,EAAE,EAAE,IAAI,cAAc;AAC9B,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACb;AAGA,CAAC;AACC,WAAS;AACT,WAAS,QAAQ;AACjB,aAAW;AACX,eAAa;AACb,iBAAe,IAAI;AACnB,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS;AACT,aAAW;AACX,OAAK,IAAI;AACT,SAAO,IAAI;AACb;AAEA,CAPC,eAOe;AACd,WAAS;AACT,eAAa;AACf;AAEA,CAZC,eAYe;AACd,gBAAc,IAAI;AACpB;AAGA,CAAC;AACC,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,YAAU;AACV,cAAY,IAAI;AAChB,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,WAAS;AACT,cAAY;AACZ,oBAAkB,IAAI;AACtB,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CAAC;AACC,WAAS,IAAI,cAAc,IAAI;AAC/B,UAAQ;AACR,eAAa;AACb,WAAS;AACT,eAAa;AACb,cAAY,iBAAiB,KAAK,IAAI,EAAE,MAAM,KAAK;AACnD,eAAa;AACb,SAAO,IAAI;AACX,YAAU;AACZ;AAEA,CAZC,IAYI;AACH,gBAAc,IAAI;AACpB;AAEA,CAhBC,GAgBG,CAAC;AACH,SAAO,IAAI;AACX,eAAa;AACf;AAEA,CArBC,GAqBG,CALC,MAKM;AACT,WAAS;AACT,YAAU;AACV,UAAQ;AACR,QAAM;AACN,SAAO;AACP,UAAQ;AACR,oBAAkB,IAAI;AACxB;AAEA,CA/BC,GA+BG,MAAM,KAAK,CAfV;AAgBH,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW;AACX,UAAQ;AACR,WAAS,EAAE;AACX,aAAW;AACX,eAAa;AACb,eAAa;AACb,iBAAe;AACf,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,eAAa,IAAI;AACnB;AAGA,CAAC;AACC,WAAS,IAAI;AACb,aAAW,OAAO,KAAK,IAAI;AAC7B;AAGA,CAAC;AACC,iBAAe,IAAI;AACrB;AAEA,CAJC,aAIa;AACZ,cAAY;AACZ,iBAAe,IAAI;AACnB,eAAa;AACb,SAAO,IAAI;AACX,iBAAe,IAAI,MAAM,IAAI;AAC7B,kBAAgB,IAAI;AACtB;AAGA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,SAAS,EAAE,OAAO,KAAK,EAAE;AACvD,OAAK,IAAI;AACX;AAEA,CAAC;AACC,WAAS,IAAI;AACb,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY,UAAU,KAAK,IAAI,EAAE,iBAAiB,KAAK;AACzD;AAEA,CAPC,SAOS;AACR,aAAW,WAAW;AACtB,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACX,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACb;AAGA,CAAC;AACC,cAAY;AACZ,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,SAAO;AACP,mBAAiB;AACjB,kBAAgB;AAChB,iBAAe,IAAI;AACrB;AAEA,CAPC,aAOa;AACZ,eAAa;AACb,kBAAgB;AAChB,aAAW;AACX,kBAAgB;AAChB,WAAS,IAAI;AACb,iBAAe,IAAI,MAAM,IAAI;AAC7B,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,cAAY;AACd;AAEA,CAnBC,aAmBa;AACZ,WAAS,IAAI;AACb,kBAAgB;AAChB,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CAzBC,aAyBa,MAAM;AAClB,cAAY,iBAAiB,KAAK;AACpC;AAEA,CA7BC,aA6Ba,MAAM,EAAE;AACpB,oBAAkB,IAAI;AACxB;AAGA,CAAC;AACC,WAAS;AACT,WAAS,QAAQ;AACjB,aAAW;AACX,eAAa;AACb,iBAAe,IAAI;AACnB,cAAY;AACd;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,WAAS,IAAI;AACb,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY;AACd;AAEA,CAXC,QAWQ;AACP,gBAAc,IAAI;AACpB;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA3RD;AA4RG,oBAAgB;AAChB,gBAAY;AACd;AAEA,GAhRD;AAiRG,kBAAc;AACd,mBAAe,IAAI;AACrB;AAEA,GAjRD;AAkRG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AAEA,GAjQD;AAkQG,eAAW;AACb;AAEA,GA5ND;AA6NG,qBAAiB;AACnB;AAEA,GAvMD;AAwMG,eAAW;AACb;AAEA,GApMD;AAqMG,UAAM,EAAE,EAAE;AACV,qBAAiB;AACjB,aAAS,IAAI,cAAc,IAAI;AACjC;AAEA,GAlID;AAmIG,2BAAuB;AACzB;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAlWD;AAmWG,aAAS,IAAI;AACf;AAEA,GApND;AAqNG,eAAW;AACb;AAEA,GAxND,IAwNM;AACH,kBAAc;AAChB;AAEA,GA5ND,IA4NM,IAAI,KAAK,CAzLf;AA0LG,aAAS;AACX;AAEA,GA7LD;AA8LG,iBAAa;AACf;AACF;AAGA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACX,eAAa;AACb,eAAa;AACf;AAGA,CAAC,iBAAmB,CAjWnB;AAkWC,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CApKnB;AAqKC,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CAxKnB,SAwK6B;AAC5B,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,CAAC,iBAAmB,CA/InB,aA+IiC,MAAM,EAAE;AACxC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,CAAC,iBAAmB,CAvGnB;AAwGC,oBAAkB,IAAI;AACxB;", "names": []}