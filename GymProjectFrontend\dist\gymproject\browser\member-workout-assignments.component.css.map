{"version": 3, "sources": ["src/app/components/member-workout-assignments/member-workout-assignments.component.css"], "sourcesContent": ["/* Member Workout Assignments Component Styles */\n\n/* Page Header */\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: var(--spacing-lg);\n  padding: var(--spacing-lg);\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\n  border-radius: var(--border-radius-lg);\n  color: white;\n}\n\n.page-title-container {\n  flex: 1;\n}\n\n.page-title {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: 1.75rem;\n  font-weight: 700;\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n\n.page-icon {\n  font-size: 1.5rem;\n}\n\n.page-subtitle {\n  margin: 0;\n  opacity: 0.9;\n  font-size: 1rem;\n}\n\n.page-actions {\n  display: flex;\n  gap: var(--spacing-sm);\n}\n\n/* Statistics Container */\n.stats-container {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-lg);\n}\n\n/* Search Container in Header */\n.modern-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: var(--spacing-md);\n}\n\n.search-container {\n  flex: 1;\n  min-width: 350px;\n  max-width: 600px;\n}\n\n.search-input-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.search-icon {\n  position: absolute;\n  left: var(--spacing-sm);\n  color: var(--text-secondary);\n  z-index: 1;\n  font-size: 0.9rem;\n}\n\n.search-input {\n  padding-left: 2.5rem !important;\n  width: 100%;\n}\n\n/* Table Styles */\n.table-responsive {\n  overflow-x: auto;\n  margin: var(--spacing-md) 0;\n}\n\n.member-info strong {\n  color: var(--text-primary);\n  font-weight: 600;\n}\n\n.program-info strong {\n  color: var(--primary);\n  font-weight: 600;\n}\n\n.program-stats {\n  text-align: center;\n}\n\n.program-stats small {\n  color: var(--text-secondary);\n  font-size: 0.75rem;\n}\n\n/* Action Buttons */\n.action-buttons {\n  display: flex;\n  gap: var(--spacing-xs);\n  justify-content: center;\n}\n\n.action-buttons .modern-btn {\n  min-width: 32px;\n  height: 32px;\n  padding: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* Loading State */\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: var(--spacing-xl);\n}\n\n.loading-spinner {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-md);\n  color: var(--text-secondary);\n}\n\n.loading-spinner i {\n  font-size: 2rem;\n  color: var(--primary);\n}\n\n/* Empty State */\n.empty-state {\n  text-align: center;\n  padding: var(--spacing-xl);\n  color: var(--text-secondary);\n}\n\n.empty-icon {\n  font-size: 4rem;\n  color: var(--text-muted);\n  margin-bottom: var(--spacing-md);\n}\n\n.empty-state h3 {\n  color: var(--text-primary);\n  margin-bottom: var(--spacing-sm);\n}\n\n.empty-state p {\n  margin-bottom: var(--spacing-lg);\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n/* Pagination */\n.pagination-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: var(--spacing-lg);\n  padding-top: var(--spacing-md);\n  border-top: 1px solid var(--border-color);\n}\n\n.pagination-info {\n  color: var(--text-secondary);\n  font-size: 0.875rem;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .page-header {\n    flex-direction: column;\n    gap: var(--spacing-md);\n  }\n\n  .page-actions {\n    width: 100%;\n    justify-content: stretch;\n  }\n\n  .page-actions .modern-btn {\n    flex: 1;\n  }\n\n  .modern-card-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-sm);\n  }\n\n  .search-container {\n    min-width: 0;\n    max-width: none;\n    width: 100%;\n    order: 2;\n  }\n\n  .stats-container {\n    grid-template-columns: 1fr;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n    gap: var(--spacing-xs);\n  }\n\n  .pagination-container {\n    flex-direction: column;\n    gap: var(--spacing-sm);\n  }\n\n  .modern-pagination {\n    justify-content: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .page-header {\n    padding: var(--spacing-md);\n  }\n\n  .page-title {\n    font-size: 1.5rem;\n  }\n\n  .table-responsive {\n    font-size: 0.875rem;\n  }\n\n  .modern-table th,\n  .modern-table td {\n    padding: var(--spacing-sm);\n  }\n\n  .action-buttons .modern-btn {\n    min-width: 28px;\n    height: 28px;\n    font-size: 0.75rem;\n  }\n}\n\n/* Dark Mode Adjustments */\n[data-theme=\"dark\"] .page-header {\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\n}\n\n[data-theme=\"dark\"] .member-info strong {\n  color: var(--text-primary);\n}\n\n[data-theme=\"dark\"] .program-info strong {\n  color: var(--primary);\n}\n\n[data-theme=\"dark\"] .program-stats small {\n  color: var(--text-secondary);\n}\n\n[data-theme=\"dark\"] .loading-spinner {\n  color: var(--text-secondary);\n}\n\n[data-theme=\"dark\"] .empty-state {\n  color: var(--text-secondary);\n}\n\n[data-theme=\"dark\"] .empty-state h3 {\n  color: var(--text-primary);\n}\n\n[data-theme=\"dark\"] .pagination-info {\n  color: var(--text-secondary);\n}\n\n[data-theme=\"dark\"] .search-icon {\n  color: var(--text-secondary);\n}\n\n[data-theme=\"dark\"] .search-input {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n  color: var(--text-primary);\n}\n\n[data-theme=\"dark\"] .search-input:focus {\n  border-color: var(--primary);\n  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);\n}\n"], "mappings": ";AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,iBAAe,IAAI;AACnB,WAAS,IAAI;AACb;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,IAAI,gBAAgB;AAC3E,iBAAe,IAAI;AACnB,SAAO;AACT;AAEA,CAAC;AACC,QAAM;AACR;AAEA,CAAC;AACC,UAAQ,EAAE,EAAE,IAAI,cAAc;AAC9B,aAAW;AACX,eAAa;AACb,WAAS;AACT,eAAa;AACb,OAAK,IAAI;AACX;AAEA,CAAC;AACC,aAAW;AACb;AAEA,CAAC;AACC,UAAQ;AACR,WAAS;AACT,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,OAAK,IAAI;AACX;AAGA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,QAAQ,EAAE,OAAO,KAAK,EAAE;AACtD,OAAK,IAAI;AACT,iBAAe,IAAI;AACrB;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,aAAW;AACX,OAAK,IAAI;AACX;AAEA,CAAC;AACC,QAAM;AACN,aAAW;AACX,aAAW;AACb;AAEA,CAAC;AACC,YAAU;AACV,WAAS;AACT,eAAa;AACf;AAEA,CAAC;AACC,YAAU;AACV,QAAM,IAAI;AACV,SAAO,IAAI;AACX,WAAS;AACT,aAAW;AACb;AAEA,CAAC;AACC,gBAAc;AACd,SAAO;AACT;AAGA,CAAC;AACC,cAAY;AACZ,UAAQ,IAAI,cAAc;AAC5B;AAEA,CAAC,YAAY;AACX,SAAO,IAAI;AACX,eAAa;AACf;AAEA,CAAC,aAAa;AACZ,SAAO,IAAI;AACX,eAAa;AACf;AAEA,CAAC;AACC,cAAY;AACd;AAEA,CAJC,cAIc;AACb,SAAO,IAAI;AACX,aAAW;AACb;AAGA,CAAC;AACC,WAAS;AACT,OAAK,IAAI;AACT,mBAAiB;AACnB;AAEA,CANC,eAMe,CAAC;AACf,aAAW;AACX,UAAQ;AACR,WAAS;AACT,WAAS;AACT,eAAa;AACb,mBAAiB;AACnB;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS,IAAI;AACf;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,OAAK,IAAI;AACT,SAAO,IAAI;AACb;AAEA,CARC,gBAQgB;AACf,aAAW;AACX,SAAO,IAAI;AACb;AAGA,CAAC;AACC,cAAY;AACZ,WAAS,IAAI;AACb,SAAO,IAAI;AACb;AAEA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACX,iBAAe,IAAI;AACrB;AAEA,CAZC,YAYY;AACX,SAAO,IAAI;AACX,iBAAe,IAAI;AACrB;AAEA,CAjBC,YAiBY;AACX,iBAAe,IAAI;AACnB,aAAW;AACX,eAAa;AACb,gBAAc;AAChB;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,cAAY,IAAI;AAChB,eAAa,IAAI;AACjB,cAAY,IAAI,MAAM,IAAI;AAC5B;AAEA,CAAC;AACC,SAAO,IAAI;AACX,aAAW;AACb;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAzLD;AA0LG,oBAAgB;AAChB,SAAK,IAAI;AACX;AAEA,GA5JD;AA6JG,WAAO;AACP,qBAAiB;AACnB;AAEA,GAjKD,aAiKe,CAlFC;AAmFb,UAAM;AACR;AAEA,GAvJD;AAwJG,oBAAgB;AAChB,iBAAa;AACb,SAAK,IAAI;AACX;AAEA,GArJD;AAsJG,eAAW;AACX,eAAW;AACX,WAAO;AACP,WAAO;AACT;AAEA,GA5KD;AA6KG,2BAAuB;AACzB;AAEA,GA7GD;AA8GG,oBAAgB;AAChB,SAAK,IAAI;AACX;AAEA,GApDD;AAqDG,oBAAgB;AAChB,SAAK,IAAI;AACX;AAEA,GAAC;AACC,qBAAiB;AACnB;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAxOD;AAyOG,aAAS,IAAI;AACf;AAEA,GA7ND;AA8NG,eAAW;AACb;AAEA,GA9JD;AA+JG,eAAW;AACb;AAEA,GAAC,aAAa;AAAA,EACd,CADC,aACa;AACZ,aAAS,IAAI;AACf;AAEA,GA9ID,eA8IiB,CAxID;AAyIb,eAAW;AACX,YAAQ;AACR,eAAW;AACb;AACF;AAGA,CAAC,iBAAmB,CAjQnB;AAkQC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,IAAI,gBAAgB;AAC7E;AAEA,CAAC,iBAAmB,CA9KnB,YA8KgC;AAC/B,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA7KnB,aA6KiC;AAChC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA5KnB,cA4KkC;AACjC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA/InB;AAgJC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CArInB;AAsIC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAzInB,YAyIgC;AAC/B,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA3GnB;AA4GC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA7NnB;AA8NC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAzNnB;AA0NC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA/NnB,YA+NgC;AAC/B,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,OAAO,KAAK,IAAI,cAAc,EAAE;AACpD;", "names": []}