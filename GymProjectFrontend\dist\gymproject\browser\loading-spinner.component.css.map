{"version": 3, "sources": ["angular:styles/component:css;3880b229c0c8ea7550197cd1c10eba662b32bc032449c431343f163cbe83e752;C:/Users/<USER>/Desktop/GymProject/GymProjectFrontend/src/app/components/loading-spinner/loading-spinner.component.ts"], "sourcesContent": ["\n    :host {\n      display: block;\n    }\n\n    .spinner-container {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      height: 100%;\n      width: 100%;\n    }\n\n    .spinner-container.overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: rgba(var(--bg-primary-rgb), 0.85);\n      z-index: 999; /* Sidebar'dan <PERSON> z-index */\n      backdrop-filter: blur(4px);\n      transition: all 0.3s ease;\n    }\n\n    /* Sidebar-aware overlay - sidebar'ın sağından başlar */\n    .spinner-container.overlay.sidebar-aware {\n      left: 280px; /* Normal sidebar genişliği */\n      width: calc(100% - 280px);\n    }\n\n    /* Sidebar collapsed durumunda */\n    :host-context(.app-container.sidebar-collapsed) .spinner-container.overlay.sidebar-aware {\n      left: 80px; /* Collapsed sidebar genişliği */\n      width: calc(100% - 80px);\n    }\n\n    /* Mobile responsive - sidebar overlay olduğu için full width */\n    @media (max-width: 991.98px) {\n      .spinner-container.overlay.sidebar-aware {\n        left: 0;\n        width: 100%;\n      }\n\n      :host-context(.app-container.sidebar-collapsed) .spinner-container.overlay.sidebar-aware {\n        left: 0;\n        width: 100%;\n      }\n    }\n\n    .spinner-content {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      gap: 20px;\n    }\n\n    .spinner-content.with-text {\n      padding: 30px;\n      border-radius: 16px;\n      background-color: rgba(var(--bg-secondary-rgb), 0.95);\n      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\n      backdrop-filter: blur(10px);\n      border: 1px solid var(--border-color);\n      color: var(--text-primary);\n    }\n\n    .gym-spinner {\n      position: relative;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n    }\n\n    .gym-spinner.small {\n      width: 60px;\n      height: 40px;\n    }\n\n    .gym-spinner.medium {\n      width: 90px;\n      height: 60px;\n    }\n\n    .gym-spinner.large {\n      width: 120px;\n      height: 80px;\n    }\n\n    .spinner-circle {\n      position: absolute;\n      border-radius: 50%;\n      border: 3px solid transparent;\n      border-top-color: var(--primary-color, #4361ee);\n      border-bottom-color: var(--primary-color, #4361ee);\n      animation: spin 1.5s linear infinite;\n    }\n\n    .small .spinner-circle {\n      width: 40px;\n      height: 40px;\n      border-width: 2px;\n    }\n\n    .medium .spinner-circle {\n      width: 60px;\n      height: 60px;\n      border-width: 3px;\n    }\n\n    .large .spinner-circle {\n      width: 80px;\n      height: 80px;\n      border-width: 4px;\n    }\n\n    .dumbbell {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      animation: lift 2s ease-in-out infinite;\n    }\n\n    .weight {\n      background: linear-gradient(135deg, var(--primary-color, #4361ee) 0%, var(--secondary-color, #3f37c9) 100%);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      box-shadow: 0 0 15px rgba(67, 97, 238, 0.5);\n    }\n\n    .inner-weight {\n      width: 50%;\n      height: 50%;\n      background-color: rgba(255, 255, 255, 0.2);\n      border-radius: 50%;\n    }\n\n    .weight.left {\n      animation: pulse-left 2s ease-in-out infinite;\n    }\n\n    .weight.right {\n      animation: pulse-right 2s ease-in-out infinite;\n    }\n\n    .small .weight {\n      width: 16px;\n      height: 16px;\n    }\n\n    .medium .weight {\n      width: 22px;\n      height: 22px;\n    }\n\n    .large .weight {\n      width: 28px;\n      height: 28px;\n    }\n\n    .handle {\n      height: 8px;\n      background: linear-gradient(90deg, var(--primary-color, #4361ee) 0%, var(--secondary-color, #3f37c9) 100%);\n      border-radius: 4px;\n      box-shadow: 0 0 10px rgba(67, 97, 238, 0.5);\n    }\n\n    .small .handle {\n      width: 30px;\n      height: 5px;\n    }\n\n    .medium .handle {\n      width: 40px;\n      height: 6px;\n    }\n\n    .large .handle {\n      width: 50px;\n      height: 8px;\n    }\n\n    .spinner-text {\n      color: var(--text-primary);\n      font-size: 16px;\n      font-weight: 500;\n      text-align: center;\n      letter-spacing: 0.5px;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .dots {\n      display: flex;\n      gap: 4px;\n    }\n\n    .dot {\n      width: 4px;\n      height: 4px;\n      background-color: var(--text-primary);\n      border-radius: 50%;\n      display: inline-block;\n    }\n\n    .dot:nth-child(1) {\n      animation: dot-fade 1.5s ease-in-out infinite;\n    }\n\n    .dot:nth-child(2) {\n      animation: dot-fade 1.5s ease-in-out 0.5s infinite;\n    }\n\n    .dot:nth-child(3) {\n      animation: dot-fade 1.5s ease-in-out 1s infinite;\n    }\n\n    @keyframes spin {\n      0% {\n        transform: rotate(0deg);\n      }\n      100% {\n        transform: rotate(360deg);\n      }\n    }\n\n    @keyframes lift {\n      0%, 100% {\n        transform: translateY(0);\n      }\n      50% {\n        transform: translateY(-5px);\n      }\n    }\n\n    @keyframes pulse-left {\n      0%, 100% {\n        transform: scale(1);\n      }\n      50% {\n        transform: scale(1.1);\n      }\n    }\n\n    @keyframes pulse-right {\n      0%, 100% {\n        transform: scale(1);\n      }\n      50% {\n        transform: scale(1.1);\n      }\n    }\n\n    @keyframes dot-fade {\n      0%, 100% {\n        opacity: 0.3;\n      }\n      50% {\n        opacity: 1;\n      }\n    }\n  "], "mappings": ";AACI;AACE,WAAS;AACX;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,UAAQ;AACR,SAAO;AACT;AAEA,CARC,iBAQiB,CAAC;AACjB,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,oBAAkB,KAAK,IAAI,iBAAiB,EAAE;AAC9C,WAAS;AACT,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACtB,cAAY,IAAI,KAAK;AACvB;AAGA,CArBC,iBAqBiB,CAbC,OAaO,CAAC;AACzB,QAAM;AACN,SAAO,KAAK,KAAK,EAAE;AACrB;AAGA,cAAc,CAAC,aAAa,CAAC,mBAAmB,CA3B/C,iBA2BiE,CAnB/C,OAmBuD,CAN/C;AAOzB,QAAM;AACN,SAAO,KAAK,KAAK,EAAE;AACrB;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAlCD,iBAkCmB,CA1BD,OA0BS,CAbD;AAcvB,UAAM;AACN,WAAO;AACT;AAEA,gBAAc,CAAC,aAAa,CAAC,mBAAmB,CAvCjD,iBAuCmE,CA/BjD,OA+ByD,CAlBjD;AAmBvB,UAAM;AACN,WAAO;AACT;AACF;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,mBAAiB;AACjB,OAAK;AACP;AAEA,CARC,eAQe,CAAC;AACf,WAAS;AACT,iBAAe;AACf,oBAAkB,KAAK,IAAI,mBAAmB,EAAE;AAChD,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACrC,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACtB,UAAQ,IAAI,MAAM,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,YAAU;AACV,WAAS;AACT,mBAAiB;AACjB,eAAa;AACf;AAEA,CAPC,WAOW,CAAC;AACX,SAAO;AACP,UAAQ;AACV;AAEA,CAZC,WAYW,CAAC;AACX,SAAO;AACP,UAAQ;AACV;AAEA,CAjBC,WAiBW,CAAC;AACX,SAAO;AACP,UAAQ;AACV;AAEA,CAAC;AACC,YAAU;AACV,iBAAe;AACf,UAAQ,IAAI,MAAM;AAClB,oBAAkB,IAAI,eAAe,EAAE;AACvC,uBAAqB,IAAI,eAAe,EAAE;AAC1C,aAAW,KAAK,KAAK,OAAO;AAC9B;AAEA,CAxBa,MAwBN,CATN;AAUC,SAAO;AACP,UAAQ;AACR,gBAAc;AAChB;AAEA,CAzBa,OAyBL,CAfP;AAgBC,SAAO;AACP,UAAQ;AACR,gBAAc;AAChB;AAEA,CA1Ba,MA0BN,CArBN;AAsBC,SAAO;AACP,UAAQ;AACR,gBAAc;AAChB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW,KAAK,GAAG,YAAY;AACjC;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,eAAe,EAAE,SAAS,EAAE;AAAA,MAAE,IAAI,iBAAiB,EAAE,SAAS;AACtG,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,cAAY,EAAE,EAAE,KAAK,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AACzC;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,iBAAe;AACjB;AAEA,CAhBC,MAgBM,CAAC;AACN,aAAW,WAAW,GAAG,YAAY;AACvC;AAEA,CApBC,MAoBM,CAAC;AACN,aAAW,YAAY,GAAG,YAAY;AACxC;AAEA,CAzEa,MAyEN,CAxBN;AAyBC,SAAO;AACP,UAAQ;AACV;AAEA,CAzEa,OAyEL,CA7BP;AA8BC,SAAO;AACP,UAAQ;AACV;AAEA,CAzEa,MAyEN,CAlCN;AAmCC,SAAO;AACP,UAAQ;AACV;AAEA,CAAC;AACC,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,KAAK;AAAA,MAAE,IAAI,eAAe,EAAE,SAAS,EAAE;AAAA,MAAE,IAAI,iBAAiB,EAAE,SAAS;AACrG,iBAAe;AACf,cAAY,EAAE,EAAE,KAAK,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AACzC;AAEA,CA/Fa,MA+FN,CAPN;AAQC,SAAO;AACP,UAAQ;AACV;AAEA,CA/Fa,OA+FL,CAZP;AAaC,SAAO;AACP,UAAQ;AACV;AAEA,CA/Fa,MA+FN,CAjBN;AAkBC,SAAO;AACP,UAAQ;AACV;AAEA,CAAC;AACC,SAAO,IAAI;AACX,aAAW;AACX,eAAa;AACb,cAAY;AACZ,kBAAgB;AAChB,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACP;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,oBAAkB,IAAI;AACtB,iBAAe;AACf,WAAS;AACX;AAEA,CARC,GAQG;AACF,aAAW,SAAS,KAAK,YAAY;AACvC;AAEA,CAZC,GAYG;AACF,aAAW,SAAS,KAAK,YAAY,KAAK;AAC5C;AAEA,CAhBC,GAgBG;AACF,aAAW,SAAS,KAAK,YAAY,GAAG;AAC1C;AAEA,WA7Ha;AA8HX;AACE,eAAW,OAAO;AACpB;AACA;AACE,eAAW,OAAO;AACpB;AACF;AAEA,WA7Ga;AA8GX;AACE,eAAW,WAAW;AACxB;AACA;AACE,eAAW,WAAW;AACxB;AACF;AAEA,WAlGa;AAmGX;AACE,eAAW,MAAM;AACnB;AACA;AACE,eAAW,MAAM;AACnB;AACF;AAEA,WAvGa;AAwGX;AACE,eAAW,MAAM;AACnB;AACA;AACE,eAAW,MAAM;AACnB;AACF;AAEA,WA/Ca;AAgDX;AACE,aAAS;AACX;AACA;AACE,aAAS;AACX;AACF;", "names": []}