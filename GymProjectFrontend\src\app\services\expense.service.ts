import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { ResponseModel } from '../models/responseModel';
import { Expense } from '../models/expense.model';
import { ExpenseDto } from '../models/expenseDto.model';
import { ExpenseDashboardDto } from '../models/expenseDashboardDto.model';
import { BaseApiService } from './baseApiService';

@Injectable({
  providedIn: 'root'
})
export class ExpenseService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  getAll(): Observable<ListResponseModel<Expense>> {
    return this.httpClient.get<ListResponseModel<Expense>>(`${this.apiUrl}expenses/getall`);
  }

  getById(id: number): Observable<SingleResponseModel<Expense>> {
    return this.httpClient.get<SingleResponseModel<Expense>>(`${this.apiUrl}expenses/getbyid?id=${id}`);
  }

  add(expense: Expense): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(`${this.apiUrl}expenses/add`, expense);
  }

  update(expense: Expense): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(`${this.apiUrl}expenses/update`, expense);
  }

  delete(id: number): Observable<ResponseModel> {
    // Backend controller'da HttpDelete kullanıldığı için method: 'delete' olmalı
    // Ancak mevcut projede delete için de post kullanılmış olabilir, kontrol etmek lazım.
    // Şimdilik backend controller'daki [HttpDelete] attribute'una uygun olarak delete kullanalım.
    // Eğer backend'de post kullanılıyorsa, burası post olmalı ve id body içinde gönderilmeli.
    return this.httpClient.delete<ResponseModel>(`${this.apiUrl}expenses/delete?id=${id}`);
  }

  getExpensesByDateRange(startDate: Date, endDate: Date): Observable<ListResponseModel<ExpenseDto>> {
    let params = new HttpParams()
      .set('startDate', startDate.toISOString()) // Tarihleri ISO formatında göndermek genellikle daha güvenlidir
      .set('endDate', endDate.toISOString());
    return this.httpClient.get<ListResponseModel<ExpenseDto>>(`${this.apiUrl}expenses/getbydaterange`, { params });
  }

  getMonthlyExpenses(year: number, month: number): Observable<ListResponseModel<ExpenseDto>> {
    let params = new HttpParams()
      .set('year', year.toString())
      .set('month', month.toString());
    return this.httpClient.get<ListResponseModel<ExpenseDto>>(`${this.apiUrl}expenses/getmonthlyexpenses`, { params });
  }

   getTotalMonthlyExpenses(year: number, month: number): Observable<SingleResponseModel<number>> {
    let params = new HttpParams()
      .set('year', year.toString())
      .set('month', month.toString());
    return this.httpClient.get<SingleResponseModel<number>>(`${this.apiUrl}expenses/gettotalmonthlyexpenses`, { params });
  }

  getTotalDailyExpenses(date: Date): Observable<SingleResponseModel<number>> {
    // Tarihi YYYY-MM-DD formatında gönderelim (veya backend'in beklediği formatta)
    const dateString = date.toISOString().split('T')[0];
    let params = new HttpParams().set('date', dateString);
    return this.httpClient.get<SingleResponseModel<number>>(`${this.apiUrl}expenses/gettotaldailyexpenses`, { params });
  }

  getTotalYearlyExpenses(year: number): Observable<SingleResponseModel<number>> {
    let params = new HttpParams().set('year', year.toString());
    return this.httpClient.get<SingleResponseModel<number>>(`${this.apiUrl}expenses/gettotalyearlyexpenses`, { params });
  }

  getMonthlyExpenseSummary(year: number): Observable<SingleResponseModel<{[key: number]: number}>> {
    let params = new HttpParams().set('year', year.toString());
    return this.httpClient.get<SingleResponseModel<{[key: number]: number}>>(`${this.apiUrl}expenses/getmonthlyexpensesummary`, { params });
  }

  // Dashboard için optimize edilmiş tek API çağrısı - 5 API isteği yerine 1 API isteği
  getDashboardData(year: number, month: number): Observable<SingleResponseModel<ExpenseDashboardDto>> {
    let params = new HttpParams()
      .set('year', year.toString())
      .set('month', month.toString());
    return this.httpClient.get<SingleResponseModel<ExpenseDashboardDto>>(`${this.apiUrl}expenses/getdashboarddata`, { params });
  }
}