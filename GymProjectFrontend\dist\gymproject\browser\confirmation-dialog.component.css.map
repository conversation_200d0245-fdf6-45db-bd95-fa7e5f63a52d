{"version": 3, "sources": ["angular:styles/component:css;d4b839e935d56c4d5ee1626e0c198c4a90a3f7d98f7d37873c54b976e750fa7d;C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectFrontend\\src\\app\\components\\confirmation-dialog\\confirmation-dialog.component.html"], "sourcesContent": ["\n  .modern-dialog {\n    max-width: 400px;\n    width: auto;\n    background-color: var(--bg-primary);\n    border-radius: var(--border-radius-lg);\n    box-shadow: var(--shadow-lg);\n    overflow: hidden;\n  }\n\n  .modern-dialog-header {\n    padding: var(--spacing-md) var(--spacing-lg);\n    border-bottom: 1px solid var(--border-color);\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    background-color: var(--bg-secondary);\n  }\n\n  .modern-dialog-title {\n    margin: 0;\n    font-size: 1.25rem;\n    font-weight: 600;\n  }\n\n  .modern-dialog-content {\n    padding: var(--spacing-lg);\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    text-align: center;\n  }\n\n  .modern-dialog-footer {\n    padding: var(--spacing-md) var(--spacing-lg);\n    border-top: 1px solid var(--border-color);\n    background-color: var(--bg-secondary);\n    display: flex;\n    justify-content: flex-end;\n    gap: var(--spacing-sm);\n  }\n\n  .confirmation-icon {\n    width: 70px;\n    height: 70px;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-bottom: var(--spacing-md);\n  }\n\n  .confirmation-icon i {\n    font-size: 2rem;\n    color: white;\n  }\n\n  .confirmation-icon.warning {\n    background-color: var(--warning);\n  }\n\n  .confirmation-icon.danger {\n    background-color: var(--danger);\n  }\n\n  .confirmation-icon.info {\n    background-color: var(--info);\n  }\n\n  .confirmation-icon.success {\n    background-color: var(--success);\n  }\n\n  .confirmation-message {\n    font-size: 1.1rem;\n    margin-bottom: var(--spacing-md);\n    line-height: 1.5;\n    white-space: pre-line;\n  }\n"], "mappings": ";AACE,CAAC;AACC,aAAW;AACX,SAAO;AACP,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,YAAU;AACZ;AAEA,CAAC;AACC,WAAS,IAAI,cAAc,IAAI;AAC/B,iBAAe,IAAI,MAAM,IAAI;AAC7B,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,UAAQ;AACR,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,WAAS,IAAI;AACb,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,cAAY;AACd;AAEA,CAAC;AACC,WAAS,IAAI,cAAc,IAAI;AAC/B,cAAY,IAAI,MAAM,IAAI;AAC1B,oBAAkB,IAAI;AACtB,WAAS;AACT,mBAAiB;AACjB,OAAK,IAAI;AACX;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,iBAAe,IAAI;AACrB;AAEA,CAVC,kBAUkB;AACjB,aAAW;AACX,SAAO;AACT;AAEA,CAfC,iBAeiB,CAAC;AACjB,oBAAkB,IAAI;AACxB;AAEA,CAnBC,iBAmBiB,CAAC;AACjB,oBAAkB,IAAI;AACxB;AAEA,CAvBC,iBAuBiB,CAAC;AACjB,oBAAkB,IAAI;AACxB;AAEA,CA3BC,iBA2BiB,CAAC;AACjB,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,aAAW;AACX,iBAAe,IAAI;AACnB,eAAa;AACb,eAAa;AACf;", "names": []}