﻿using Business.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserLicensesController : ControllerBase
    {
        private readonly IUserLicenseService _userLicenseService;

        public UserLicensesController(IUserLicenseService userLicenseService)
        {
            _userLicenseService = userLicenseService;
        }

        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _userLicenseService.GetAll();
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getbyid")]
        public IActionResult GetById(int id)
        {
            var result = _userLicenseService.GetById(id);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getactivebyuserid")]
        public IActionResult GetActiveByUserId(int userId)
        {
            var result = _userLicenseService.GetActiveByUserId(userId);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getuserroles")]
        public IActionResult GetUserRoles(int userId)
        {
            var result = _userLicenseService.GetUserRoles(userId);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("add")]
        public IActionResult Add(UserLicense userLicense)
        {
            var result = _userLicenseService.Add(userLicense);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("update")]
        public IActionResult Update(UserLicense userLicense)
        {
            var result = _userLicenseService.Update(userLicense);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpDelete("delete")]
        public IActionResult Delete(int id)
        {
            var result = _userLicenseService.Delete(id);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("purchase")]
        public IActionResult Purchase(LicensePurchaseDto licensePurchaseDto)
        {
            var result = _userLicenseService.Purchase(licensePurchaseDto);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("extend")]
        public IActionResult ExtendLicense(int userLicenseId, int extensionDays)
        {
            var result = _userLicenseService.ExtendLicense(userLicenseId, extensionDays);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("revoke")]
        public IActionResult RevokeLicense(int userLicenseId)
        {
            var result = _userLicenseService.RevokeLicense(userLicenseId);
            return result.Success ? Ok(result) : BadRequest(result);
        }
    }
}