{"version": 3, "sources": ["src/app/components/expense-management/expense-management.component.css"], "sourcesContent": ["/* Loading Spinner Overlay */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  /* Use CSS variable for background with opacity */\r\n  background-color: rgba(var(--bg-primary-rgb, 255, 255, 255), 0.7);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(3px); /* Optional blur effect */\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.spinner-container {\r\n  text-align: center;\r\n  animation: zoomIn 0.5s ease-out; /* Add animation */\r\n}\r\n\r\n/* Content Blur when Loading */\r\n.content-blur {\r\n  filter: blur(5px);\r\n  transition: filter 0.3s ease;\r\n}\r\n\r\n/* Modern Stats Card Overrides/Specifics */\r\n.modern-stats-card {\r\n  /* Base styles come from modern-components.css */\r\n  /* Add specific background/icon colors */\r\n  transition: all var(--transition-speed) var(--transition-timing); /* Ensure transition */\r\n}\r\n\r\n.modern-stats-card:hover {\r\n   transform: translateY(-5px);\r\n   box-shadow: var(--shadow-md);\r\n}\r\n\r\n.modern-stats-icon {\r\n   /* Base styles come from modern-components.css */\r\n   color: var(--white); /* Ensure icon color is white */\r\n   box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\r\n   transition: transform var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.modern-stats-card:hover .modern-stats-icon {\r\n    transform: scale(1.1);\r\n}\r\n\r\n.modern-stats-value {\r\n    /* Base styles come from modern-components.css */\r\n    transition: color var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n/* Daily Expense Card (Example: Info color) */\r\n.daily-expense-card {\r\n  background: linear-gradient(135deg, var(--info-light) 0%, rgba(var(--info-rgb), 0.2) 100%);\r\n}\r\n.daily-expense-card .modern-stats-icon {\r\n  background: linear-gradient(135deg, var(--info) 0%, #0b7b9a 100%); /* Example dark info */\r\n}\r\n.daily-expense-card .modern-stats-value {\r\n  color: var(--info);\r\n}\r\n\r\n/* Monthly Expense Card (Example: Primary color) */\r\n.monthly-expense-card {\r\n  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(var(--primary-rgb), 0.2) 100%);\r\n}\r\n.monthly-expense-card .modern-stats-icon {\r\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\r\n}\r\n.monthly-expense-card .modern-stats-value {\r\n  color: var(--primary);\r\n}\r\n\r\n/* Yearly Expense Card (Example: Success color) */\r\n.yearly-expense-card {\r\n  background: linear-gradient(135deg, var(--success-light) 0%, rgba(var(--success-rgb), 0.2) 100%);\r\n}\r\n.yearly-expense-card .modern-stats-icon {\r\n  background: linear-gradient(135deg, var(--success) 0%, #1a6e2f 100%); /* Example dark success */\r\n}\r\n.yearly-expense-card .modern-stats-value {\r\n  color: var(--success);\r\n}\r\n\r\n/*  Eski kart stilleri yorum satırı içinde kalabilir veya tamamen silinebilir */\r\n/*\r\n.count-expense-card { ... }\r\n.avg-expense-card { ... }\r\n.total-expense-card { ... }\r\n*/\r\n\r\n/* Filter Icon Style */\r\n.filter-icon {\r\n  color: var(--text-secondary);\r\n  font-size: 1rem; /* Stil tanımı eklendi */\r\n}\r\n\r\n/* Chart Container */\r\n.chart-container {\r\n  position: relative;\r\n  margin: auto;\r\n  border-radius: var(--border-radius-md);\r\n  padding: var(--spacing-sm); /* Reduced padding */\r\n  background-color: rgba(var(--secondary-rgb), 0.05); /* Subtle background */\r\n  box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.04);\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n  min-height: 300px; /* Ensure container has height */\r\n  display: flex; /* Center empty state */\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.chart-container:hover {\r\n  box-shadow: inset 0 0 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.chart-empty-state {\r\n  color: var(--text-secondary);\r\n  font-style: italic;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n\r\n/* Filter Icon Style */\r\n.filter-icon {\r\n  color: var(--text-secondary);\r\n  font-size: 1rem;\r\n}\r\n\r\n/* Active Filters */\r\n.active-filters {\r\n  margin-bottom: var(--spacing-md);\r\n  padding: var(--spacing-sm) var(--spacing-md);\r\n  background-color: rgba(var(--primary-rgb), 0.05);\r\n  border-radius: var(--border-radius-md);\r\n  border: 1px solid rgba(var(--primary-rgb), 0.1);\r\n}\r\n\r\n.filter-badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(var(--primary-rgb), 0.2) 100%);\r\n  color: var(--primary);\r\n  padding: 0.4rem 0.8rem;\r\n  border-radius: var(--border-radius-pill);\r\n  font-size: 0.85rem;\r\n  font-weight: 500;\r\n  box-shadow: var(--shadow-sm);\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.filter-badge:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.15);\r\n}\r\n\r\n.filter-badge .btn-close {\r\n  font-size: 0.65rem;\r\n  padding: 0.25rem;\r\n  margin-left: 0.5rem;\r\n  background-color: rgba(var(--primary-rgb), 0.2);\r\n  border-radius: 50%;\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n  opacity: 0.7;\r\n}\r\n\r\n.filter-badge .btn-close:hover {\r\n  background-color: rgba(var(--primary-rgb), 0.3);\r\n  transform: rotate(90deg);\r\n  opacity: 1;\r\n}\r\n\r\n\r\n/* Empty State Styles */\r\n.empty-state {\r\n  padding: var(--spacing-xl) var(--spacing-lg);\r\n  text-align: center;\r\n  background-color: var(--bg-secondary);\r\n  border-radius: var(--border-radius-lg);\r\n  margin: var(--spacing-md) 0;\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n  border: 1px dashed var(--border-color);\r\n}\r\n\r\n.empty-state i,\r\n.empty-state fa-icon {\r\n  font-size: 3rem;\r\n  color: var(--text-secondary);\r\n  opacity: 0.6;\r\n  margin-bottom: var(--spacing-md);\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.empty-state:hover i,\r\n.empty-state:hover fa-icon {\r\n  transform: scale(1.1) rotate(-5deg);\r\n  opacity: 0.8;\r\n}\r\n\r\n.empty-state h5 {\r\n  font-size: 1.3rem;\r\n  font-weight: 600;\r\n  margin-bottom: var(--spacing-sm);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.empty-state p {\r\n  color: var(--text-secondary);\r\n  font-size: 1rem;\r\n  max-width: 85%;\r\n  margin: 0 auto var(--spacing-md);\r\n}\r\n\r\n.empty-state .modern-btn {\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n  padding: 0.6rem 1.5rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.empty-state .modern-btn:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n/* Table Specifics */\r\n.modern-table td .modern-badge {\r\n  min-width: 80px; /* Ensure badges have some minimum width */\r\n  text-align: center;\r\n}\r\n\r\n.modern-table td[style*=\"font-weight: 700\"] {\r\n   letter-spacing: 0.5px; /* Add slight spacing to amount */\r\n}\r\n\r\n/* Staggered animation for table rows */\r\n.staggered-item {\r\n  animation: fadeInTableRow 0.6s ease-out forwards;\r\n  opacity: 0;\r\n}\r\n\r\n@keyframes fadeInTableRow {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(15px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Ensure icon margins are consistent */\r\n.modern-btn-icon {\r\n    margin-right: 0.5rem;\r\n}\r\n.modern-btn-icon-only fa-icon,\r\n.modern-btn-icon-only i {\r\n    margin-right: 0; /* No margin for icon-only buttons */\r\n}\r\n\r\n/* Dark Mode Adjustments (if needed beyond modern-components.css) */\r\n[data-theme=\"dark\"] .chart-container {\r\n   background-color: rgba(var(--secondary-rgb), 0.1); /* Darker subtle background */\r\n   box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n[data-theme=\"dark\"] .active-filters {\r\n  background-color: rgba(var(--primary-rgb), 0.1);\r\n  border-color: rgba(var(--primary-rgb), 0.2);\r\n}\r\n\r\n[data-theme=\"dark\"] .filter-badge {\r\n   background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.15) 0%, rgba(var(--primary-rgb), 0.25) 100%);\r\n   color: var(--primary); /* Primary color should be adjusted for dark mode in variables */\r\n}\r\n\r\n[data-theme=\"dark\"] .filter-badge .btn-close {\r\n   background-color: rgba(var(--primary-rgb), 0.3);\r\n}\r\n[data-theme=\"dark\"] .filter-badge .btn-close:hover {\r\n   background-color: rgba(var(--primary-rgb), 0.4);\r\n}\r\n\r\n/* Search Input Styles (from member.component.css) */\r\n/* Search Input (member.component.css'den alındı) */\r\n.search-input-container {\r\n  position: relative;\r\n  /* margin-bottom: 1rem; */ /* Header içindeki diğer filtrelerle hizalama için kaldırıldı */\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: 1rem; /* member.component.css'deki değer */\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: var(--text-muted); /* member.component.css'deki değer */\r\n  /* z-index, font-size, line-height kaldırıldı, gerekirse eklenebilir */\r\n}\r\n\r\n.search-input {\r\n  width: 100%; /* Kapsayıcı genişliğini doldurması için */\r\n  padding: 0.75rem 1rem 0.75rem 2.5rem; /* member.component.css'deki padding (ikon için sol padding dahil) */\r\n  border-radius: 0.5rem; /* member.component.css'deki değer */\r\n  border: 1px solid var(--border-color); /* member.component.css'deki değer */\r\n  background-color: var(--input-bg); /* member.component.css'deki değer */\r\n  color: var(--input-text); /* member.component.css'deki değer */\r\n  transition: all 0.3s ease; /* member.component.css'deki değer */\r\n  /* height, line-height kaldırıldı, padding ile ayarlanıyor */\r\n}\r\n\r\n.search-input:focus {\r\n  border-color: var(--primary-color); /* member.component.css'deki değer */\r\n  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25); /* member.component.css'deki değer */\r\n  outline: none;\r\n}\r\n"], "mappings": ";AACA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AAER,oBAAkB,KAAK,IAAI,gBAAgB,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE;AAC7D,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACtB,cAAY,QAAQ,KAAK;AAC3B;AAEA,CAAC;AACC,cAAY;AACZ,aAAW,OAAO,KAAK;AACzB;AAGA,CAAC;AACC,UAAQ,KAAK;AACb,cAAY,OAAO,KAAK;AAC1B;AAGA,CAAC;AAGC,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CANC,iBAMiB;AACf,aAAW,WAAW;AACtB,cAAY,IAAI;AACnB;AAEA,CAAC;AAEE,SAAO,IAAI;AACX,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACrC,cAAY,UAAU,IAAI,oBAAoB,IAAI;AACrD;AAEA,CAlBC,iBAkBiB,OAAO,CAPxB;AAQG,aAAW,MAAM;AACrB;AAEA,CAAC;AAEG,cAAY,MAAM,IAAI,oBAAoB,IAAI;AAClD;AAGA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,cAAc,EAAE;AAAA,MAAE,KAAK,IAAI,WAAW,EAAE,KAAK;AACvF;AACA,CAHC,mBAGmB,CApBnB;AAqBC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,QAAQ,EAAE;AAAA,MAAE,QAAQ;AAC9D;AACA,CANC,mBAMmB,CAZnB;AAaC,SAAO,IAAI;AACb;AAGA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,iBAAiB,EAAE;AAAA,MAAE,KAAK,IAAI,cAAc,EAAE,KAAK;AAC7F;AACA,CAHC,qBAGqB,CA/BrB;AAgCC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,IAAI,gBAAgB;AAC7E;AACA,CANC,qBAMqB,CAvBrB;AAwBC,SAAO,IAAI;AACb;AAGA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,iBAAiB,EAAE;AAAA,MAAE,KAAK,IAAI,cAAc,EAAE,KAAK;AAC7F;AACA,CAHC,oBAGoB,CA1CpB;AA2CC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,QAAQ;AACjE;AACA,CANC,oBAMoB,CAlCpB;AAmCC,SAAO,IAAI;AACb;AAUA,CAAC;AACC,SAAO,IAAI;AACX,aAAW;AACb;AAGA,CAAC;AACC,YAAU;AACV,UAAQ;AACR,iBAAe,IAAI;AACnB,WAAS,IAAI;AACb,oBAAkB,KAAK,IAAI,gBAAgB,EAAE;AAC7C,cAAY,MAAM,EAAE,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACxC,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,cAAY;AACZ,WAAS;AACT,eAAa;AACb,mBAAiB;AACnB;AAEA,CAdC,eAce;AACd,cAAY,MAAM,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC3C;AAEA,CAAC;AACC,SAAO,IAAI;AACX,cAAY;AACZ,aAAW;AACb;AAIA,CAhCC;AAiCC,SAAO,IAAI;AACX,aAAW;AACb;AAGA,CAAC;AACC,iBAAe,IAAI;AACnB,WAAS,IAAI,cAAc,IAAI;AAC/B,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,iBAAe,IAAI;AACnB,UAAQ,IAAI,MAAM,KAAK,IAAI,cAAc,EAAE;AAC7C;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,iBAAiB,EAAE;AAAA,MAAE,KAAK,IAAI,cAAc,EAAE,KAAK;AAC3F,SAAO,IAAI;AACX,WAAS,OAAO;AAChB,iBAAe,IAAI;AACnB,aAAW;AACX,eAAa;AACb,cAAY,IAAI;AAChB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAbC,YAaY;AACX,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,IAAI,cAAc,EAAE;AACjD;AAEA,CAlBC,aAkBa,CAAC;AACb,aAAW;AACX,WAAS;AACT,eAAa;AACb,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,iBAAe;AACf,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,WAAS;AACX;AAEA,CA5BC,aA4Ba,CAVC,SAUS;AACtB,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,aAAW,OAAO;AAClB,WAAS;AACX;AAIA,CAAC;AACC,WAAS,IAAI,cAAc,IAAI;AAC/B,cAAY;AACZ,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,UAAQ,IAAI,cAAc;AAC1B,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,UAAQ,IAAI,OAAO,IAAI;AACzB;AAEA,CAVC,YAUY;AACb,CAXC,YAWY;AACX,aAAW;AACX,SAAO,IAAI;AACX,WAAS;AACT,iBAAe,IAAI;AACnB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAnBC,WAmBW,OAAO;AACnB,CApBC,WAoBW,OAAO;AACjB,aAAW,MAAM,KAAK,OAAO;AAC7B,WAAS;AACX;AAEA,CAzBC,YAyBY;AACX,aAAW;AACX,eAAa;AACb,iBAAe,IAAI;AACnB,SAAO,IAAI;AACb;AAEA,CAhCC,YAgCY;AACX,SAAO,IAAI;AACX,aAAW;AACX,aAAW;AACX,UAAQ,EAAE,KAAK,IAAI;AACrB;AAEA,CAvCC,YAuCY,CAAC;AACZ,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,WAAS,OAAO;AAChB,eAAa;AACf;AAEA,CA7CC,YA6CY,CANC,UAMU;AACtB,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAGA,CAAC,aAAa,GAAG,CAAC;AAChB,aAAW;AACX,cAAY;AACd;AAEA,CALC,aAKa,EAAE,CAAC;AACd,kBAAgB;AACnB;AAGA,CAAC;AACC,aAAW,eAAe,KAAK,SAAS;AACxC,WAAS;AACX;AAEA,WAJa;AAKX;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACA;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACF;AAGA,CAAC;AACG,gBAAc;AAClB;AACA,CAAC,qBAAqB;AACtB,CADC,qBACqB;AAClB,gBAAc;AAClB;AAGA,CAAC,iBAAmB,CAnKnB;AAoKE,oBAAkB,KAAK,IAAI,gBAAgB,EAAE;AAC7C,cAAY,MAAM,EAAE,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC3C;AAEA,CAAC,iBAAmB,CAxInB;AAyIC,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,gBAAc,KAAK,IAAI,cAAc,EAAE;AACzC;AAEA,CAAC,iBAAmB,CArInB;AAsIE;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,KAAK,IAAI,cAAc,EAAE,MAAM,EAAvD;AAAA,MAA2D,KAAK,IAAI,cAAc,EAAE,MAAM;AACtG,SAAO,IAAI;AACd;AAEA,CAAC,iBAAmB,CA1InB,aA0IiC,CAxHnB;AAyHZ,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC9C;AACA,CAAC,iBAAmB,CA7InB,aA6IiC,CA3HnB,SA2H6B;AACzC,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC9C;AAIA,CAAC;AACC,YAAU;AAEZ;AAEA,CAAC;AACC,YAAU;AACV,QAAM;AACN,OAAK;AACL,aAAW,WAAW;AACtB,SAAO,IAAI;AAEb;AAEA,CAAC;AACC,SAAO;AACP,WAAS,QAAQ,KAAK,QAAQ;AAC9B,iBAAe;AACf,UAAQ,IAAI,MAAM,IAAI;AACtB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,cAAY,IAAI,KAAK;AAEvB;AAEA,CAXC,YAWY;AACX,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,OAAO,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AAC3C,WAAS;AACX;", "names": []}