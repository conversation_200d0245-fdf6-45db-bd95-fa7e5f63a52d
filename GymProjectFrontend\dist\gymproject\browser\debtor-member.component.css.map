{"version": 3, "sources": ["src/app/components/debtor-member/debtor-member.component.css"], "sourcesContent": ["/* Debtor Member Component Styles */\r\n\r\n/* Loading Spinner */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(255, 255, 255, 0.8);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(3px);\r\n}\r\n\r\n.spinner-container {\r\n  text-align: center;\r\n}\r\n\r\n/* Content Blur */\r\n.content-blur {\r\n  filter: blur(3px);\r\n  pointer-events: none;\r\n}\r\n\r\n/* Fade In Animation */\r\n.fade-in {\r\n  animation: fadeIn 0.5s ease-out;\r\n}\r\n\r\n/* Modern Stats Cards */\r\n.modern-stats-card {\r\n  border-radius: var(--border-radius-lg);\r\n  padding: 1.5rem;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.modern-stats-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.modern-stats-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 1.5rem;\r\n  margin-right: 1rem;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  flex-shrink: 0;\r\n}\r\n\r\n.modern-stats-info {\r\n  flex-grow: 1;\r\n}\r\n\r\n.modern-stats-value {\r\n  font-size: 1.75rem;\r\n  font-weight: 700;\r\n  margin-bottom: 0.25rem;\r\n}\r\n\r\n.modern-stats-label {\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n/* Background Gradients */\r\n.bg-danger-gradient {\r\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\r\n  color: white;\r\n}\r\n\r\n.bg-warning-gradient {\r\n  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);\r\n  color: white;\r\n}\r\n\r\n.bg-info-gradient {\r\n  background: linear-gradient(135deg, #0dcaf0 0%, #0097b2 100%);\r\n  color: white;\r\n}\r\n\r\n/* Member Avatar */\r\n.member-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.member-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-weight: 600;\r\n  margin-right: 0.75rem;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* Progress Bar */\r\n.progress-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.progress {\r\n  flex-grow: 1;\r\n  height: 8px;\r\n  background-color: #e9ecef;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-bar {\r\n  height: 100%;\r\n  border-radius: 4px;\r\n  transition: width 0.6s ease;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 0.875rem;\r\n  font-weight: 600;\r\n  min-width: 40px;\r\n  text-align: right;\r\n}\r\n\r\n/* Search Box */\r\n.search-box {\r\n  max-width: 300px;\r\n}\r\n\r\n/* Dark mode support for search box */\r\n[data-theme=\"dark\"] .search-box .input-group-text {\r\n  background-color: var(--bg-tertiary);\r\n  color: var(--text-primary);\r\n  border-color: var(--border-color);\r\n}\r\n\r\n[data-theme=\"dark\"] .search-box .form-control {\r\n  background-color: var(--input-bg);\r\n  color: var(--input-text);\r\n  border-color: var(--border-color);\r\n}\r\n\r\n[data-theme=\"dark\"] .search-box .form-control::placeholder {\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n/* Table Styles */\r\n.modern-table {\r\n  width: 100%;\r\n  border-collapse: separate;\r\n  border-spacing: 0;\r\n}\r\n\r\n.modern-table th {\r\n  background-color: var(--bg-secondary);\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  font-size: 0.75rem;\r\n  letter-spacing: 0.5px;\r\n  padding: 1rem;\r\n  border-bottom: 2px solid var(--border-color);\r\n}\r\n\r\n.modern-table td {\r\n  padding: 1rem;\r\n  vertical-align: middle;\r\n  border-bottom: 1px solid var(--border-color);\r\n}\r\n\r\n.modern-table tbody tr {\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.modern-table tbody tr:hover {\r\n  background-color: var(--primary-light);\r\n}\r\n\r\n/* Empty State */\r\n.empty-state {\r\n  padding: 3rem 1rem;\r\n  text-align: center;\r\n  color: var(--text-secondary);\r\n}\r\n\r\n/* Animations */\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(10px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n/* Dark Mode Support */\r\n[data-theme=\"dark\"] .loading-overlay {\r\n  background-color: rgba(18, 18, 18, 0.8);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-table th {\r\n  background-color: var(--bg-tertiary);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-table tbody tr:hover {\r\n  background-color: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n[data-theme=\"dark\"] .progress {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n/* Responsive Adjustments */\r\n@media (max-width: 767.98px) {\r\n  .modern-stats-card {\r\n    margin-bottom: 1rem;\r\n  }\r\n  \r\n  .modern-stats-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n    font-size: 1.25rem;\r\n  }\r\n  \r\n  .modern-stats-value {\r\n    font-size: 1.5rem;\r\n  }\r\n  \r\n  .header-actions {\r\n    margin-top: 1rem;\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-box {\r\n    max-width: 100%;\r\n    width: 100%;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .progress-container {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 5px;\r\n  }\r\n  \r\n  .progress-text {\r\n    text-align: left;\r\n  }\r\n}\r\n"], "mappings": ";AAGA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACxB;AAEA,CAAC;AACC,cAAY;AACd;AAGA,CAAC;AACC,UAAQ,KAAK;AACb,kBAAgB;AAClB;AAGA,CAAC;AACC,aAAW,OAAO,KAAK;AACzB;AAGA,CAAC;AACC,iBAAe,IAAI;AACnB,WAAS;AACT,UAAQ;AACR,WAAS;AACT,eAAa;AACb,cAAY,IAAI,KAAK;AACrB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACrC,iBAAe;AACjB;AAEA,CAXC,iBAWiB;AAChB,aAAW,WAAW;AACtB,cAAY,EAAE,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACxC;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW;AACX,gBAAc;AACd,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,eAAa;AACf;AAEA,CAAC;AACC,aAAW;AACb;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,iBAAe;AACjB;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,iBAAe;AACjB;AAGA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACT;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACT;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACT;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACf;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,eAAa;AACb,gBAAc;AACd,eAAa;AACf;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,aAAW;AACX,UAAQ;AACR,oBAAkB;AAClB,iBAAe;AACf,YAAU;AACZ;AAEA,CAAC;AACC,UAAQ;AACR,iBAAe;AACf,cAAY,MAAM,KAAK;AACzB;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,aAAW;AACX,cAAY;AACd;AAGA,CAAC;AACC,aAAW;AACb;AAGA,CAAC,iBAAmB,CALnB,WAK+B,CAAC;AAC/B,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAXnB,WAW+B,CAAC;AAC/B,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAjBnB,WAiB+B,CANC,YAMY;AAC3C,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAGA,CAAC;AACC,SAAO;AACP,mBAAiB;AACjB,kBAAgB;AAClB;AAEA,CANC,aAMa;AACZ,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,eAAa;AACb,kBAAgB;AAChB,aAAW;AACX,kBAAgB;AAChB,WAAS;AACT,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CAjBC,aAiBa;AACZ,WAAS;AACT,kBAAgB;AAChB,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CAvBC,aAuBa,MAAM;AAClB,cAAY,iBAAiB,KAAK;AACpC;AAEA,CA3BC,aA2Ba,MAAM,EAAE;AACpB,oBAAkB,IAAI;AACxB;AAGA,CAAC;AACC,WAAS,KAAK;AACd,cAAY;AACZ,SAAO,IAAI;AACb;AAGA,WApLa;AAqLX;AAAO,aAAS;AAAG,eAAW,WAAW;AAAO;AAChD;AAAK,aAAS;AAAG,eAAW,WAAW;AAAI;AAC7C;AAGA,CAAC,iBAAmB,CApNnB;AAqNC,oBAAkB,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACrC;AAEA,CAAC,iBAAmB,CAjDnB,aAiDiC;AAChC,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CArDnB,aAqDiC,MAAM,EAAE;AACxC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,CAAC,iBAAmB,CA3GnB;AA4GC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAxMD;AAyMG,mBAAe;AACjB;AAEA,GA5LD;AA6LG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AAEA,GAjLD;AAkLG,eAAW;AACb;AAEA,GApFD;AAqFG,gBAAY;AACZ,WAAO;AACT;AAEA,GA9GD;AA+GG,eAAW;AACX,WAAO;AACT;AAEA,GAAC;AACC,oBAAgB;AAChB,iBAAa;AACf;AAEA,GApJD;AAqJG,oBAAgB;AAChB,iBAAa;AACb,SAAK;AACP;AAEA,GAtID;AAuIG,gBAAY;AACd;AACF;", "names": []}