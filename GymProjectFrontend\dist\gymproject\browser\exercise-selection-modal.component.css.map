{"version": 3, "sources": ["src/app/components/workout-programs/exercise-selection-modal.component.css"], "sourcesContent": ["/* Exercise Selection Modal Specific Styles */\n\n.modal-header {\n  padding: 1.5rem;\n  border-bottom: 1px solid var(--border-color);\n  background-color: var(--bg-secondary);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.modal-title {\n  margin: 0;\n  font-weight: 600;\n  color: var(--text-primary);\n}\n\n.btn-close {\n  background: none;\n  border: none;\n  font-size: 1.25rem;\n  color: var(--text-secondary);\n  cursor: pointer;\n  padding: 0.5rem;\n  border-radius: var(--border-radius-md);\n  transition: all var(--transition-speed) var(--transition-timing);\n}\n\n.btn-close:hover {\n  background-color: var(--danger-light);\n  color: var(--danger);\n}\n\n.modal-body {\n  padding: 1.5rem;\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n.modal-footer {\n  padding: 1rem 1.5rem;\n  border-top: 1px solid var(--border-color);\n  background-color: var(--bg-secondary);\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.75rem;\n}\n\n.filters-section {\n  padding: 1rem;\n  background-color: var(--bg-secondary);\n  border-radius: var(--border-radius-lg);\n  border: 1px solid var(--border-color);\n}\n\n.exercises-list {\n  max-height: 400px;\n  overflow-y: auto;\n  border: 1px solid var(--border-color);\n  border-radius: var(--border-radius-lg);\n}\n\n.exercise-item {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  border-bottom: 1px solid var(--border-color);\n  cursor: pointer;\n  transition: all var(--transition-speed) var(--transition-timing);\n  position: relative;\n}\n\n.exercise-item:last-child {\n  border-bottom: none;\n}\n\n.exercise-item:hover {\n  background-color: var(--bg-secondary);\n}\n\n.exercise-item.selected {\n  background-color: var(--primary-light);\n  border-color: var(--primary);\n}\n\n.exercise-content {\n  flex: 1;\n}\n\n.exercise-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 0.5rem;\n}\n\n.exercise-name {\n  font-weight: 600;\n  font-size: 1rem;\n  color: var(--text-primary);\n}\n\n.exercise-badges {\n  display: flex;\n  gap: 0.5rem;\n  flex-shrink: 0;\n}\n\n.exercise-details {\n  font-size: 0.875rem;\n  color: var(--text-secondary);\n}\n\n.exercise-category {\n  font-weight: 500;\n  color: var(--primary);\n  margin-bottom: 0.25rem;\n}\n\n.exercise-description {\n  margin-bottom: 0.25rem;\n  line-height: 1.4;\n}\n\n.exercise-muscles,\n.exercise-equipment {\n  margin-bottom: 0.25rem;\n}\n\n.exercise-select-indicator {\n  position: absolute;\n  top: 0.5rem;\n  right: 0.5rem;\n  width: 24px;\n  height: 24px;\n  background-color: var(--primary);\n  color: var(--white);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.75rem;\n}\n\n.pagination-section {\n  border-top: 1px solid var(--border-color);\n  padding-top: 1rem;\n}\n\n.pagination .page-link {\n  color: var(--primary);\n  background-color: var(--bg-primary);\n  border: 1px solid var(--border-color);\n  padding: 0.5rem 0.75rem;\n  margin: 0 0.125rem;\n  border-radius: var(--border-radius-md);\n  transition: all var(--transition-speed) var(--transition-timing);\n}\n\n.pagination .page-link:hover {\n  color: var(--white);\n  background-color: var(--primary);\n  border-color: var(--primary);\n  transform: translateY(-1px);\n}\n\n.pagination .page-item.active .page-link {\n  color: var(--white);\n  background-color: var(--primary);\n  border-color: var(--primary);\n}\n\n.pagination .page-item.disabled .page-link {\n  color: var(--text-secondary);\n  background-color: var(--bg-secondary);\n  border-color: var(--border-color);\n  cursor: not-allowed;\n}\n\n.spinner-border {\n  width: 3rem;\n  height: 3rem;\n}\n\n/* Badge Styles */\n.modern-badge-secondary {\n  background-color: var(--secondary-light);\n  color: var(--secondary);\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .modal-header,\n  .modal-body,\n  .modal-footer {\n    padding: 1rem;\n  }\n  \n  .exercise-item {\n    padding: 0.75rem;\n  }\n  \n  .exercise-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n  \n  .exercise-badges {\n    align-self: flex-start;\n  }\n  \n  .modal-footer {\n    flex-direction: column;\n  }\n  \n  .modal-footer .modern-btn {\n    width: 100%;\n  }\n  \n  .exercises-list {\n    max-height: 300px;\n  }\n}\n\n/* Dark mode specific adjustments */\n[data-theme=\"dark\"] .modal-header {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .modal-footer {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .filters-section {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .exercises-list {\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .exercise-item {\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .exercise-item:hover {\n  background-color: var(--bg-tertiary);\n}\n\n[data-theme=\"dark\"] .exercise-item.selected {\n  background-color: var(--primary-light);\n  border-color: var(--primary);\n}\n\n[data-theme=\"dark\"] .btn-close:hover {\n  background-color: var(--danger-light);\n  color: var(--danger);\n}\n\n[data-theme=\"dark\"] .pagination .page-link {\n  background-color: var(--bg-secondary);\n  border-color: var(--border-color);\n  color: var(--text-primary);\n}\n\n[data-theme=\"dark\"] .pagination .page-link:hover {\n  background-color: var(--primary);\n  border-color: var(--primary);\n  color: var(--white);\n}\n\n[data-theme=\"dark\"] .pagination .page-item.disabled .page-link {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n  color: var(--text-secondary);\n}\n"], "mappings": ";AAEA,CAAC;AACC,WAAS;AACT,iBAAe,IAAI,MAAM,IAAI;AAC7B,oBAAkB,IAAI;AACtB,WAAS;AACT,mBAAiB;AACjB,eAAa;AACf;AAEA,CAAC;AACC,UAAQ;AACR,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,aAAW;AACX,SAAO,IAAI;AACX,UAAQ;AACR,WAAS;AACT,iBAAe,IAAI;AACnB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAXC,SAWS;AACR,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS;AACT,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,WAAS,KAAK;AACd,cAAY,IAAI,MAAM,IAAI;AAC1B,oBAAkB,IAAI;AACtB,WAAS;AACT,mBAAiB;AACjB,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,cAAY;AACZ,cAAY;AACZ,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS;AACT,iBAAe,IAAI,MAAM,IAAI;AAC7B,UAAQ;AACR,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,YAAU;AACZ;AAEA,CAVC,aAUa;AACZ,iBAAe;AACjB;AAEA,CAdC,aAca;AACZ,oBAAkB,IAAI;AACxB;AAEA,CAlBC,aAkBa,CAAC;AACb,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC;AACC,QAAM;AACR;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,iBAAe;AACjB;AAEA,CAAC;AACC,eAAa;AACb,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,eAAa;AACf;AAEA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACX,iBAAe;AACjB;AAEA,CAAC;AACC,iBAAe;AACf,eAAa;AACf;AAEA,CAAC;AACD,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,YAAU;AACV,OAAK;AACL,SAAO;AACP,SAAO;AACP,UAAQ;AACR,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW;AACb;AAEA,CAAC;AACC,cAAY,IAAI,MAAM,IAAI;AAC1B,eAAa;AACf;AAEA,CAAC,WAAW,CAAC;AACX,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,UAAQ,IAAI,MAAM,IAAI;AACtB,WAAS,OAAO;AAChB,UAAQ,EAAE;AACV,iBAAe,IAAI;AACnB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAVC,WAUW,CAVC,SAUS;AACpB,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,aAAW,WAAW;AACxB;AAEA,CAjBC,WAiBW,CAAC,SAAS,CAAC,OAAO,CAjBjB;AAkBX,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAvBC,WAuBW,CANC,SAMS,CAAC,SAAS,CAvBnB;AAwBX,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,UAAQ;AACV;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACV;AAGA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA9LD;AAAA,EA+LC,CAhKD;AAAA,EAiKC,CA3JD;AA4JG,aAAS;AACX;AAEA,GAxID;AAyIG,aAAS;AACX;AAEA,GAjHD;AAkHG,oBAAgB;AAChB,iBAAa;AACb,SAAK;AACP;AAEA,GA1GD;AA2GG,gBAAY;AACd;AAEA,GA7KD;AA8KG,oBAAgB;AAClB;AAEA,GAjLD,aAiLe,CAAC;AACb,WAAO;AACT;AAEA,GArKD;AAsKG,gBAAY;AACd;AACF;AAGA,CAAC,iBAAmB,CAhOnB;AAiOC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAhMnB;AAiMC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CA5LnB;AA6LC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CA1LnB;AA2LC,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAvLnB;AAwLC,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CA3LnB,aA2LiC;AAChC,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CA/LnB,aA+LiC,CA7KnB;AA8Kb,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAjPnB,SAiP6B;AAC5B,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAlHnB,WAkH+B,CAlHnB;AAmHX,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAxHnB,WAwH+B,CAxHnB,SAwH6B;AACxC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA9HnB,WA8H+B,CA7GnB,SA6G6B,CAvGnB,SAuG6B,CA9HvC;AA+HX,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;", "names": []}