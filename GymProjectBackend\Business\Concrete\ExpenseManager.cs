using Business.Abstract;
using Business.Constants;
using Business.ValidationRules.FluentValidation; // Bunu daha sonra oluşturacağız
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Validation;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Logging; // İsteğe bağlı loglama
using Core.Aspects.Autofac.Performance; // İsteğe bağlı performans
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using Core.Utilities.Business; // BusinessRules için
using Business.BusinessAscpects.Autofac; // SecuredOperation için
using Core.Utilities.Security.CompanyContext; // ICompanyContext için
using DataAccess.Concrete.EntityFramework; // Direkt context kullanımı için

namespace Business.Concrete
{
    public class ExpenseManager : IExpenseService
    {
        private readonly IExpenseDal _expenseDal;
        private readonly ICompanyContext _companyContext;

        public ExpenseManager(IExpenseDal expenseDal, ICompanyContext companyContext)
        {
            _expenseDal = expenseDal;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [ValidationAspect(typeof(ExpenseValidator))] // Validator eklendi
        [SmartCacheRemoveAspect("Expense")]
        [TransactionScopeAspect]
        public IResult Add(Expense expense)
        {
            // Şirket ID'sini bağlamdan alıp entity'ye ata
            expense.CompanyID = _companyContext.GetCompanyId();

            // İş kuralları eklenebilir (örn: Amount negatif olamaz vb.)
            IResult ruleResult = BusinessRules.Run(CheckIfAmountIsPositive(expense.Amount));
            if (ruleResult != null)
            {
                return ruleResult;
            }

            _expenseDal.Add(expense);
            return new SuccessResult(Messages.ExpenseAdded);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [SmartCacheRemoveAspect("Expense")]
        [TransactionScopeAspect]
        public IResult Delete(int expenseId)
        {
            // Önce giderin mevcut şirkete ait olup olmadığını kontrol et
            var expenseToDelete = _expenseDal.Get(e => e.ExpenseID == expenseId && e.CompanyID == _companyContext.GetCompanyId());
            if (expenseToDelete == null)
            {
                return new ErrorResult(Messages.ExpenseNotFound);
            }

            _expenseDal.Delete(expenseId); // EfBaseRepository ID ile siliyor
            return new SuccessResult(Messages.ExpenseDeleted);
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 60, "Expense", "Business")]
        public IDataResult<List<Expense>> GetAll()
        {
            var companyId = _companyContext.GetCompanyId();
            return new SuccessDataResult<List<Expense>>(_expenseDal.GetAll(e => e.CompanyID == companyId && e.IsActive));
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 60, "Expense", "Details")]
        public IDataResult<Expense> GetById(int expenseId)
        {
            var companyId = _companyContext.GetCompanyId();
            var expense = _expenseDal.Get(e => e.ExpenseID == expenseId && e.CompanyID == companyId && e.IsActive);
            if (expense == null)
            {
                return new ErrorDataResult<Expense>(Messages.ExpenseNotFound);
            }
            return new SuccessDataResult<Expense>(expense);
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 60, "Expense", "DateRange")]
        public IDataResult<List<ExpenseDto>> GetExpensesByDateRange(DateTime startDate, DateTime endDate)
        {
            var companyId = _companyContext.GetCompanyId();
            // Tarih aralığını düzelt (endDate'i gün sonuna al)
            endDate = endDate.Date.AddDays(1).AddTicks(-1);

            using (var context = new GymContext()) // DTO için context kullanımı
            {
                var result = context.Expenses
                                    .Where(e => e.CompanyID == companyId && e.IsActive && e.ExpenseDate >= startDate && e.ExpenseDate <= endDate)
                                    .Select(e => new ExpenseDto
                                    {
                                        ExpenseID = e.ExpenseID,
                                        CompanyID = e.CompanyID,
                                        Description = e.Description,
                                        Amount = e.Amount,
                                        ExpenseDate = e.ExpenseDate,
                                        ExpenseType = e.ExpenseType,
                                        CreationDate = e.CreationDate
                                    })
                                    .OrderByDescending(e => e.ExpenseDate) // Veya CreationDate
                                    .ToList();
                return new SuccessDataResult<List<ExpenseDto>>(result);
            }
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 60, "Expense", "Monthly")]
        public IDataResult<List<ExpenseDto>> GetMonthlyExpenses(int year, int month)
        {
            var companyId = _companyContext.GetCompanyId();
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1).AddDays(1).AddTicks(-1); // Ayın son günü, gün sonu

             using (var context = new GymContext())
            {
                var result = context.Expenses
                                    .Where(e => e.CompanyID == companyId && e.IsActive && e.ExpenseDate >= startDate && e.ExpenseDate <= endDate) // .Date kaldırıldı, UTC karşılaştırması yapılacak
                                    .Select(e => new ExpenseDto
                                    {
                                        ExpenseID = e.ExpenseID,
                                        CompanyID = e.CompanyID,
                                        Description = e.Description,
                                        Amount = e.Amount,
                                        ExpenseDate = e.ExpenseDate,
                                        ExpenseType = e.ExpenseType,
                                        CreationDate = e.CreationDate
                                    })
                                    .OrderByDescending(e => e.ExpenseDate)
                                    .ToList();
                return new SuccessDataResult<List<ExpenseDto>>(result);
            }
        }

         [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 60, "Expense", "MonthlyTotal")]
        public IDataResult<decimal> GetTotalMonthlyExpenses(int year, int month)
        {
            var companyId = _companyContext.GetCompanyId();
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1).AddDays(1).AddTicks(-1);

            using (var context = new GymContext())
            {
                var total = context.Expenses
                                   .Where(e => e.CompanyID == companyId && e.IsActive && e.ExpenseDate >= startDate && e.ExpenseDate <= endDate)
                                   .Sum(e => e.Amount);
                return new SuccessDataResult<decimal>(total);
            }
        }


        [SecuredOperation("owner,admin")]
        [LogAspect]
        [ValidationAspect(typeof(ExpenseValidator))] // Validator eklendi
        [SmartCacheRemoveAspect("Expense")]
        [TransactionScopeAspect]
        public IResult Update(Expense expense)
        {
            // Güncellenecek giderin mevcut şirkete ait olup olmadığını kontrol et
             var existingExpense = _expenseDal.Get(e => e.ExpenseID == expense.ExpenseID && e.CompanyID == _companyContext.GetCompanyId());
            if (existingExpense == null)
            {
                return new ErrorResult(Messages.ExpenseNotFound);
            }

            // Şirket ID'sini tekrar ata (güvenlik için)
            expense.CompanyID = _companyContext.GetCompanyId();
            // CreationDate'in güncellenmemesini sağla (EfBaseRepository hallediyor ama garanti olsun)
            expense.CreationDate = existingExpense.CreationDate;

            // İş kuralları
            IResult ruleResult = BusinessRules.Run(CheckIfAmountIsPositive(expense.Amount));
            if (ruleResult != null)
            {
                return ruleResult;
            }

            _expenseDal.Update(expense);
            return new SuccessResult(Messages.ExpenseUpdated);
        }

        // --- İş Kuralları ---
        private IResult CheckIfAmountIsPositive(decimal amount)
        {
            if (amount <= 0)
            {
                return new ErrorResult(Messages.AmountMustBePositive);
            }
            return new SuccessResult();
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 60, "Expense", "DailyTotal")]
        public IDataResult<decimal> GetTotalDailyExpenses(DateTime date)
        {
            var companyId = _companyContext.GetCompanyId();
            var startDate = date.Date; // Günün başlangıcı
            var endDate = startDate.AddDays(1).AddTicks(-1); // Günün sonu

            using (var context = new GymContext())
            {
                // Sum işlemi boş koleksiyon için null dönebilir, bu yüzden null kontrolü ekleyelim.
                var total = context.Expenses
                                   .Where(e => e.CompanyID == companyId && e.IsActive && e.ExpenseDate >= startDate && e.ExpenseDate <= endDate)
                                   .Sum(e => (decimal?)e.Amount) ?? 0m; // decimal için 0m
                return new SuccessDataResult<decimal>(total);
            }
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 60, "Expense", "YearlyTotal")]
        public IDataResult<decimal> GetTotalYearlyExpenses(int year)
        {
            var companyId = _companyContext.GetCompanyId();
            var startDate = new DateTime(year, 1, 1); // Yılın başı
            var endDate = new DateTime(year, 12, 31, 23, 59, 59, 999); // Yılın sonu

            using (var context = new GymContext())
            {
                 // Sum işlemi boş koleksiyon için null dönebilir, bu yüzden null kontrolü ekleyelim.
                var total = context.Expenses
                                   .Where(e => e.CompanyID == companyId && e.IsActive && e.ExpenseDate >= startDate && e.ExpenseDate <= endDate)
                                   .Sum(e => (decimal?)e.Amount) ?? 0m; // decimal için 0m
                return new SuccessDataResult<decimal>(total);
            }
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 60, "Expense", "MonthlySummary")]
        public IDataResult<Dictionary<int, decimal>> GetMonthlyExpenseSummary(int year)
        {
            var companyId = _companyContext.GetCompanyId();
            var startDate = new DateTime(year, 1, 1);
            var endDate = new DateTime(year, 12, 31, 23, 59, 59, 999);

            using (var context = new GymContext())
            {
                // Tüm yılın verilerini tek sorguda al ve aylara göre grupla
                var monthlyTotals = context.Expenses
                    .Where(e => e.CompanyID == companyId && e.IsActive && e.ExpenseDate >= startDate && e.ExpenseDate <= endDate)
                    .GroupBy(e => e.ExpenseDate.Month)
                    .Select(g => new { Month = g.Key, Total = g.Sum(e => e.Amount) })
                    .ToDictionary(x => x.Month, x => x.Total);

                // 12 ayın tamamını içeren dictionary oluştur (veri olmayan aylar için 0)
                var result = new Dictionary<int, decimal>();
                for (int month = 1; month <= 12; month++)
                {
                    result[month] = monthlyTotals.ContainsKey(month) ? monthlyTotals[month] : 0m;
                }

                return new SuccessDataResult<Dictionary<int, decimal>>(result);
            }
        }

        /// <summary>
        /// Dashboard için optimize edilmiş tek metot - 5 API isteği yerine 1 API isteği
        /// Tüm dashboard verilerini tek seferde getirir
        /// </summary>
        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 30, "Expense", "Dashboard")]
        public IDataResult<ExpenseDashboardDto> GetExpenseDashboardData(int year, int month)
        {
            var companyId = _companyContext.GetCompanyId();

            using (var context = new GymContext())
            {
                var result = new ExpenseDashboardDto
                {
                    SelectedYear = year,
                    SelectedMonth = month,
                    DataRetrievedAt = DateTime.UtcNow
                };

                // Tarih aralıklarını hesapla
                var today = DateTime.Today;
                var todayStart = today;
                var todayEnd = today.AddDays(1).AddTicks(-1);

                var monthStart = new DateTime(year, month, 1);
                var monthEnd = monthStart.AddMonths(1).AddTicks(-1);

                var yearStart = new DateTime(year, 1, 1);
                var yearEnd = new DateTime(year, 12, 31, 23, 59, 59, 999);

                // Tek sorgu ile tüm yılın verilerini al
                var yearlyExpenses = context.Expenses
                    .Where(e => e.CompanyID == companyId && e.IsActive &&
                               e.ExpenseDate >= yearStart && e.ExpenseDate <= yearEnd)
                    .Select(e => new {
                        e.ExpenseID,
                        e.Description,
                        e.Amount,
                        e.ExpenseDate,
                        e.ExpenseType,
                        e.CreationDate,
                        e.CompanyID
                    })
                    .ToList();

                // 1. Günlük toplam (bugün)
                result.TotalDailyExpense = yearlyExpenses
                    .Where(e => e.ExpenseDate >= todayStart && e.ExpenseDate <= todayEnd)
                    .Sum(e => e.Amount);

                // 2. Aylık toplam ve detaylar (seçili ay)
                var monthlyExpenses = yearlyExpenses
                    .Where(e => e.ExpenseDate >= monthStart && e.ExpenseDate <= monthEnd)
                    .ToList();

                result.TotalMonthlyExpense = monthlyExpenses.Sum(e => e.Amount);

                result.MonthlyExpenseDetails = monthlyExpenses
                    .Select(e => new ExpenseDto
                    {
                        ExpenseID = e.ExpenseID,
                        CompanyID = e.CompanyID,
                        Description = e.Description,
                        Amount = e.Amount,
                        ExpenseDate = e.ExpenseDate,
                        ExpenseType = e.ExpenseType,
                        CreationDate = e.CreationDate
                    })
                    .OrderByDescending(e => e.ExpenseDate)
                    .ToList();

                // 3. Yıllık toplam
                result.TotalYearlyExpense = yearlyExpenses.Sum(e => e.Amount);

                // 4. Aylık özet (grafik için)
                var monthlyTotals = yearlyExpenses
                    .GroupBy(e => e.ExpenseDate.Month)
                    .ToDictionary(g => g.Key, g => g.Sum(e => e.Amount));

                // 12 ayın tamamını içeren dictionary oluştur
                for (int m = 1; m <= 12; m++)
                {
                    result.MonthlyExpenseSummary[m] = monthlyTotals.ContainsKey(m) ? monthlyTotals[m] : 0m;
                }

                return new SuccessDataResult<ExpenseDashboardDto>(result, "Dashboard verileri başarıyla getirildi.");
            }
        }
    }
}