{"version": 3, "sources": ["src/app/components/crud/membershiptype-update/membershiptype-update.component.css"], "sourcesContent": ["/* Membership Type Update Component Styles */\r\n\r\n/* Form Container */\r\n.modern-modal-content {\r\n  padding: 1.5rem;\r\n  border-radius: var(--border-radius-lg);\r\n  background-color: var(--bg-primary);\r\n  box-shadow: var(--shadow-lg);\r\n  max-width: 600; /* Increased from 600px to 800px */\r\n  width: 100%;\r\n  margin: 0 auto;\r\n}\r\n\r\n/* Form Section */\r\n.form-section {\r\n  margin-bottom: 1.5rem;\r\n  padding: 1.25rem;\r\n  background-color: var(--bg-secondary);\r\n  border-radius: var(--border-radius-md);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.form-section:hover {\r\n  background-color: var(--bg-tertiary);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.section-title {\r\n  font-weight: 600;\r\n  color: var(--primary);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n/* Loading Overlay */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 9999;\r\n}\r\n\r\n.spinner-container {\r\n  background-color: var(--bg-primary);\r\n  padding: 2rem;\r\n  border-radius: var(--border-radius-lg);\r\n  box-shadow: var(--shadow-lg);\r\n}\r\n\r\n/* Animations */\r\n.fade-in {\r\n  animation: fadeIn 0.3s var(--transition-timing);\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(10px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n/* Dark Mode Support */\r\n@media (prefers-color-scheme: dark) {\r\n  .form-section {\r\n    background-color: rgba(255, 255, 255, 0.05);\r\n  }\r\n  \r\n  .form-section:hover {\r\n    background-color: rgba(255, 255, 255, 0.08);\r\n  }\r\n}\r\n\r\n/* Fix for select dropdowns to ensure text is fully visible */\r\n.modern-form-control {\r\n  width: 100%;\r\n  padding-right: 30px; /* Add more padding on the right for the dropdown arrow */\r\n  text-overflow: ellipsis; /* Add ellipsis for text that might overflow */\r\n}\r\n\r\n/* Make the day dropdown wider to show full text */\r\n.day-select {\r\n  min-width: 100px; /* Ensure minimum width to display \"Gün\" fully */\r\n}\r\n\r\n/* Fix for price input group */\r\n.input-group {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 38px; /* Set a fixed height to match input height */\r\n}\r\n\r\n.input-group .modern-form-control {\r\n  flex: 1;\r\n  border-top-left-radius: 0;\r\n  border-bottom-left-radius: 0;\r\n}\r\n\r\n.input-group .input-group-text {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0.375rem 0.75rem;\r\n  font-size: 1rem;\r\n  font-weight: 400;\r\n  line-height: 1.5;\r\n  text-align: center;\r\n  white-space: nowrap;\r\n  border-top-left-radius: var(--border-radius-sm);\r\n  border-bottom-left-radius: var(--border-radius-sm);\r\n  border-top-right-radius: 0;\r\n  border-bottom-right-radius: 0;\r\n  height: 100%;\r\n  background-color: var(--bg-tertiary);\r\n  width: 40px; /* Fixed width for the currency symbol */\r\n}\r\n\r\n/* Responsive Adjustments */\r\n@media (max-width: 767.98px) {\r\n  .validity-selects {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .validity-selects select {\r\n    margin-right: 0 !important;\r\n    margin-bottom: 0.5rem;\r\n  }\r\n  \r\n  .validity-selects select:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n  \r\n  /* Ensure input group stays horizontal even on mobile */\r\n  .input-group {\r\n    flex-direction: row;\r\n  }\r\n}\r\n"], "mappings": ";AAGA,CAAC;AACC,WAAS;AACT,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACtB,cAAY,IAAI;AAChB,aAAW;AACX,SAAO;AACP,UAAQ,EAAE;AACZ;AAGA,CAAC;AACC,iBAAe;AACf,WAAS;AACT,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY,IAAI,KAAK;AACvB;AAEA,CARC,YAQY;AACX,oBAAkB,IAAI;AACtB,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACX,iBAAe;AACjB;AAGA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACX;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,WAAS;AACT,iBAAe,IAAI;AACnB,cAAY,IAAI;AAClB;AAGA,CAAC;AACC,aAAW,OAAO,KAAK,IAAI;AAC7B;AAEA,WAHa;AAIX;AAAO,aAAS;AAAG,eAAW,WAAW;AAAO;AAChD;AAAK,aAAS;AAAG,eAAW,WAAW;AAAI;AAC7C;AAGA,OAAO,CAAC,oBAAoB,EAAE;AAC5B,GApDD;AAqDG,sBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,GAxDD,YAwDc;AACX,sBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AACF;AAGA,CAAC;AACC,SAAO;AACP,iBAAe;AACf,iBAAe;AACjB;AAGA,CAAC;AACC,aAAW;AACb;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,UAAQ;AACV;AAEA,CANC,YAMY,CAlBZ;AAmBC,QAAM;AACN,0BAAwB;AACxB,6BAA2B;AAC7B;AAEA,CAZC,YAYY,CAAC;AACZ,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,WAAS,SAAS;AAClB,aAAW;AACX,eAAa;AACb,eAAa;AACb,cAAY;AACZ,eAAa;AACb,0BAAwB,IAAI;AAC5B,6BAA2B,IAAI;AAC/B,2BAAyB;AACzB,8BAA4B;AAC5B,UAAQ;AACR,oBAAkB,IAAI;AACtB,SAAO;AACT;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAAC;AACC,oBAAgB;AAClB;AAEA,GAJC,iBAIiB;AAChB,kBAAc;AACd,mBAAe;AACjB;AAEA,GATC,iBASiB,MAAM;AACtB,mBAAe;AACjB;AAGA,GA/CD;AAgDG,oBAAgB;AAClB;AACF;", "names": []}