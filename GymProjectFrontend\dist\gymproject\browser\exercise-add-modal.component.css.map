{"version": 3, "sources": ["src/app/components/exercise-add-modal/exercise-add-modal.component.css"], "sourcesContent": [".modal-header {\n  background: var(--card-bg);\n  border-bottom: 1px solid var(--border-color);\n  padding: 20px 24px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.modal-title {\n  color: var(--text-color);\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.title-icon {\n  color: var(--primary-color);\n}\n\n.btn-close {\n  background: none;\n  border: none;\n  color: var(--text-muted);\n  font-size: 1.2rem;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 6px;\n  transition: all 0.2s ease;\n}\n\n.btn-close:hover {\n  background-color: var(--hover-bg);\n  color: var(--text-color);\n}\n\n.btn-close:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.modal-body {\n  background: var(--card-bg);\n  padding: 24px;\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n.modal-footer {\n  background: var(--card-bg);\n  border-top: 1px solid var(--border-color);\n  padding: 16px 24px;\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n}\n\n/* Form Styles */\n.form-label {\n  color: var(--text-color);\n  font-weight: 500;\n  margin-bottom: 6px;\n}\n\n.form-label.required::after {\n  content: ' *';\n  color: #dc3545;\n}\n\n.form-control,\n.form-select {\n  background-color: var(--input-bg);\n  border: 1px solid var(--border-color);\n  color: var(--text-color);\n  border-radius: 8px;\n  padding: 10px 12px;\n  font-size: 0.95rem;\n  transition: all 0.2s ease;\n}\n\n.form-control:focus,\n.form-select:focus {\n  border-color: var(--primary-color);\n  box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);\n  background-color: var(--input-bg);\n  color: var(--text-color);\n}\n\n.form-control::placeholder {\n  color: var(--text-muted);\n  opacity: 0.8;\n}\n\n.form-select option {\n  background-color: var(--input-bg);\n  color: var(--text-color);\n}\n\n/* Validation Styles */\n.is-invalid {\n  border-color: #dc3545;\n}\n\n.is-invalid:focus {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.invalid-feedback {\n  display: block;\n  width: 100%;\n  margin-top: 4px;\n  font-size: 0.875rem;\n  color: #dc3545;\n}\n\n.form-text {\n  margin-top: 4px;\n  font-size: 0.8rem;\n  color: var(--text-muted);\n}\n\n/* Button Styles */\n.btn {\n  padding: 10px 20px;\n  border-radius: 8px;\n  font-weight: 500;\n  font-size: 0.95rem;\n  border: none;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: inline-flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.btn-primary {\n  background-color: var(--primary-color);\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background-color: var(--primary-hover);\n  transform: translateY(-1px);\n}\n\n.btn-primary:disabled {\n  background-color: var(--primary-color);\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.btn-secondary {\n  background-color: var(--secondary-color);\n  color: white;\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background-color: var(--secondary-hover);\n  transform: translateY(-1px);\n}\n\n.btn-secondary:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n\n/* Spinner */\n.spinner-border-sm {\n  width: 1rem;\n  height: 1rem;\n  border-width: 0.15em;\n}\n\n/* Responsive */\n@media (max-width: 768px) {\n  .modal-header,\n  .modal-body,\n  .modal-footer {\n    padding-left: 16px;\n    padding-right: 16px;\n  }\n\n  .modal-body {\n    max-height: 60vh;\n  }\n\n  .modal-footer {\n    flex-direction: column-reverse;\n  }\n\n  .modal-footer .btn {\n    width: 100%;\n    justify-content: center;\n  }\n}\n\n/* Dark mode adjustments */\n@media (prefers-color-scheme: dark) {\n  .form-control:focus,\n  .form-select:focus {\n    box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.4);\n  }\n  \n  .is-invalid:focus {\n    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.4);\n  }\n}\n\n/* Custom scrollbar for modal body */\n.modal-body::-webkit-scrollbar {\n  width: 6px;\n}\n\n.modal-body::-webkit-scrollbar-track {\n  background: var(--bg-color);\n  border-radius: 3px;\n}\n\n.modal-body::-webkit-scrollbar-thumb {\n  background: var(--border-color);\n  border-radius: 3px;\n}\n\n.modal-body::-webkit-scrollbar-thumb:hover {\n  background: var(--text-muted);\n}\n"], "mappings": ";AAAA,CAAC;AACC,cAAY,IAAI;AAChB,iBAAe,IAAI,MAAM,IAAI;AAC7B,WAAS,KAAK;AACd,WAAS;AACT,mBAAiB;AACjB,eAAa;AACf;AAEA,CAAC;AACC,SAAO,IAAI;AACX,UAAQ;AACR,aAAW;AACX,eAAa;AACb,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,SAAO,IAAI;AACb;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,SAAO,IAAI;AACX,aAAW;AACX,UAAQ;AACR,WAAS;AACT,iBAAe;AACf,cAAY,IAAI,KAAK;AACvB;AAEA,CAXC,SAWS;AACR,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAhBC,SAgBS;AACR,WAAS;AACT,UAAQ;AACV;AAEA,CAAC;AACC,cAAY,IAAI;AAChB,WAAS;AACT,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,cAAY,IAAI;AAChB,cAAY,IAAI,MAAM,IAAI;AAC1B,WAAS,KAAK;AACd,WAAS;AACT,mBAAiB;AACjB,OAAK;AACP;AAGA,CAAC;AACC,SAAO,IAAI;AACX,eAAa;AACb,iBAAe;AACjB;AAEA,CANC,UAMU,CAAC,QAAQ;AAClB,WAAS;AACT,SAAO;AACT;AAEA,CAAC;AACD,CAAC;AACC,oBAAkB,IAAI;AACtB,UAAQ,IAAI,MAAM,IAAI;AACtB,SAAO,IAAI;AACX,iBAAe;AACf,WAAS,KAAK;AACd,aAAW;AACX,cAAY,IAAI,KAAK;AACvB;AAEA,CAXC,YAWY;AACb,CAXC,WAWW;AACV,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,OAAO,KAAK,IAAI,oBAAoB,EAAE;AACxD,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAnBC,YAmBY;AACX,SAAO,IAAI;AACX,WAAS;AACX;AAEA,CAvBC,YAuBY;AACX,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAGA,CAAC;AACC,gBAAc;AAChB;AAEA,CAJC,UAIU;AACT,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,OAAO,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC7C;AAEA,CAAC;AACC,WAAS;AACT,SAAO;AACP,cAAY;AACZ,aAAW;AACX,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,aAAW;AACX,SAAO,IAAI;AACb;AAGA,CAAC;AACC,WAAS,KAAK;AACd,iBAAe;AACf,eAAa;AACb,aAAW;AACX,UAAQ;AACR,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO;AACT;AAEA,CALC,WAKW,MAAM,KAAK;AACrB,oBAAkB,IAAI;AACtB,aAAW,WAAW;AACxB;AAEA,CAVC,WAUW;AACV,oBAAkB,IAAI;AACtB,WAAS;AACT,UAAQ;AACR,aAAW;AACb;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO;AACT;AAEA,CALC,aAKa,MAAM,KAAK;AACvB,oBAAkB,IAAI;AACtB,aAAW,WAAW;AACxB;AAEA,CAVC,aAUa;AACZ,WAAS;AACT,UAAQ;AACR,aAAW;AACb;AAGA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,gBAAc;AAChB;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GArLD;AAAA,EAsLC,CA1ID;AAAA,EA2IC,CApID;AAqIG,kBAAc;AACd,mBAAe;AACjB;AAEA,GAhJD;AAiJG,gBAAY;AACd;AAEA,GA7ID;AA8IG,oBAAgB;AAClB;AAEA,GAjJD,aAiJe,CAtEf;AAuEG,WAAO;AACP,qBAAiB;AACnB;AACF;AAGA,OAAO,CAAC,oBAAoB,EAAE;AAC5B,GApID,YAoIc;AAAA,EACb,CApID,WAoIa;AACV,gBAAY,EAAE,EAAE,EAAE,OAAO,KAAK,IAAI,oBAAoB,EAAE;AAC1D;AAEA,GA3GD,UA2GY;AACT,gBAAY,EAAE,EAAE,EAAE,OAAO,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC7C;AACF;AAGA,CA3KC,UA2KU;AACT,SAAO;AACT;AAEA,CA/KC,UA+KU;AACT,cAAY,IAAI;AAChB,iBAAe;AACjB;AAEA,CApLC,UAoLU;AACT,cAAY,IAAI;AAChB,iBAAe;AACjB;AAEA,CAzLC,UAyLU,yBAAyB;AAClC,cAAY,IAAI;AAClB;", "names": []}