
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: false,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 1179, hash: '661a902c7cf2e32814bedc5ac8ddf453901a6c72ca64ad2a04ace5cd686207f7', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 1719, hash: '406c1a1a252c82a24ab20b0305e7a455136b88e16d37dcad90f363aaad2d2525', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)}
  },
};
