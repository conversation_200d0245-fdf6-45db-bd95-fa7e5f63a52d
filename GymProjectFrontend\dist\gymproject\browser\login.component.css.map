{"version": 3, "sources": ["src/app/components/login/login.component.css"], "sourcesContent": [":root {\r\n  --primary-color: #4361ee;\r\n  --secondary-color: #3a0ca3;\r\n  --accent-color: #f72585;\r\n  --success-color: #4cc9f0;\r\n  --warning-color: #f8961e;\r\n  --danger-color: #f94144;\r\n  --text-color: #2b2d42;\r\n  --text-muted: #6c757d;\r\n  --background-color: #f8f9fa;\r\n  --card-bg-color: #ffffff;\r\n  --input-bg: #f8f9fa;\r\n  --input-border: #ced4da;\r\n  --input-text: #495057;\r\n  --shadow-color: rgba(0, 0, 0, 0.1);\r\n  --border-radius: 12px;\r\n}\r\n\r\n.login-container {\r\n  height: 100vh;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: var(--background-color);\r\n  background-image: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);\r\n}\r\n\r\n.login-wrapper {\r\n  display: flex;\r\n  width: 90%;\r\n  max-width: 1200px;\r\n  height: 650px;\r\n  border-radius: var(--border-radius);\r\n  overflow: hidden;\r\n  box-shadow: 0 20px 40px var(--shadow-color);\r\n  position: relative;\r\n}\r\n\r\n/* Left Panel - Image */\r\n.login-image-panel {\r\n  flex: 1.2;\r\n  background-image: url('https://images.unsplash.com/photo-1571902943202-507ec2618e8f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');\r\n  background-size: cover;\r\n  background-position: center;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(135deg, rgba(67, 97, 238, 0.85) 0%, rgba(58, 12, 163, 0.85) 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(2px);\r\n}\r\n\r\n.gym-branding {\r\n  text-align: center;\r\n  color: white;\r\n  padding: 30px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 20px;\r\n  max-width: 500px;\r\n}\r\n\r\n.logo-container {\r\n  width: 100px;\r\n  height: 100px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 10px;\r\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.gym-branding i {\r\n  font-size: 50px;\r\n  color: white;\r\n}\r\n\r\n.gym-branding h1 {\r\n  font-size: 42px;\r\n  font-weight: 700;\r\n  margin-bottom: 5px;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.gym-branding p {\r\n  font-size: 18px;\r\n  opacity: 0.9;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.features {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  width: 100%;\r\n  max-width: 300px;\r\n}\r\n\r\n.feature {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  padding: 12px 20px;\r\n  border-radius: 10px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.feature:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: translateY(-3px);\r\n}\r\n\r\n.feature i {\r\n  font-size: 20px;\r\n  color: white;\r\n}\r\n\r\n.feature span {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* Right Panel - Form */\r\n.login-form-panel {\r\n  flex: 0.8;\r\n  background-color: var(--card-bg-color);\r\n  padding: 40px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.login-form-panel::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -50px;\r\n  right: -50px;\r\n  width: 100px;\r\n  height: 100px;\r\n  background: var(--primary-color);\r\n  opacity: 0.1;\r\n  border-radius: 50%;\r\n}\r\n\r\n.login-form-panel::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -80px;\r\n  left: -80px;\r\n  width: 160px;\r\n  height: 160px;\r\n  background: var(--secondary-color);\r\n  opacity: 0.1;\r\n  border-radius: 50%;\r\n}\r\n\r\n.login-form-container {\r\n  max-width: 400px;\r\n  margin: 0 auto;\r\n  width: 100%;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.login-header {\r\n  margin-bottom: 40px;\r\n  text-align: center;\r\n}\r\n\r\n.header-icon {\r\n  width: 70px;\r\n  height: 70px;\r\n  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto 20px;\r\n  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);\r\n}\r\n\r\n.header-icon i {\r\n  font-size: 30px;\r\n  color: white;\r\n}\r\n\r\n.login-header h2 {\r\n  color: var(--text-color);\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.login-header p {\r\n  color: var(--text-muted);\r\n  font-size: 16px;\r\n}\r\n\r\n.login-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 24px;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 6px;\r\n  font-size: 0.9rem;\r\n  color: var(--text-color);\r\n}\r\n\r\n.input-group {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.input-group i {\r\n  position: absolute;\r\n  left: 12px;\r\n  color: var(--text-muted);\r\n}\r\n\r\n.input-group input, .input-group select {\r\n  width: 100%;\r\n  padding: 12px 12px 12px 40px;\r\n  border: 1px solid var(--input-border);\r\n  border-radius: 6px;\r\n  background-color: var(--input-bg);\r\n  color: var(--input-text);\r\n  font-size: 1rem;\r\n  transition: border-color 0.3s, box-shadow 0.3s;\r\n}\r\n\r\n.input-group input:focus, .input-group select:focus {\r\n  border-color: var(--primary-color);\r\n  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);\r\n  outline: none;\r\n}\r\n\r\n.input-group input.is-invalid {\r\n  border-color: var(--danger-color);\r\n  box-shadow: 0 0 0 2px rgba(249, 65, 68, 0.2);\r\n}\r\n\r\n.input-group .toggle-password {\r\n  position: absolute;\r\n  right: 12px;\r\n  background: none;\r\n  border: none;\r\n  color: var(--text-muted);\r\n  cursor: pointer;\r\n}\r\n\r\n.input-group .toggle-password:hover {\r\n  color: var(--primary-color);\r\n}\r\n\r\n.error-message {\r\n  margin-top: 5px;\r\n  color: var(--danger-color);\r\n  font-size: 0.8rem;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 5px;\r\n}\r\n\r\n.forgot-password {\r\n  color: var(--primary-color);\r\n  text-decoration: none;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.forgot-password:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.login-button {\r\n  width: 100%;\r\n  padding: 14px;\r\n  background-color: var(--primary-color);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.login-button:hover {\r\n  background-color: var(--secondary-color);\r\n}\r\n\r\n.login-button:disabled {\r\n  background-color: #a0a0a0;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.login-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n  color: var(--text-muted);\r\n  font-size: 0.85rem;\r\n}\r\n\r\n.support {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.support a {\r\n  color: var(--primary-color);\r\n  text-decoration: none;\r\n}\r\n\r\n.support a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.login-footer p {\r\n  color: var(--text-muted);\r\n  font-size: 0.85rem;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 992px) {\r\n  .login-wrapper {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .login-image-panel {\r\n    display: none;\r\n  }\r\n\r\n  .login-form-panel {\r\n    padding: 30px 20px;\r\n  }\r\n}\r\n\r\n@media (max-width: 576px) {\r\n  .login-form-container {\r\n    padding: 0;\r\n  }\r\n\r\n  .login-header h2 {\r\n    font-size: 1.5rem;\r\n  }\r\n}\r\n\r\n/* Dark Mode Specific Styles */\r\n:host-context([data-theme=\"dark\"]) .input-group input,\r\n:host-context([data-theme=\"dark\"]) .input-group select {\r\n  background-color: var(--input-bg);\r\n  border-color: var(--input-border);\r\n  color: var(--input-text);\r\n}\r\n\r\n:host-context([data-theme=\"dark\"]) .login-form-panel {\r\n  background-color: var(--card-bg-color);\r\n}\r\n\r\n:host-context([data-theme=\"dark\"]) .login-header h2,\r\n:host-context([data-theme=\"dark\"]) .form-group label {\r\n  color: var(--text-color);\r\n}\r\n\r\n:host-context([data-theme=\"dark\"]) .login-footer,\r\n:host-context([data-theme=\"dark\"]) .login-header p {\r\n  color: var(--text-muted);\r\n}\r\n\r\n:host-context([data-theme=\"dark\"]) .input-group i,\r\n:host-context([data-theme=\"dark\"]) .input-group .toggle-password {\r\n  color: var(--text-muted);\r\n}\r\n\r\n:host-context([data-theme=\"dark\"]) .login-container {\r\n  background-image: linear-gradient(135deg, #121212 0%, #1a1a1a 100%);\r\n}\r\n"], "mappings": ";AAAA;AACE,mBAAiB;AACjB,qBAAmB;AACnB,kBAAgB;AAChB,mBAAiB;AACjB,mBAAiB;AACjB,kBAAgB;AAChB,gBAAc;AACd,gBAAc;AACd,sBAAoB;AACpB,mBAAiB;AACjB,cAAY;AACZ,kBAAgB;AAChB,gBAAc;AACd,kBAAgB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC9B,mBAAiB;AACnB;AAEA,CAAC;AACC,UAAQ;AACR,SAAO;AACP,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,oBAAkB,IAAI;AACtB;AAAA,IAAkB;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AAChE;AAEA,CAAC;AACC,WAAS;AACT,SAAO;AACP,aAAW;AACX,UAAQ;AACR,iBAAe,IAAI;AACnB,YAAU;AACV,cAAY,EAAE,KAAK,KAAK,IAAI;AAC5B,YAAU;AACZ;AAGA,CAAC;AACC,QAAM;AACN,oBAAkB;AAClB,mBAAiB;AACjB,uBAAqB;AACrB,YAAU;AACV,WAAS;AACT,eAAa;AACb,mBAAiB;AACnB;AAEA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,EAAhD;AAAA,MAAoD,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM;AACxF,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACxB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,WAAS;AACT,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,OAAK;AACL,aAAW;AACb;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,iBAAe;AACf,cAAY,EAAE,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,CAxBC,aAwBa;AACZ,aAAW;AACX,SAAO;AACT;AAEA,CA7BC,aA6Ba;AACZ,aAAW;AACX,eAAa;AACb,iBAAe;AACf,eAAa,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CApCC,aAoCa;AACZ,aAAW;AACX,WAAS;AACT,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACL,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,WAAS,KAAK;AACd,iBAAe;AACf,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACtB,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,cAAY,IAAI,KAAK;AACvB;AAEA,CAZC,OAYO;AACN,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,aAAW,WAAW;AACxB;AAEA,CAjBC,QAiBQ;AACP,aAAW;AACX,SAAO;AACT;AAEA,CAtBC,QAsBQ;AACP,aAAW;AACX,eAAa;AACf;AAGA,CAAC;AACC,QAAM;AACN,oBAAkB,IAAI;AACtB,WAAS;AACT,WAAS;AACT,kBAAgB;AAChB,mBAAiB;AACjB,YAAU;AACV,YAAU;AACZ;AAEA,CAXC,gBAWgB;AACf,WAAS;AACT,YAAU;AACV,OAAK;AACL,SAAO;AACP,SAAO;AACP,UAAQ;AACR,cAAY,IAAI;AAChB,WAAS;AACT,iBAAe;AACjB;AAEA,CAvBC,gBAuBgB;AACf,WAAS;AACT,YAAU;AACV,UAAQ;AACR,QAAM;AACN,SAAO;AACP,UAAQ;AACR,cAAY,IAAI;AAChB,WAAS;AACT,iBAAe;AACjB;AAEA,CAAC;AACC,aAAW;AACX,UAAQ,EAAE;AACV,SAAO;AACP,YAAU;AACV,WAAS;AACX;AAEA,CAAC;AACC,iBAAe;AACf,cAAY;AACd;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,iBAAiB,EAAE;AAAA,MAAE,IAAI,mBAAmB;AACpF,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,UAAQ,EAAE,KAAK;AACf,cAAY,EAAE,KAAK,KAAK,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AAC5C;AAEA,CAZC,YAYY;AACX,aAAW;AACX,SAAO;AACT;AAEA,CAtBC,aAsBa;AACZ,SAAO,IAAI;AACX,aAAW;AACX,eAAa;AACb,iBAAe;AACjB;AAEA,CA7BC,aA6Ba;AACZ,SAAO,IAAI;AACX,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAJC,WAIW;AACV,WAAS;AACT,iBAAe;AACf,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CAAC;AACC,YAAU;AACV,WAAS;AACT,eAAa;AACf;AAEA,CANC,YAMY;AACX,YAAU;AACV,QAAM;AACN,SAAO,IAAI;AACb;AAEA,CAZC,YAYY;AAAO,CAZnB,YAYgC;AAC/B,SAAO;AACP,WAAS,KAAK,KAAK,KAAK;AACxB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe;AACf,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,aAAW;AACX,cAAY,aAAa,IAAI,EAAE,WAAW;AAC5C;AAEA,CAvBC,YAuBY,KAAK;AAAQ,CAvBzB,YAuBsC,MAAM;AAC3C,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AACxC,WAAS;AACX;AAEA,CA7BC,YA6BY,KAAK,CAAC;AACjB,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC1C;AAEA,CAlCC,YAkCY,CAAC;AACZ,YAAU;AACV,SAAO;AACP,cAAY;AACZ,UAAQ;AACR,SAAO,IAAI;AACX,UAAQ;AACV;AAEA,CA3CC,YA2CY,CATC,eASe;AAC3B,SAAO,IAAI;AACb;AAEA,CAAC;AACC,cAAY;AACZ,SAAO,IAAI;AACX,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,cAAY;AACd;AAEA,CAAC;AACC,SAAO,IAAI;AACX,mBAAiB;AACjB,aAAW;AACb;AAEA,CANC,eAMe;AACd,mBAAiB;AACnB;AAEA,CAAC;AACC,SAAO;AACP,WAAS;AACT,oBAAkB,IAAI;AACtB,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,aAAW;AACX,eAAa;AACb,UAAQ;AACR,cAAY,iBAAiB;AAC7B,cAAY;AACZ,WAAS;AACT,mBAAiB;AACjB,eAAa;AACf;AAEA,CAjBC,YAiBY;AACX,oBAAkB,IAAI;AACxB;AAEA,CArBC,YAqBY;AACX,oBAAkB;AAClB,UAAQ;AACV;AAEA,CAAC;AACC,cAAY;AACZ,cAAY;AACZ,SAAO,IAAI;AACX,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,OAAK;AACL,iBAAe;AACjB;AAEA,CARC,QAQQ;AACP,SAAO,IAAI;AACX,mBAAiB;AACnB;AAEA,CAbC,QAaQ,CAAC;AACR,mBAAiB;AACnB;AAEA,CAxBC,aAwBa;AACZ,SAAO,IAAI;AACX,aAAW;AACb;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA9UD;AA+UG,oBAAgB;AAClB;AAEA,GAtUD;AAuUG,aAAS;AACX;AAEA,GApOD;AAqOG,aAAS,KAAK;AAChB;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAvMD;AAwMG,aAAS;AACX;AAEA,GAnMD,aAmMe;AACZ,eAAW;AACb;AACF;AAGA,cAAc,CAAC,UAAU,CAAC,SAAS,CAtJlC,YAsJ+C;AAChD,cAAc,CAAC,UAAU,CAAC,SAAS,CAvJlC,YAuJ+C;AAC9C,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,cAAc,CAAC,UAAU,CAAC,SAAS,CA3PlC;AA4PC,oBAAkB,IAAI;AACxB;AAEA,cAAc,CAAC,UAAU,CAAC,SAAS,CApNlC,aAoNgD;AACjD,cAAc,CAAC,UAAU,CAAC,SAAS,CA7KlC,WA6K8C;AAC7C,SAAO,IAAI;AACb;AAEA,cAAc,CAAC,UAAU,CAAC,SAAS,CAvElC;AAwED,cAAc,CAAC,UAAU,CAAC,SAAS,CA1NlC,aA0NgD;AAC/C,SAAO,IAAI;AACb;AAEA,cAAc,CAAC,UAAU,CAAC,SAAS,CA3KlC,YA2K+C;AAChD,cAAc,CAAC,UAAU,CAAC,SAAS,CA5KlC,YA4K+C,CA1IlC;AA2IZ,SAAO,IAAI;AACb;AAEA,cAAc,CAAC,UAAU,CAAC,SAAS,CA1YlC;AA2YC;AAAA,IAAkB;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AAChE;", "names": []}