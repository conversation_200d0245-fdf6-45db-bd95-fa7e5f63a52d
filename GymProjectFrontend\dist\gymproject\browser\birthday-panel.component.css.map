{"version": 3, "sources": ["src/app/components/birthday-panel/birthday-panel.component.css"], "sourcesContent": [".birthday-panel-container {\r\n  padding: 20px;\r\n}\r\n\r\n.dialog-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.no-birthdays {\r\n  text-align: center;\r\n  padding: 30px;\r\n  color: var(--text-secondary, #666);\r\n}\r\n\r\n.message-template {\r\n  background-color: var(--bg-secondary, #f8f9fa);\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n  border: 1px solid var(--border-color, #dee2e6);\r\n}\r\n\r\n.members-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n}\r\n\r\n.member-card {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px;\r\n  background-color: var(--card-bg-color, #fff);\r\n  color: var(--text-color, #212529);\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px var(--shadow-color, rgba(0, 0, 0, 0.1));\r\n  border: 1px solid var(--border-color, #dee2e6);\r\n}\r\n\r\n.member-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.avatar-circle {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  background-color: var(--primary-color, #4361ee);\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.member-details h4 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: var(--text-color, #212529);\r\n}\r\n\r\n.member-details p {\r\n  margin: 5px 0 0;\r\n  font-size: 14px;\r\n  color: var(--text-secondary, #666);\r\n}\r\n\r\n.days-left {\r\n  color: var(--primary-color, #4361ee) !important;\r\n  font-weight: 600;\r\n}\r\n\r\n.dialog-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n}\r\n\r\n/* Styles for the standalone page version */\r\n.birthday-content {\r\n  padding: 15px;\r\n}\r\n\r\n/* Dark mode specific overrides */\r\n[data-theme=\"dark\"] .member-card {\r\n  border: 1px solid var(--border-color, #343a40);\r\n}\r\n\r\n[data-theme=\"dark\"] .message-template {\r\n  background-color: var(--bg-tertiary, #2d2d2d);\r\n}\r\n\r\n[data-theme=\"dark\"] .member-details h4,\r\n[data-theme=\"dark\"] .member-details p {\r\n  color: var(--text-color, #e9ecef);\r\n}\r\n\r\n[data-theme=\"dark\"] .days-left {\r\n  color: var(--accent-color, #4895ef) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .no-birthdays {\r\n  color: var(--text-muted, #adb5bd);\r\n}\r\n\r\n/* Fix for textarea placeholder in dark mode */\r\n[data-theme=\"dark\"] textarea::placeholder {\r\n  color: var(--text-muted, #adb5bd);\r\n  opacity: 0.7;\r\n}\r\n\r\n[data-theme=\"dark\"] textarea {\r\n  color: var(--text-color, #e9ecef);\r\n  background-color: var(--bg-tertiary, #2d2d2d);\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .member-card {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .member-actions {\r\n    margin-top: 15px;\r\n    width: 100%;\r\n  }\r\n  \r\n  .member-actions button {\r\n    width: 100%;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,CAAC;AACC,WAAS;AACX;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,SAAO,IAAI,gBAAgB,EAAE;AAC/B;AAEA,CAAC;AACC,oBAAkB,IAAI,cAAc,EAAE;AACtC,WAAS;AACT,iBAAe;AACf,iBAAe;AACf,UAAQ,IAAI,MAAM,IAAI,cAAc,EAAE;AACxC;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,oBAAkB,IAAI,eAAe,EAAE;AACvC,SAAO,IAAI,YAAY,EAAE;AACzB,iBAAe;AACf,cAAY,EAAE,IAAI,IAAI,IAAI,cAAc,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACxD,UAAQ,IAAI,MAAM,IAAI,cAAc,EAAE;AACxC;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,oBAAkB,IAAI,eAAe,EAAE;AACvC,SAAO;AACP,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW;AACX,eAAa;AACf;AAEA,CAAC,eAAe;AACd,UAAQ;AACR,aAAW;AACX,eAAa;AACb,SAAO,IAAI,YAAY,EAAE;AAC3B;AAEA,CAPC,eAOe;AACd,UAAQ,IAAI,EAAE;AACd,aAAW;AACX,SAAO,IAAI,gBAAgB,EAAE;AAC/B;AAEA,CAAC;AACC,SAAO,IAAI,eAAe,EAAE;AAC5B,eAAa;AACf;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,cAAY;AACd;AAGA,CAAC;AACC,WAAS;AACX;AAGA,CAAC,iBAAmB,CA7DnB;AA8DC,UAAQ,IAAI,MAAM,IAAI,cAAc,EAAE;AACxC;AAEA,CAAC,iBAAmB,CA/EnB;AAgFC,oBAAkB,IAAI,aAAa,EAAE;AACvC;AAEA,CAAC,iBAAmB,CAtCnB,eAsCmC;AACpC,CAAC,iBAAmB,CAvCnB,eAuCmC;AAClC,SAAO,IAAI,YAAY,EAAE;AAC3B;AAEA,CAAC,iBAAmB,CA9BnB;AA+BC,SAAO,IAAI,cAAc,EAAE;AAC7B;AAEA,CAAC,iBAAmB,CAlGnB;AAmGC,SAAO,IAAI,YAAY,EAAE;AAC3B;AAGA,CAAC,iBAAmB,QAAQ;AAC1B,SAAO,IAAI,YAAY,EAAE;AACzB,WAAS;AACX;AAEA,CAAC,iBAAmB;AAClB,SAAO,IAAI,YAAY,EAAE;AACzB,oBAAkB,IAAI,aAAa,EAAE;AACvC;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GA9FD;AA+FG,oBAAgB;AAChB,iBAAa;AACf;AAEA,GAAC;AACC,gBAAY;AACZ,WAAO;AACT;AAEA,GALC,eAKe;AACd,WAAO;AACT;AACF;", "names": []}