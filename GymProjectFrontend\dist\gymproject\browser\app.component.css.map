{"version": 3, "sources": ["src/app/app.component.css"], "sourcesContent": ["/* Initializing Overlay */\r\n.app-initializing {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: var(--background-color);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    z-index: 9999;\r\n}\r\n\r\n.initializing-spinner {\r\n    position: relative;\r\n    width: 100px;\r\n    height: 100px;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n}\r\n\r\n.spinner-circle {\r\n    position: absolute;\r\n    width: 80px;\r\n    height: 80px;\r\n    border-radius: 50%;\r\n    border: 4px solid transparent;\r\n    border-top-color: var(--primary-color);\r\n    border-bottom-color: var(--primary-color);\r\n    animation: spin 1.5s linear infinite;\r\n}\r\n\r\n.dumbbell {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    animation: lift 2s ease-in-out infinite;\r\n}\r\n\r\n.weight {\r\n    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);\r\n    border-radius: 50%;\r\n    width: 28px;\r\n    height: 28px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    box-shadow: 0 0 15px rgba(67, 97, 238, 0.5);\r\n}\r\n\r\n.inner-weight {\r\n    width: 50%;\r\n    height: 50%;\r\n    background-color: rgba(255, 255, 255, 0.2);\r\n    border-radius: 50%;\r\n}\r\n\r\n.weight.left {\r\n    animation: pulse-left 2s ease-in-out infinite;\r\n}\r\n\r\n.weight.right {\r\n    animation: pulse-right 2s ease-in-out infinite;\r\n}\r\n\r\n.handle {\r\n    height: 8px;\r\n    width: 50px;\r\n    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);\r\n    border-radius: 4px;\r\n    box-shadow: 0 0 10px rgba(67, 97, 238, 0.5);\r\n}\r\n\r\n@keyframes spin {\r\n    0% {\r\n        transform: rotate(0deg);\r\n    }\r\n    100% {\r\n        transform: rotate(360deg);\r\n    }\r\n}\r\n\r\n@keyframes lift {\r\n    0%, 100% {\r\n        transform: translateY(0);\r\n    }\r\n    50% {\r\n        transform: translateY(-5px);\r\n    }\r\n}\r\n\r\n@keyframes pulse-left {\r\n    0%, 100% {\r\n        transform: scale(1);\r\n    }\r\n    50% {\r\n        transform: scale(1.1);\r\n    }\r\n}\r\n\r\n@keyframes pulse-right {\r\n    0%, 100% {\r\n        transform: scale(1);\r\n    }\r\n    50% {\r\n        transform: scale(1.1);\r\n    }\r\n}\r\n\r\n/* Main App Container */\r\n.app-container {\r\n    display: flex;\r\n    height: 100vh;\r\n    width: 100%;\r\n    overflow: hidden;\r\n    position: relative;\r\n    transition: all 0.3s ease;\r\n    opacity: 1;\r\n    visibility: visible;\r\n}\r\n\r\n.app-container.initializing {\r\n    opacity: 0;\r\n    visibility: hidden;\r\n}\r\n\r\n.main-content {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.content-area {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 20px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n/* Mobile Header */\r\n.mobile-header {\r\n    display: none;\r\n    padding: 15px;\r\n    background-color: var(--sidebar-bg);\r\n    color: var(--sidebar-text);\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    box-shadow: 0 2px 4px var(--shadow-color);\r\n    z-index: 100;\r\n}\r\n\r\n.mobile-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n}\r\n\r\n.sidebar-toggle, .theme-toggle {\r\n    background: none;\r\n    border: none;\r\n    color: var(--sidebar-text);\r\n    font-size: 18px;\r\n    cursor: pointer;\r\n    width: 40px;\r\n    height: 40px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border-radius: 50%;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.sidebar-toggle:hover, .theme-toggle:hover {\r\n    background-color: var(--sidebar-hover);\r\n}\r\n\r\n/* Responsive Styles */\r\n@media (max-width: 991.98px) {\r\n    .mobile-header {\r\n        display: flex;\r\n    }\r\n    \r\n    .app-container.sidebar-collapsed .main-content {\r\n        margin-left: 0;\r\n    }\r\n}\r\n\r\n/* Initializing state in body */\r\nbody.initializing {\r\n    overflow: hidden;\r\n}\r\n"], "mappings": ";AACA,CAAC;AACG,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,oBAAkB,IAAI;AACtB,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACb;AAEA,CAAC;AACG,YAAU;AACV,SAAO;AACP,UAAQ;AACR,WAAS;AACT,mBAAiB;AACjB,eAAa;AACjB;AAEA,CAAC;AACG,YAAU;AACV,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,UAAQ,IAAI,MAAM;AAClB,oBAAkB,IAAI;AACtB,uBAAqB,IAAI;AACzB,aAAW,KAAK,KAAK,OAAO;AAChC;AAEA,CAAC;AACG,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW,KAAK,GAAG,YAAY;AACnC;AAEA,CAAC;AACG;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,iBAAiB,EAAE;AAAA,MAAE,IAAI,mBAAmB;AACpF,iBAAe;AACf,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,cAAY,EAAE,EAAE,KAAK,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AAC3C;AAEA,CAAC;AACG,SAAO;AACP,UAAQ;AACR,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,iBAAe;AACnB;AAEA,CAlBC,MAkBM,CAAC;AACJ,aAAW,WAAW,GAAG,YAAY;AACzC;AAEA,CAtBC,MAsBM,CAAC;AACJ,aAAW,YAAY,GAAG,YAAY;AAC1C;AAEA,CAAC;AACG,UAAQ;AACR,SAAO;AACP;AAAA,IAAY;AAAA,MAAgB,KAAK;AAAA,MAAE,IAAI,iBAAiB,EAAE;AAAA,MAAE,IAAI,mBAAmB;AACnF,iBAAe;AACf,cAAY,EAAE,EAAE,KAAK,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AAC3C;AAEA,WA5Ce;AA6CX;AACI,eAAW,OAAO;AACtB;AACA;AACI,eAAW,OAAO;AACtB;AACJ;AAEA,WA9Ce;AA+CX;AACI,eAAW,WAAW;AAC1B;AACA;AACI,eAAW,WAAW;AAC1B;AACJ;AAEA,WAjCe;AAkCX;AACI,eAAW,MAAM;AACrB;AACA;AACI,eAAW,MAAM;AACrB;AACJ;AAEA,WAtCe;AAuCX;AACI,eAAW,MAAM;AACrB;AACA;AACI,eAAW,MAAM;AACrB;AACJ;AAGA,CAAC;AACG,WAAS;AACT,UAAQ;AACR,SAAO;AACP,YAAU;AACV,YAAU;AACV,cAAY,IAAI,KAAK;AACrB,WAAS;AACT,cAAY;AAChB;AAEA,CAXC,aAWa,CAAC;AACX,WAAS;AACT,cAAY;AAChB;AAEA,CAAC;AACG,QAAM;AACN,WAAS;AACT,kBAAgB;AAChB,YAAU;AACV,cAAY,IAAI,KAAK;AACzB;AAEA,CAAC;AACG,QAAM;AACN,cAAY;AACZ,WAAS;AACT,cAAY,IAAI,KAAK;AACzB;AAGA,CAAC;AACG,WAAS;AACT,WAAS;AACT,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,eAAa;AACb,mBAAiB;AACjB,cAAY,EAAE,IAAI,IAAI,IAAI;AAC1B,WAAS;AACb;AAEA,CAAC;AACG,aAAW;AACX,eAAa;AACjB;AAEA,CAAC;AAAgB,CAAC;AACd,cAAY;AACZ,UAAQ;AACR,SAAO,IAAI;AACX,aAAW;AACX,UAAQ;AACR,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,iBAAe;AACf,cAAY,IAAI,KAAK;AACzB;AAEA,CAfC,cAec;AAAQ,CAfL,YAekB;AAChC,oBAAkB,IAAI;AAC1B;AAGA,OAAO,CAAC,SAAS,EAAE;AACf,GArCH;AAsCO,aAAS;AACb;AAEA,GAzEH,aAyEiB,CAAC,kBAAkB,CAzDpC;AA0DO,iBAAa;AACjB;AACJ;AAGA,IAAI,CApEW;AAqEX,YAAU;AACd;", "names": []}