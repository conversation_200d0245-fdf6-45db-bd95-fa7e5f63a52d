{"version": 3, "sources": ["src/app/components/membershiptype/membershiptype.component.css"], "sourcesContent": ["/* Membership Type Component Styles */\r\n\r\n/* Content Blur Effect */\r\n.content-blur {\r\n  filter: blur(3px);\r\n  pointer-events: none;\r\n}\r\n\r\n/* Table Container */\r\n.table-container {\r\n  overflow-x: auto;\r\n  margin-bottom: 1.5rem;\r\n  border-radius: var(--border-radius-md);\r\n}\r\n\r\n/* Sort Button */\r\n.sort-btn {\r\n  background: none;\r\n  border: none;\r\n  color: inherit;\r\n  padding: 0 5px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.sort-btn:hover {\r\n  opacity: 0.8;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.sort-btn:focus {\r\n  outline: none;\r\n  box-shadow: none;\r\n}\r\n\r\n/* Action Buttons */\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n/* Animations */\r\n.fade-in {\r\n  animation: fadeIn 0.5s var(--transition-timing);\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(10px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.zoom-in {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.zoom-in:hover {\r\n  transform: scale(1.02);\r\n}\r\n\r\n/* Dark Mode Support */\r\n[data-theme=\"dark\"] .sort-btn {\r\n  color: var(--text-primary);\r\n}\r\n\r\n/* Responsive Adjustments */\r\n@media (max-width: 767.98px) {\r\n  .modern-card-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .modern-card-header button {\r\n    margin-top: 1rem;\r\n    width: 100%;\r\n  }\r\n  \r\n  .action-buttons {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .action-buttons button {\r\n    width: 100%;\r\n    margin-left: 0 !important;\r\n    margin-top: 0.5rem;\r\n  }\r\n  \r\n  .action-buttons button:first-child {\r\n    margin-top: 0;\r\n  }\r\n}\r\n"], "mappings": ";AAGA,CAAC;AACC,UAAQ,KAAK;AACb,kBAAgB;AAClB;AAGA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,iBAAe,IAAI;AACrB;AAGA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,SAAO;AACP,WAAS,EAAE;AACX,UAAQ;AACR,cAAY,IAAI,KAAK;AACvB;AAEA,CATC,QASQ;AACP,WAAS;AACT,aAAW,WAAW;AACxB;AAEA,CAdC,QAcQ;AACP,WAAS;AACT,cAAY;AACd;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,OAAK;AACP;AAGA,CAAC;AACC,aAAW,OAAO,KAAK,IAAI;AAC7B;AAEA,WAHa;AAIX;AAAO,aAAS;AAAG,eAAW,WAAW;AAAO;AAChD;AAAK,aAAS;AAAG,eAAW,WAAW;AAAI;AAC7C;AAEA,CAAC;AACC,cAAY,UAAU,KAAK;AAC7B;AAEA,CAJC,OAIO;AACN,aAAW,MAAM;AACnB;AAGA,CAAC,iBAAmB,CA7CnB;AA8CC,SAAO,IAAI;AACb;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAAC;AACC,oBAAgB;AAChB,iBAAa;AACf;AAEA,GALC,mBAKmB;AAClB,gBAAY;AACZ,WAAO;AACT;AAEA,GAzCD;AA0CG,oBAAgB;AAClB;AAEA,GA7CD,eA6CiB;AACd,WAAO;AACP,iBAAa;AACb,gBAAY;AACd;AAEA,GAnDD,eAmDiB,MAAM;AACpB,gBAAY;AACd;AACF;", "names": []}