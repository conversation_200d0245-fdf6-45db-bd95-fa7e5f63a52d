{"version": 3, "sources": ["src/app/components/crud/member-update/member-update.component.css"], "sourcesContent": ["/* Modern Modal Content Styles */\r\n.modern-modal-content {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  background-color: var(--white);\r\n  border-radius: var(--border-radius-lg);\r\n  box-shadow: var(--shadow-md);\r\n  overflow: hidden;\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n.modern-modal-title {\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: var(--primary);\r\n  margin-bottom: 1.5rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.modern-modal-body {\r\n  position: relative;\r\n  padding: 1.5rem;\r\n}\r\n\r\n.modern-modal-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 1rem;\r\n  margin-top: 2rem;\r\n  padding-top: 1.5rem;\r\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* Form Styles */\r\n.update-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.form-section {\r\n  margin-bottom: 1.5rem;\r\n  padding: 1.25rem;\r\n  background-color: rgba(0, 0, 0, 0.02);\r\n  border-radius: var(--border-radius-md);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.form-section:hover {\r\n  background-color: rgba(0, 0, 0, 0.03);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.section-title {\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  color: var(--secondary-dark);\r\n  margin-bottom: 1.25rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n/* Input Group Styles */\r\n.input-group {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.input-group-text {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 40px;\r\n  height: 40px;\r\n  background-color: var(--primary-light);\r\n  color: var(--primary);\r\n  border: none;\r\n  border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);\r\n}\r\n\r\n.modern-form-control {\r\n  flex: 1;\r\n  height: 40px;\r\n  padding: 0.5rem 1rem;\r\n  border: 1px solid rgba(0, 0, 0, 0.1);\r\n  border-left: none;\r\n  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.modern-form-control:focus {\r\n  outline: none;\r\n  box-shadow: 0 0 0 3px var(--primary-light);\r\n}\r\n\r\n.modern-form-label {\r\n  display: block;\r\n  margin-bottom: 0.5rem;\r\n  font-weight: 500;\r\n  color: var(--secondary-dark);\r\n}\r\n\r\n/* Progress Bar Animation */\r\n.progress {\r\n  height: 6px;\r\n  background-color: rgba(0, 0, 0, 0.05);\r\n  border-radius: var(--border-radius-pill);\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-bar {\r\n  background-color: var(--primary);\r\n  transition: width 0.5s ease;\r\n}\r\n\r\n/* Loading Overlay */\r\n.loading-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(255, 255, 255, 0.8);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 10;\r\n  border-radius: var(--border-radius-lg);\r\n  backdrop-filter: blur(3px);\r\n}\r\n\r\n.spinner-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n}\r\n\r\n/* Animations */\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Dark Mode Support */\r\n@media (prefers-color-scheme: dark) {\r\n  .modern-modal-content {\r\n    background-color: var(--bg-secondary);\r\n    color: var(--text-primary);\r\n  }\r\n  \r\n  .form-section {\r\n    background-color: rgba(255, 255, 255, 0.05);\r\n  }\r\n  \r\n  .form-section:hover {\r\n    background-color: rgba(255, 255, 255, 0.08);\r\n  }\r\n  \r\n  .modern-form-control {\r\n    background-color: var(--bg-tertiary);\r\n    border-color: rgba(255, 255, 255, 0.1);\r\n    color: var(--text-primary);\r\n  }\r\n  \r\n  .loading-overlay {\r\n    background-color: rgba(0, 0, 0, 0.7);\r\n  }\r\n}\r\n  "], "mappings": ";AACA,CAAC;AACC,aAAW;AACX,UAAQ,EAAE;AACV,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,YAAU;AACV,aAAW,OAAO,KAAK;AACzB;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACX,iBAAe;AACf,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,YAAU;AACV,WAAS;AACX;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,OAAK;AACL,cAAY;AACZ,eAAa;AACb,cAAY,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAGA,CAAC;AACC,WAAS;AACT,kBAAgB;AAClB;AAEA,CAAC;AACC,iBAAe;AACf,WAAS;AACT,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,iBAAe,IAAI;AACnB,cAAY,IAAI,KAAK;AACvB;AAEA,CARC,YAQY;AACX,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACX,iBAAe;AACf,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,UAAQ;AACR,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,UAAQ;AACR,iBAAe,IAAI,oBAAoB,EAAE,EAAE,IAAI;AACjD;AAEA,CAAC;AACC,QAAM;AACN,UAAQ;AACR,WAAS,OAAO;AAChB,UAAQ,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,eAAa;AACb,iBAAe,EAAE,IAAI,oBAAoB,IAAI,oBAAoB;AACjE,cAAY,IAAI,KAAK;AACvB;AAEA,CAVC,mBAUmB;AAClB,WAAS;AACT,cAAY,EAAE,EAAE,EAAE,IAAI,IAAI;AAC5B;AAEA,CAAC;AACC,WAAS;AACT,iBAAe;AACf,eAAa;AACb,SAAO,IAAI;AACb;AAGA,CAAC;AACC,UAAQ;AACR,oBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,iBAAe,IAAI;AACnB,YAAU;AACZ;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,cAAY,MAAM,KAAK;AACzB;AAGA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,iBAAe,IAAI;AACnB,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACxB;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,UAAQ;AACV;AAGA,WAtIa;AAuIX;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACA;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACF;AAGA,OAAO,CAAC,oBAAoB,EAAE;AAC5B,GA1JD;AA2JG,sBAAkB,IAAI;AACtB,WAAO,IAAI;AACb;AAEA,GAvHD;AAwHG,sBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,GA3HD,YA2Hc;AACX,sBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,GArFD;AAsFG,sBAAkB,IAAI;AACtB,kBAAc,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAClC,WAAO,IAAI;AACb;AAEA,GAvDD;AAwDG,sBAAkB,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAClC;AACF;", "names": []}