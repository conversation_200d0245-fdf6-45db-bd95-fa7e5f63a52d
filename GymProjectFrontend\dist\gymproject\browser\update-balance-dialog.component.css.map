{"version": 3, "sources": ["angular:styles/component:css;f8697919cdf68fd01ff70f2358500c4f358be3385c14429d8c0d97d18da8b68d;C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectFrontend\\src\\app\\components\\update-balance-dialog\\update-balance-dialog.component.html"], "sourcesContent": ["\n  .modern-dialog {\n    max-width: 500px;\n    width: 100%;\n    background-color: var(--bg-primary);\n    border-radius: var(--border-radius-lg);\n    box-shadow: var(--shadow-lg);\n    overflow: hidden;\n  }\n\n  .modern-dialog-header {\n    padding: var(--spacing-md) var(--spacing-lg);\n    border-bottom: 1px solid var(--border-color);\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    background-color: var(--bg-secondary);\n  }\n\n  .modern-dialog-title {\n    margin: 0;\n    font-size: 1.25rem;\n    font-weight: 600;\n  }\n\n  .modern-dialog-content {\n    padding: var(--spacing-lg);\n  }\n\n  .loading-container {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    min-height: 200px;\n  }\n\n  .member-info {\n    padding: var(--spacing-md);\n    background-color: var(--bg-secondary);\n    border-radius: var(--border-radius-md);\n    transition: all 0.3s ease;\n  }\n\n  .member-info:hover {\n    background-color: var(--bg-tertiary);\n    box-shadow: var(--shadow-sm);\n  }\n"], "mappings": ";AACE,CAAC;AACC,aAAW;AACX,SAAO;AACP,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,YAAU;AACZ;AAEA,CAAC;AACC,WAAS,IAAI,cAAc,IAAI;AAC/B,iBAAe,IAAI,MAAM,IAAI;AAC7B,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,UAAQ;AACR,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,WAAS,IAAI;AACf;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,cAAY;AACd;AAEA,CAAC;AACC,WAAS,IAAI;AACb,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY,IAAI,KAAK;AACvB;AAEA,CAPC,WAOW;AACV,oBAAkB,IAAI;AACtB,cAAY,IAAI;AAClB;", "names": []}