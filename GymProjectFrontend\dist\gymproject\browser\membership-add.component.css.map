{"version": 3, "sources": ["src/app/components/crud/membership-add/membership-add.component.css"], "sourcesContent": [".last-membership-info {\r\n    margin-top: 5px;\r\n    font-size: 0.85em;\r\n    color: #fc4c4c;\r\n  }\r\n\r\n  .form-group {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .mb-3 {\r\n    margin-bottom: 1rem !important;\r\n  }\r\n\r\n  /* Zorunlu alanlar için hata durumu */\r\n  .form-control.ng-invalid.ng-touched {\r\n    border-color: #dc3545;\r\n    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\r\n  }\r\n\r\n  /* Titreşim animasyonu için sınıf */\r\n  .shake-animation {\r\n    animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;\r\n    transform: translate3d(0, 0, 0);\r\n    backface-visibility: hidden;\r\n    perspective: 1000px;\r\n    border-color: #dc3545 !important;\r\n    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5) !important;\r\n  }\r\n\r\n  @keyframes shake {\r\n    0%, 100% { transform: translateX(0); }\r\n    10%, 30%, 50%, 70%, 90% { transform: translateX(-6px); }\r\n    20%, 40%, 60%, 80% { transform: translateX(6px); }\r\n  }\r\n\r\n  /* Loading spinner artık generic component'te yönetiliyor */\r\n\r\n  /* Content Blur */\r\n  .content-blur {\r\n    filter: blur(3px);\r\n    pointer-events: none;\r\n    transition: filter 0.3s ease;\r\n  }\r\n\r\n  /* Fade In Animation */\r\n  .fade-in {\r\n    animation: fadeIn 0.5s ease-out;\r\n  }\r\n\r\n  @keyframes fadeIn {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateY(20px);\r\n    }\r\n    to {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  /* Dark Mode Support */\r\n  [data-theme=\"dark\"] .loading-overlay {\r\n    background-color: rgba(18, 18, 18, 0.8);\r\n  }\r\n\r\n  [data-theme=\"dark\"] .content-blur {\r\n    filter: blur(3px) brightness(0.7);\r\n  }"], "mappings": ";AAAA,CAAC;AACG,cAAY;AACZ,aAAW;AACX,SAAO;AACT;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,iBAAe;AACjB;AAGA,CAAC,YAAY,CAAC,UAAU,CAAC;AACvB,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,OAAO,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC7C;AAGA,CAAC;AACC,aAAW,MAAM,KAAK,aAAa,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK;AACpD,aAAW,YAAY,CAAC,EAAE,CAAC,EAAE;AAC7B,uBAAqB;AACrB,eAAa;AACb,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,QAAQ,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC9C;AAEA,WARa;AASX;AAAW,eAAW,WAAW;AAAI;AACrC;AAA0B,eAAW,WAAW;AAAO;AACvD;AAAqB,eAAW,WAAW;AAAM;AACnD;AAKA,CAAC;AACC,UAAQ,KAAK;AACb,kBAAgB;AAChB,cAAY,OAAO,KAAK;AAC1B;AAGA,CAAC;AACC,aAAW,OAAO,KAAK;AACzB;AAEA,WAHa;AAIX;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACA;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACF;AAGA,CAAC,iBAAmB,CAAC;AACnB,oBAAkB,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACrC;AAEA,CAAC,iBAAmB,CA3BnB;AA4BC,UAAQ,KAAK,KAAK,WAAW;AAC/B;", "names": []}