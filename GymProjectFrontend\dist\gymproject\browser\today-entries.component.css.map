{"version": 3, "sources": ["src/app/components/today-entries/today-entries.component.css"], "sourcesContent": ["/* Today Entries Component Styles */\r\n\r\n/* Loading Spinner */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(255, 255, 255, 0.8);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(3px);\r\n}\r\n\r\n.spinner-container {\r\n  text-align: center;\r\n}\r\n\r\n/* Content Blur */\r\n.content-blur {\r\n  filter: blur(3px);\r\n  pointer-events: none;\r\n}\r\n\r\n/* Fade In Animation */\r\n.fade-in {\r\n  animation: fadeIn 0.5s ease-out;\r\n}\r\n\r\n/* Dashboard Header */\r\n.dashboard-header {\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.visitor-summary-card {\r\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\r\n  color: white;\r\n  text-align: center;\r\n  padding: 1.5rem 2rem 1rem 2rem;\r\n  border-radius: var(--border-radius-lg);\r\n  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.15);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.visitor-summary-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 15px 30px rgba(67, 97, 238, 0.2);\r\n}\r\n\r\n.visitor-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto 0.75rem;\r\n  font-size: 1.75rem;\r\n}\r\n\r\n.visitor-count {\r\n  font-size: 2.75rem;\r\n  font-weight: 700;\r\n  margin: 0.5rem 0;\r\n}\r\n\r\n.date-display {\r\n  font-size: 1rem;\r\n  opacity: 0.8;\r\n  margin-bottom: 0.25rem;\r\n}\r\n\r\n/* Modern Stats Cards */\r\n.modern-stats-card {\r\n  border-radius: var(--border-radius-lg);\r\n  padding: 1.5rem;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.modern-stats-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.modern-stats-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 1.5rem;\r\n  margin-right: 1rem;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  flex-shrink: 0;\r\n}\r\n\r\n.modern-stats-info {\r\n  flex-grow: 1;\r\n}\r\n\r\n.modern-stats-value {\r\n  font-size: 1.75rem;\r\n  font-weight: 700;\r\n  margin-bottom: 0.25rem;\r\n}\r\n\r\n.modern-stats-label {\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.modern-stats-subtext {\r\n  font-size: 0.875rem;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* Background Gradients */\r\n.bg-primary-gradient {\r\n  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);\r\n  color: white;\r\n}\r\n\r\n.bg-success-gradient {\r\n  background: linear-gradient(135deg, #28a745 0%, #208838 100%);\r\n  color: white;\r\n}\r\n\r\n.bg-info-gradient {\r\n  background: linear-gradient(135deg, #4cc9f0 0%, #4895ef 100%);\r\n  color: white;\r\n}\r\n\r\n/* Chart Container */\r\n.chart-container {\r\n  height: 300px;\r\n  margin-top: 2rem;\r\n  position: relative;\r\n}\r\n\r\n/* Member Avatar */\r\n.member-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.member-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-weight: 600;\r\n  margin-right: 0.75rem;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* Table Styles */\r\n.modern-table {\r\n  width: 100%;\r\n  border-collapse: separate;\r\n  border-spacing: 0;\r\n}\r\n\r\n.modern-table th {\r\n  background-color: var(--bg-secondary);\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  font-size: 0.75rem;\r\n  letter-spacing: 0.5px;\r\n  padding: 1rem;\r\n  border-bottom: 2px solid var(--border-color);\r\n}\r\n\r\n.modern-table td {\r\n  padding: 1rem;\r\n  vertical-align: middle;\r\n  border-bottom: 1px solid var(--border-color);\r\n}\r\n\r\n.modern-table tbody tr {\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.modern-table tbody tr:hover {\r\n  background-color: var(--primary-light);\r\n}\r\n\r\n.modern-table tbody tr.active-entry {\r\n  background-color: rgba(40, 167, 69, 0.05);\r\n}\r\n\r\n/* Empty State */\r\n.empty-state {\r\n  padding: 3rem 1rem;\r\n  text-align: center;\r\n  color: var(--text-secondary);\r\n}\r\n\r\n/* Animations */\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(10px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.zoom-in {\r\n  animation: zoomIn 0.5s ease-out;\r\n}\r\n\r\n@keyframes zoomIn {\r\n  from { opacity: 0; transform: scale(0.95); }\r\n  to { opacity: 1; transform: scale(1); }\r\n}\r\n\r\n/* Dark Mode Support */\r\n[data-theme=\"dark\"] .loading-overlay {\r\n  background-color: rgba(18, 18, 18, 0.8);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-table th {\r\n  background-color: var(--bg-tertiary);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-table tbody tr:hover {\r\n  background-color: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-table tbody tr.active-entry {\r\n  background-color: rgba(40, 167, 69, 0.1);\r\n}\r\n\r\n/* Form controls and labels styling for both light and dark modes */\r\n.form-label, .modern-form-label {\r\n  color: #888888 !important; /* Gray color visible in both modes */\r\n  font-weight: bold;\r\n}\r\n\r\ninput::placeholder,\r\ntextarea::placeholder,\r\n.form-control::placeholder,\r\n.search-input::placeholder {\r\n  color: #888888 !important; /* Gray color visible in both modes */\r\n  opacity: 0.9 !important;\r\n}\r\n\r\n/* Specific styling for the search input in today-entries */\r\n.search-container .search-input::placeholder {\r\n  color: #888888 !important; /* Gray color visible in both modes */\r\n  opacity: 0.9 !important;\r\n}\r\n\r\n/* Ensure form controls have proper contrast in dark mode */\r\n[data-theme=\"dark\"] .form-control {\r\n  color: var(--text-primary);\r\n  background-color: var(--bg-tertiary);\r\n  border-color: var(--border-color);\r\n}\r\n\r\n/* Search Button */\r\n.search-btn {\r\n  height: 38px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0 15px;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n/* Responsive Adjustments */\r\n@media (max-width: 767.98px) {\r\n  .visitor-count {\r\n    font-size: 2.5rem;\r\n  }\r\n  \r\n  .visitor-icon {\r\n    width: 60px;\r\n    height: 60px;\r\n    font-size: 1.5rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n  \r\n  .modern-stats-card {\r\n    margin-bottom: 1rem;\r\n  }\r\n  \r\n  .modern-stats-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n    font-size: 1.25rem;\r\n  }\r\n  \r\n  .modern-stats-value {\r\n    font-size: 1.5rem;\r\n  }\r\n  \r\n  .chart-container {\r\n    height: 250px;\r\n  }\r\n  \r\n  .table-actions {\r\n    margin-top: 1rem;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n  }\r\n  \r\n  .table-actions button {\r\n    flex: 1;\r\n  }\r\n}\r\n"], "mappings": ";AAGA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACxB;AAEA,CAAC;AACC,cAAY;AACd;AAGA,CAAC;AACC,UAAQ,KAAK;AACb,kBAAgB;AAClB;AAGA,CAAC;AACC,aAAW,OAAO,KAAK;AACzB;AAGA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,IAAI,gBAAgB;AAC3E,SAAO;AACP,cAAY;AACZ,WAAS,OAAO,KAAK,KAAK;AAC1B,iBAAe,IAAI;AACnB,cAAY,EAAE,KAAK,KAAK,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AAC1C,cAAY,IAAI,KAAK;AACvB;AAEA,CAVC,oBAUoB;AACnB,aAAW,WAAW;AACtB,cAAY,EAAE,KAAK,KAAK,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AAC5C;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,UAAQ,EAAE,KAAK;AACf,aAAW;AACb;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,UAAQ,OAAO;AACjB;AAEA,CAAC;AACC,aAAW;AACX,WAAS;AACT,iBAAe;AACjB;AAGA,CAAC;AACC,iBAAe,IAAI;AACnB,WAAS;AACT,UAAQ;AACR,WAAS;AACT,eAAa;AACb,cAAY,IAAI,KAAK;AACrB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CAVC,iBAUiB;AAChB,aAAW,WAAW;AACtB,cAAY,EAAE,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACxC;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW;AACX,gBAAc;AACd,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,eAAa;AACf;AAEA,CAAC;AACC,aAAW;AACb;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,iBAAe;AACjB;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,iBAAe;AACjB;AAEA,CAAC;AACC,aAAW;AACX,WAAS;AACX;AAGA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACT;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACT;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACT;AAGA,CAAC;AACC,UAAQ;AACR,cAAY;AACZ,YAAU;AACZ;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACf;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,eAAa;AACb,gBAAc;AACd,eAAa;AACf;AAGA,CAAC;AACC,SAAO;AACP,mBAAiB;AACjB,kBAAgB;AAClB;AAEA,CANC,aAMa;AACZ,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,eAAa;AACb,kBAAgB;AAChB,aAAW;AACX,kBAAgB;AAChB,WAAS;AACT,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CAjBC,aAiBa;AACZ,WAAS;AACT,kBAAgB;AAChB,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CAvBC,aAuBa,MAAM;AAClB,cAAY,iBAAiB,KAAK;AACpC;AAEA,CA3BC,aA2Ba,MAAM,EAAE;AACpB,oBAAkB,IAAI;AACxB;AAEA,CA/BC,aA+Ba,MAAM,EAAE,CAAC;AACrB,oBAAkB,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;AACtC;AAGA,CAAC;AACC,WAAS,KAAK;AACd,cAAY;AACZ,SAAO,IAAI;AACb;AAGA,WAvLa;AAwLX;AAAO,aAAS;AAAG,eAAW,WAAW;AAAO;AAChD;AAAK,aAAS;AAAG,eAAW,WAAW;AAAI;AAC7C;AAEA,CAAC;AACC,aAAW,OAAO,KAAK;AACzB;AAEA,WAHa;AAIX;AAAO,aAAS;AAAG,eAAW,MAAM;AAAO;AAC3C;AAAK,aAAS;AAAG,eAAW,MAAM;AAAI;AACxC;AAGA,CAAC,iBAAmB,CAhOnB;AAiOC,oBAAkB,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACrC;AAEA,CAAC,iBAAmB,CA9DnB,aA8DiC;AAChC,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CAlEnB,aAkEiC,MAAM,EAAE;AACxC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,CAAC,iBAAmB,CAtEnB,aAsEiC,MAAM,EAAE,CAvCnB;AAwCrB,oBAAkB,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;AACtC;AAGA,CAAC;AAAY,CAAC;AACZ,SAAO;AACP,eAAa;AACf;AAEA,KAAK;AACL,QAAQ;AACR,CAAC,YAAY;AACb,CAAC,YAAY;AACX,SAAO;AACP,WAAS;AACX;AAGA,CAAC,iBAAiB,CANjB,YAM8B;AAC7B,SAAO;AACP,WAAS;AACX;AAGA,CAAC,iBAAmB,CAbnB;AAcC,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAGA,CAAC;AACC,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,WAAS,EAAE;AACX,aAAW;AACb;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA1ND;AA2NG,eAAW;AACb;AAEA,GA1OD;AA2OG,WAAO;AACP,YAAQ;AACR,eAAW;AACX,mBAAe;AACjB;AAEA,GAxND;AAyNG,mBAAe;AACjB;AAEA,GA7MD;AA8MG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AAEA,GAlMD;AAmMG,eAAW;AACb;AAEA,GApKD;AAqKG,YAAQ;AACV;AAEA,GAAC;AACC,gBAAY;AACZ,aAAS;AACT,qBAAiB;AACjB,WAAO;AACT;AAEA,GAPC,cAOc;AACb,UAAM;AACR;AACF;", "names": []}