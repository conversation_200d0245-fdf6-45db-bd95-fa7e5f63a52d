{"version": 3, "sources": ["angular:styles/component:css;eb4a369c8b78a241753b8a9411fcfeb9f5103ecacb4fc9e9e290b09210114a12;C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectFrontend\\src\\app\\components\\operation-claim\\operation-claim.component.html"], "sourcesContent": ["\n  .bg-primary-light {\n    background-color: var(--primary-light);\n  }\n  \n  .bg-success-light {\n    background-color: var(--success-light);\n  }\n  \n  .bg-info-light {\n    background-color: var(--info-light);\n  }\n  \n  .bg-primary {\n    background-color: var(--primary);\n  }\n  \n  .bg-success {\n    background-color: var(--success);\n  }\n  \n  .bg-info {\n    background-color: var(--info);\n  }\n  \n  .role-icon {\n    width: 32px;\n    height: 32px;\n    border-radius: 50%;\n    background-color: var(--primary-light);\n    color: var(--primary);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .content-blur {\n    filter: blur(3px);\n    pointer-events: none;\n  }\n"], "mappings": ";AACE,CAAC;AACC,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,WAAS;AACT,eAAa;AACb,mBAAiB;AACnB;AAEA,CAAC;AACC,UAAQ,KAAK;AACb,kBAAgB;AAClB;", "names": []}