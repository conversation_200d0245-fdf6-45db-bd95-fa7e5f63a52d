{"version": 3, "sources": ["src/app/components/member-workout-assignments/member-workout-assign-modal.component.css"], "sourcesContent": ["/* Member Workout Assign Mo<PERSON> Styles */\n\n/* Modal Header */\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: var(--spacing-lg);\n  border-bottom: 1px solid var(--border-color);\n  background-color: var(--bg-secondary);\n}\n\n.modal-title {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n\n.modal-icon {\n  color: var(--primary);\n}\n\n.modal-close-btn {\n  background: none;\n  border: none;\n  font-size: 1.25rem;\n  color: var(--text-secondary);\n  cursor: pointer;\n  padding: var(--spacing-xs);\n  border-radius: var(--border-radius-sm);\n  transition: all var(--transition-speed) ease;\n}\n\n.modal-close-btn:hover:not(:disabled) {\n  color: var(--danger);\n  background-color: var(--danger-light);\n}\n\n.modal-close-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* Modal Body */\n.modal-body {\n  padding: var(--spacing-lg);\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n/* Form Styles */\n.form-icon {\n  margin-right: var(--spacing-xs);\n  color: var(--primary);\n  width: 16px;\n}\n\n\n\n.required::after {\n  content: ' *';\n  color: var(--danger);\n}\n\n.is-invalid {\n  border-color: var(--danger) !important;\n  box-shadow: 0 0 0 0.2rem var(--danger-light) !important;\n}\n\n.invalid-feedback {\n  display: block;\n  width: 100%;\n  margin-top: var(--spacing-xs);\n  font-size: 0.875rem;\n  color: var(--danger);\n}\n\n.form-text {\n  display: block;\n  margin-top: var(--spacing-xs);\n  font-size: 0.75rem;\n  color: var(--text-muted);\n}\n\n\n\n/* Checkbox Styles */\n.form-check-container {\n  margin-top: var(--spacing-xs);\n}\n\n.form-check-label {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  font-weight: 500;\n  color: var(--text-primary);\n  position: relative;\n  padding-left: 2rem;\n}\n\n.form-check-input {\n  position: absolute;\n  opacity: 0;\n  cursor: pointer;\n  height: 0;\n  width: 0;\n}\n\n.checkmark {\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  height: 20px;\n  width: 20px;\n  background-color: var(--bg-primary);\n  border: 2px solid var(--border-color);\n  border-radius: var(--border-radius-sm);\n  transition: all var(--transition-speed) ease;\n}\n\n.form-check-input:checked ~ .checkmark {\n  background-color: var(--primary);\n  border-color: var(--primary);\n}\n\n.checkmark:after {\n  content: \"\";\n  position: absolute;\n  display: none;\n  left: 6px;\n  top: 2px;\n  width: 6px;\n  height: 12px;\n  border: solid white;\n  border-width: 0 2px 2px 0;\n  transform: rotate(45deg);\n}\n\n.form-check-input:checked ~ .checkmark:after {\n  display: block;\n}\n\n/* Selected Info Preview */\n.selected-info-preview {\n  margin-top: var(--spacing-lg);\n}\n\n.preview-card {\n  background-color: var(--bg-secondary);\n  border: 1px solid var(--border-color);\n  border-radius: var(--border-radius-md);\n  padding: var(--spacing-md);\n}\n\n.preview-card h6 {\n  margin: 0 0 var(--spacing-sm) 0;\n  font-weight: 600;\n  color: var(--text-primary);\n  font-size: 0.875rem;\n}\n\n.preview-item {\n  margin-bottom: var(--spacing-xs);\n  font-size: 0.875rem;\n  color: var(--text-secondary);\n}\n\n.preview-item strong {\n  color: var(--text-primary);\n}\n\n/* Modal Footer */\n.modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: var(--spacing-sm);\n  padding: var(--spacing-lg);\n  border-top: 1px solid var(--border-color);\n  background-color: var(--bg-secondary);\n}\n\n.modal-footer .modern-btn {\n  min-width: 100px;\n}\n\n/* Loading State */\n.modal-footer .modern-btn .fa-spinner {\n  margin-right: var(--spacing-xs);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .modal-header {\n    padding: var(--spacing-md);\n  }\n\n  .modal-body {\n    padding: var(--spacing-md);\n    max-height: 60vh;\n  }\n\n  .modal-footer {\n    padding: var(--spacing-md);\n    flex-direction: column;\n  }\n\n  .modal-footer .modern-btn {\n    width: 100%;\n  }\n\n\n\n  .modal-title {\n    font-size: 1.125rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .modal-header {\n    padding: var(--spacing-sm);\n  }\n\n  .modal-body {\n    padding: var(--spacing-sm);\n  }\n\n  .modal-footer {\n    padding: var(--spacing-sm);\n  }\n\n  .modal-title {\n    font-size: 1rem;\n  }\n}\n\n/* Dark Mode Adjustments */\n[data-theme=\"dark\"] .modal-header {\n  background-color: var(--bg-secondary);\n  border-bottom-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .modal-title {\n  color: var(--text-primary);\n}\n\n[data-theme=\"dark\"] .modal-close-btn {\n  color: var(--text-secondary);\n}\n\n[data-theme=\"dark\"] .modal-close-btn:hover:not(:disabled) {\n  color: var(--danger);\n  background-color: var(--danger-light);\n}\n\n[data-theme=\"dark\"] .form-check-label {\n  color: var(--text-primary);\n}\n\n[data-theme=\"dark\"] .checkmark {\n  background-color: var(--bg-primary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .preview-card {\n  background-color: var(--bg-secondary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .preview-card h6 {\n  color: var(--text-primary);\n}\n\n[data-theme=\"dark\"] .preview-item {\n  color: var(--text-secondary);\n}\n\n[data-theme=\"dark\"] .preview-item strong {\n  color: var(--text-primary);\n}\n\n[data-theme=\"dark\"] .modal-footer {\n  background-color: var(--bg-secondary);\n  border-top-color: var(--border-color);\n}\n\n\n"], "mappings": ";AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS,IAAI;AACb,iBAAe,IAAI,MAAM,IAAI;AAC7B,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,UAAQ;AACR,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACX,WAAS;AACT,eAAa;AACb,OAAK,IAAI;AACX;AAEA,CAAC;AACC,SAAO,IAAI;AACb;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,aAAW;AACX,SAAO,IAAI;AACX,UAAQ;AACR,WAAS,IAAI;AACb,iBAAe,IAAI;AACnB,cAAY,IAAI,IAAI,oBAAoB;AAC1C;AAEA,CAXC,eAWe,MAAM,KAAK;AACzB,SAAO,IAAI;AACX,oBAAkB,IAAI;AACxB;AAEA,CAhBC,eAgBe;AACd,WAAS;AACT,UAAQ;AACV;AAGA,CAAC;AACC,WAAS,IAAI;AACb,cAAY;AACZ,cAAY;AACd;AAGA,CAAC;AACC,gBAAc,IAAI;AAClB,SAAO,IAAI;AACX,SAAO;AACT;AAIA,CAAC,QAAQ;AACP,WAAS;AACT,SAAO,IAAI;AACb;AAEA,CAAC;AACC,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,OAAO,IAAI;AAC/B;AAEA,CAAC;AACC,WAAS;AACT,SAAO;AACP,cAAY,IAAI;AAChB,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS;AACT,cAAY,IAAI;AAChB,aAAW;AACX,SAAO,IAAI;AACb;AAKA,CAAC;AACC,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,UAAQ;AACR,eAAa;AACb,SAAO,IAAI;AACX,YAAU;AACV,gBAAc;AAChB;AAEA,CAAC;AACC,YAAU;AACV,WAAS;AACT,UAAQ;AACR,UAAQ;AACR,SAAO;AACT;AAEA,CAAC;AACC,YAAU;AACV,QAAM;AACN,OAAK;AACL,aAAW,WAAW;AACtB,UAAQ;AACR,SAAO;AACP,oBAAkB,IAAI;AACtB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY,IAAI,IAAI,oBAAoB;AAC1C;AAEA,CArBC,gBAqBgB,SAAS,EAAE,CAb3B;AAcC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAlBC,SAkBS;AACR,WAAS;AACT,YAAU;AACV,WAAS;AACT,QAAM;AACN,OAAK;AACL,SAAO;AACP,UAAQ;AACR,UAAQ,MAAM;AACd,gBAAc,EAAE,IAAI,IAAI;AACxB,aAAW,OAAO;AACpB;AAEA,CAvCC,gBAuCgB,SAAS,EAAE,CA/B3B,SA+BqC;AACpC,WAAS;AACX;AAGA,CAAC;AACC,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,WAAS,IAAI;AACf;AAEA,CAPC,aAOa;AACZ,UAAQ,EAAE,EAAE,IAAI,cAAc;AAC9B,eAAa;AACb,SAAO,IAAI;AACX,aAAW;AACb;AAEA,CAAC;AACC,iBAAe,IAAI;AACnB,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CANC,aAMa;AACZ,SAAO,IAAI;AACb;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,OAAK,IAAI;AACT,WAAS,IAAI;AACb,cAAY,IAAI,MAAM,IAAI;AAC1B,oBAAkB,IAAI;AACxB;AAEA,CATC,aASa,CAAC;AACb,aAAW;AACb;AAGA,CAdC,aAca,CALC,WAKW,CAAC;AACzB,gBAAc,IAAI;AACpB;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAnMD;AAoMG,aAAS,IAAI;AACf;AAEA,GA1JD;AA2JG,aAAS,IAAI;AACb,gBAAY;AACd;AAEA,GA7BD;AA8BG,aAAS,IAAI;AACb,oBAAgB;AAClB;AAEA,GAlCD,aAkCe,CAzBD;AA0BX,WAAO;AACT;AAIA,GA9MD;AA+MG,eAAW;AACb;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GA7ND;AA8NG,aAAS,IAAI;AACf;AAEA,GApLD;AAqLG,aAAS,IAAI;AACf;AAEA,GAtDD;AAuDG,aAAS,IAAI;AACf;AAEA,GAhOD;AAiOG,eAAW;AACb;AACF;AAGA,CAAC,iBAAmB,CA/OnB;AAgPC,oBAAkB,IAAI;AACtB,uBAAqB,IAAI;AAC3B;AAEA,CAAC,iBAAmB,CA3OnB;AA4OC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAjOnB;AAkOC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CArOnB,eAqOmC,MAAM,KAAK;AAC7C,SAAO,IAAI;AACX,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CArKnB;AAsKC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAvJnB;AAwJC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CApHnB;AAqHC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAzHnB,aAyHiC;AAChC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA/GnB;AAgHC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAnHnB,aAmHiC;AAChC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA5GnB;AA6GC,oBAAkB,IAAI;AACtB,oBAAkB,IAAI;AACxB;", "names": []}