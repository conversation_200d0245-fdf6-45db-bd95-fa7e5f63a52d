using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IExpenseService
    {
        IDataResult<List<Expense>> GetAll(); // Belki DTO daha uygun olabilir, şimdilik Entity
        IDataResult<Expense> GetById(int expenseId);
        IResult Add(Expense expense);
        IResult Update(Expense expense);
        IResult Delete(int expenseId);

        // Filtreleme ve Raporlama için ek metotlar
        IDataResult<List<ExpenseDto>> GetExpensesByDateRange(DateTime startDate, DateTime endDate);
        IDataResult<List<ExpenseDto>> GetMonthlyExpenses(int year, int month);
        IDataResult<decimal> GetTotalMonthlyExpenses(int year, int month); // Aylık toplam gider
        IDataResult<decimal> GetTotalDailyExpenses(DateTime date); // Günlük toplam
        IDataResult<decimal> GetTotalYearlyExpenses(int year); // Yıllık toplam
        IDataResult<Dictionary<int, decimal>> GetMonthlyExpenseSummary(int year); // Yıllık aylık özet

        // Dashboard için optimize edilmiş tek metot
        IDataResult<ExpenseDashboardDto> GetExpenseDashboardData(int year, int month); // Tüm dashboard verilerini tek seferde getir
    }
}