{"version": 3, "sources": ["src/app/components/change-password/change-password.component.css"], "sourcesContent": [".main-content {\r\n  padding: 30px 0;\r\n  min-height: calc(100vh - 70px);\r\n}\r\n\r\n.card {\r\n  border: none;\r\n  border-radius: 8px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n  background-color: var(--card-bg-color);\r\n  margin-bottom: 30px;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-header {\r\n  background-color: var(--card-bg-color);\r\n  border-bottom: 1px solid var(--border-color);\r\n  padding: 20px 25px;\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n  color: var(--text-color);\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.card-title i {\r\n  color: var(--primary-color);\r\n}\r\n\r\n.card-body {\r\n  padding: 25px;\r\n}\r\n\r\n.alert {\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 10px;\r\n}\r\n\r\n.alert i {\r\n  margin-top: 3px;\r\n}\r\n\r\n.alert-warning {\r\n  background-color: rgba(255, 193, 7, 0.1);\r\n  border-left: 4px solid #ffc107;\r\n  color: var(--text-color);\r\n}\r\n\r\n.alert-info {\r\n  background-color: rgba(13, 202, 240, 0.1);\r\n  border-left: 4px solid #0dcaf0;\r\n  color: var(--text-color);\r\n}\r\n\r\n.modern-form {\r\n  margin-top: 20px;\r\n}\r\n\r\n.form-row {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.modern-form-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n  color: var(--text-color);\r\n}\r\n\r\n.input-group {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: stretch;\r\n  width: 100%;\r\n}\r\n\r\n.input-group-text {\r\n  background-color: var(--input-bg);\r\n  border: 1px solid var(--input-border);\r\n  border-right: none;\r\n  color: var(--text-muted);\r\n  border-top-left-radius: 8px;\r\n  border-bottom-left-radius: 8px;\r\n  padding: 0.5rem 1rem;\r\n}\r\n\r\n.modern-form-control {\r\n  flex: 1;\r\n  padding: 0.5rem 1rem;\r\n  font-size: 1rem;\r\n  border: 1px solid var(--input-border);\r\n  background-color: var(--input-bg);\r\n  color: var(--input-text);\r\n  border-radius: 0;\r\n  transition: border-color 0.3s, box-shadow 0.3s;\r\n}\r\n\r\n.modern-form-control:focus {\r\n  border-color: var(--primary-color);\r\n  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);\r\n  outline: none;\r\n}\r\n\r\n.modern-form-control.is-invalid {\r\n  border-color: #dc3545;\r\n}\r\n\r\n.password-toggle {\r\n  border-top-left-radius: 0;\r\n  border-bottom-left-radius: 0;\r\n  border-top-right-radius: 8px;\r\n  border-bottom-right-radius: 8px;\r\n  border: 1px solid var(--input-border);\r\n  background-color: var(--input-bg);\r\n  color: var(--text-muted);\r\n  padding: 0.5rem 1rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.password-toggle:hover {\r\n  color: var(--primary-color);\r\n}\r\n\r\n.invalid-feedback {\r\n  display: block;\r\n  width: 100%;\r\n  margin-top: 0.25rem;\r\n  font-size: 0.875rem;\r\n  color: #dc3545;\r\n}\r\n\r\n.password-requirements {\r\n  background-color: var(--card-background-secondary, #2d2d2d);\r\n  border: none;\r\n  border-radius: 8px;\r\n}\r\n\r\n.password-requirements .card-title {\r\n  font-size: 1rem;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.requirements-list {\r\n  list-style: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.requirements-list li {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin-bottom: 5px;\r\n  color: var(--text-secondary, #a0a0a0);\r\n}\r\n\r\n.requirements-list li.fulfilled {\r\n  color: var(--text-color, #e0e0e0);\r\n}\r\n\r\n.btn-primary {\r\n  background-color: var(--primary-color, #4361ee);\r\n  border-color: var(--primary-color, #4361ee);\r\n  color: white;\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.btn-primary:hover:not(:disabled) {\r\n  background-color: var(--secondary-color, #3f37c9);\r\n  border-color: var(--secondary-color, #3f37c9);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.btn-primary:disabled {\r\n  background-color: var(--text-muted, #6c757d);\r\n  border-color: var(--text-muted, #6c757d);\r\n  cursor: not-allowed;\r\n  opacity: 0.7;\r\n}\r\n\r\n.support {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  color: var(--text-muted, #6c757d);\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.support i {\r\n  color: var(--primary-color, #4361ee);\r\n}\r\n\r\n.support a {\r\n  color: var(--primary-color, #4361ee);\r\n  text-decoration: none;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.support a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n/* Responsive styles */\r\n@media (max-width: 768px) {\r\n  .card-body {\r\n    padding: 20px 15px;\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 1.3rem;\r\n  }\r\n\r\n  .btn-lg {\r\n    padding: 8px 16px;\r\n    font-size: 0.95rem;\r\n  }\r\n}"], "mappings": ";AAAA,CAAC;AACC,WAAS,KAAK;AACd,cAAY,KAAK,MAAM,EAAE;AAC3B;AAEA,CAAC;AACC,UAAQ;AACR,iBAAe;AACf,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACrC,oBAAkB,IAAI;AACtB,iBAAe;AACf,YAAU;AACZ;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,iBAAe,IAAI,MAAM,IAAI;AAC7B,WAAS,KAAK;AAChB;AAEA,CAAC;AACC,UAAQ;AACR,SAAO,IAAI;AACX,aAAW;AACX,eAAa;AACb,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAVC,WAUW;AACV,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS;AACX;AAEA,CAAC;AACC,iBAAe;AACf,WAAS;AACT,iBAAe;AACf,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CATC,MASM;AACL,cAAY;AACd;AAEA,CAAC;AACC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE;AACpC,eAAa,IAAI,MAAM;AACvB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,oBAAkB,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACrC,eAAa,IAAI,MAAM;AACvB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,cAAY;AACd;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,iBAAe;AACf,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAAC;AACC,YAAU;AACV,WAAS;AACT,eAAa;AACb,SAAO;AACT;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,UAAQ,IAAI,MAAM,IAAI;AACtB,gBAAc;AACd,SAAO,IAAI;AACX,0BAAwB;AACxB,6BAA2B;AAC3B,WAAS,OAAO;AAClB;AAEA,CAAC;AACC,QAAM;AACN,WAAS,OAAO;AAChB,aAAW;AACX,UAAQ,IAAI,MAAM,IAAI;AACtB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,iBAAe;AACf,cAAY,aAAa,IAAI,EAAE,WAAW;AAC5C;AAEA,CAXC,mBAWmB;AAClB,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,OAAO,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AAC3C,WAAS;AACX;AAEA,CAjBC,mBAiBmB,CAAC;AACnB,gBAAc;AAChB;AAEA,CAAC;AACC,0BAAwB;AACxB,6BAA2B;AAC3B,2BAAyB;AACzB,8BAA4B;AAC5B,UAAQ,IAAI,MAAM,IAAI;AACtB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,WAAS,OAAO;AAChB,UAAQ;AACV;AAEA,CAZC,eAYe;AACd,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS;AACT,SAAO;AACP,cAAY;AACZ,aAAW;AACX,SAAO;AACT;AAEA,CAAC;AACC,oBAAkB,IAAI,2BAA2B,EAAE;AACnD,UAAQ;AACR,iBAAe;AACjB;AAEA,CANC,sBAMsB,CAlItB;AAmIC,aAAW;AACX,iBAAe;AACjB;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,UAAQ;AACV;AAEA,CANC,kBAMkB;AACjB,WAAS;AACT,eAAa;AACb,OAAK;AACL,iBAAe;AACf,SAAO,IAAI,gBAAgB,EAAE;AAC/B;AAEA,CAdC,kBAckB,EAAE,CAAC;AACpB,SAAO,IAAI,YAAY,EAAE;AAC3B;AAEA,CAAC;AACC,oBAAkB,IAAI,eAAe,EAAE;AACvC,gBAAc,IAAI,eAAe,EAAE;AACnC,SAAO;AACP,iBAAe;AACf,WAAS,KAAK;AACd,eAAa;AACb,cAAY,IAAI,KAAK;AACvB;AAEA,CAVC,WAUW,MAAM,KAAK;AACrB,oBAAkB,IAAI,iBAAiB,EAAE;AACzC,gBAAc,IAAI,iBAAiB,EAAE;AACrC,aAAW,WAAW;AACxB;AAEA,CAhBC,WAgBW;AACV,oBAAkB,IAAI,YAAY,EAAE;AACpC,gBAAc,IAAI,YAAY,EAAE;AAChC,UAAQ;AACR,WAAS;AACX;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,OAAK;AACL,SAAO,IAAI,YAAY,EAAE;AACzB,aAAW;AACb;AAEA,CATC,QASQ;AACP,SAAO,IAAI,eAAe,EAAE;AAC9B;AAEA,CAbC,QAaQ;AACP,SAAO,IAAI,eAAe,EAAE;AAC5B,mBAAiB;AACjB,cAAY,MAAM;AACpB;AAEA,CAnBC,QAmBQ,CAAC;AACR,mBAAiB;AACnB;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA3LD;AA4LG,aAAS,KAAK;AAChB;AAEA,GA7MD;AA8MG,eAAW;AACb;AAEA,GAAC;AACC,aAAS,IAAI;AACb,eAAW;AACb;AACF;", "names": []}