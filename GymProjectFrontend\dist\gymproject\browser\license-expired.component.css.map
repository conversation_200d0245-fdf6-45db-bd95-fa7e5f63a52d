{"version": 3, "sources": ["src/app/components/license-expired/license-expired.component.css"], "sourcesContent": [".license-expired-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 100vh;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.expired-content {\r\n  background: white;\r\n  padding: 2rem;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  text-align: center;\r\n  max-width: 400px;\r\n  width: 90%;\r\n}\r\n\r\nh2 {\r\n  color: #dc3545;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\np {\r\n  color: #6c757d;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 1rem;\r\n  justify-content: center;\r\n}\r\n\r\n.btn {\r\n  padding: 0.5rem 1.5rem;\r\n  border-radius: 4px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.btn-primary {\r\n  background-color: #007bff;\r\n  border: 1px solid #0056b3;\r\n  color: white;\r\n}\r\n\r\n.btn-primary:hover {\r\n  background-color: #0056b3;\r\n}\r\n\r\n.btn-secondary {\r\n  background-color: #6c757d;\r\n  border: 1px solid #545b62;\r\n  color: white;\r\n}\r\n\r\n.btn-secondary:hover {\r\n  background-color: #545b62;\r\n}\r\n"], "mappings": ";AAAA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,cAAY;AACZ,oBAAkB;AACpB;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,iBAAe;AACf,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,cAAY;AACZ,aAAW;AACX,SAAO;AACT;AAEA;AACE,SAAO;AACP,iBAAe;AACjB;AAEA;AACE,SAAO;AACP,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,mBAAiB;AACnB;AAEA,CAAC;AACC,WAAS,OAAO;AAChB,iBAAe;AACf,eAAa;AACb,UAAQ;AACR,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,oBAAkB;AAClB,UAAQ,IAAI,MAAM;AAClB,SAAO;AACT;AAEA,CANC,WAMW;AACV,oBAAkB;AACpB;AAEA,CAAC;AACC,oBAAkB;AAClB,UAAQ,IAAI,MAAM;AAClB,SAAO;AACT;AAEA,CANC,aAMa;AACZ,oBAAkB;AACpB;", "names": []}