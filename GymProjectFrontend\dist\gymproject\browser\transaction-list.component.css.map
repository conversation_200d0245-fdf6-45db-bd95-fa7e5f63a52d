{"version": 3, "sources": ["src/app/components/transaction-list/transaction-list.component.css"], "sourcesContent": [".content-blur {\r\n  filter: blur(2px);\r\n  pointer-events: none;\r\n}\r\n\r\n.table thead th {\r\n  background-color: #4a7299;\r\n  color: white;\r\n  padding: 12px;\r\n}\r\n\r\n.table td {\r\n  vertical-align: middle;\r\n  padding: 10px;\r\n}\r\n\r\n.badge {\r\n  padding: 8px 12px;\r\n  font-size: 0.9em;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.badge:hover {\r\n  opacity: 0.8;\r\n}\r\n\r\n.search-container {\r\n  min-width: 250px;\r\n}\r\n\r\n.total-amount {\r\n  font-size: 1.1em;\r\n  color: #dc3545;\r\n  padding: 10px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.member-group {\r\n  border: 1px solid #dee2e6;\r\n  border-radius: 4px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.05);\r\n}\r\n\r\n.member-group .bg-light {\r\n  border-top-left-radius: 4px;\r\n  border-top-right-radius: 4px;\r\n}\r\n\r\n.product-summary {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.product-summary .badge {\r\n  font-size: 0.85em;\r\n  background-color: rgba(0,123,255,0.1) !important;\r\n  color: #0056b3 !important;\r\n  border: 1px solid rgba(0,123,255,0.2);\r\n  padding: 4px 8px;\r\n}\r\n\r\n/* Toplam Borç Styling */\r\n.total-debt-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 12px;\r\n  background-color: var(--bg-secondary, #f8f9fa);\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.total-debt-label {\r\n  font-weight: 600;\r\n  color: var(--text-primary, #495057);\r\n}\r\n\r\n.total-debt-amount {\r\n  font-weight: 700;\r\n  font-size: 1.1em;\r\n  color: var(--danger, #dc3545);\r\n  letter-spacing: 0.5px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* Dark mode compatibility */\r\n@media (prefers-color-scheme: dark) {\r\n  .total-debt-amount {\r\n    color: var(--danger, #ff6b6b);\r\n  }\r\n  \r\n  .total-debt-label {\r\n    color: var(--text-primary, #e9ecef);\r\n  }\r\n  \r\n  .total-debt-container {\r\n    background-color: var(--bg-secondary, #343a40);\r\n  }\r\n}\r\n\r\n/* Hover effect */\r\n.total-debt-container:hover .total-debt-amount {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.summary-box .card {\r\n  transition: transform 0.2s ease;\r\n}\r\n\r\n.summary-box .card:hover {\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.month-filter input {\r\n  min-width: 150px;\r\n}\r\n\r\n.calendar-icon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 36px;\r\n  height: 36px;\r\n  background-color: #f8f9fa;\r\n  border: 1px solid #ced4da;\r\n  border-radius: 4px;\r\n  color: #495057;\r\n}\r\n\r\n.calendar-icon i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* Responsive tasarım için ek stiller */\r\n@media (max-width: 768px) {\r\n  .card-header {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .d-flex.align-items-center {\r\n    flex-direction: column;\r\n    width: 100%;\r\n  }\r\n\r\n  .search-container,\r\n  .month-filter {\r\n    width: 100%;\r\n  }\r\n\r\n  .product-summary {\r\n    margin-bottom: 0.5rem;\r\n  }\r\n\r\n  .total-amount {\r\n    margin-bottom: 0.5rem;\r\n  }\r\n}\r\n\r\n/* Segmented Control Styling */\r\n.transaction-view-selector {\r\n  padding: 0 var(--spacing-lg); /* Align with card body horizontal padding */\r\n  margin-top: var(--spacing-md); /* Add space below header */\r\n  margin-bottom: var(--spacing-md); /* Add space above card body */\r\n  display: flex; /* Center the control */\r\n  justify-content: center; /* Center the control */\r\n}\r\n\r\n.segmented-control {\r\n  display: inline-flex; /* Use inline-flex to wrap buttons */\r\n  background-color: var(--bg-tertiary); /* Slightly different background */\r\n  border-radius: var(--border-radius-md);\r\n  padding: var(--spacing-xs); /* Small padding around buttons */\r\n  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1); /* Inner shadow for depth */\r\n}\r\n\r\n.segment-button {\r\n  padding: var(--spacing-sm) var(--spacing-md);\r\n  border: none;\r\n  background-color: transparent; /* Initially transparent */\r\n  color: var(--text-secondary);\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  border-radius: var(--border-radius-sm); /* Slightly rounded corners for buttons */\r\n  transition: background-color var(--transition-speed) var(--transition-timing),\r\n              color var(--transition-speed) var(--transition-timing),\r\n              box-shadow var(--transition-speed) var(--transition-timing);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-grow: 1; /* Make buttons take equal space if needed */\r\n  text-align: center;\r\n}\r\n\r\n.segment-button:hover:not(.active) {\r\n  background-color: rgba(var(--primary-rgb), 0.05); /* Subtle hover effect */\r\n  color: var(--text-primary);\r\n}\r\n\r\n.segment-button.active {\r\n  background-color: var(--white); /* Active button background */\r\n  color: var(--primary); /* Active button text color */\r\n  font-weight: 600;\r\n  box-shadow: var(--shadow-sm); /* Shadow for active button */\r\n}\r\n\r\n.segment-button i {\r\n  transition: color var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n/* Icon colors */\r\n.segment-button.active i {\r\n  color: var(--primary);\r\n}\r\n\r\n.segment-button:not(.active) i {\r\n  color: var(--text-secondary);\r\n}\r\n.segment-button:hover:not(.active) i {\r\n   color: var(--text-primary);\r\n}\r\n\r\n\r\n/* Dark Mode Adjustments for Segmented Control */\r\n[data-theme=\"dark\"] .segmented-control {\r\n  background-color: var(--bg-secondary); /* Darker background for container */\r\n  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n[data-theme=\"dark\"] .segment-button {\r\n  color: var(--text-secondary);\r\n}\r\n\r\n[data-theme=\"dark\"] .segment-button:hover:not(.active) {\r\n  background-color: rgba(var(--primary-rgb), 0.1); /* Adjusted hover for dark */\r\n  color: var(--text-primary);\r\n}\r\n\r\n[data-theme=\"dark\"] .segment-button.active {\r\n  background-color: var(--bg-tertiary); /* Active button background in dark */\r\n  color: var(--primary); /* Primary color adjusted for dark mode */\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n[data-theme=\"dark\"] .segment-button.active i {\r\n  color: var(--primary);\r\n}\r\n\r\n[data-theme=\"dark\"] .segment-button:not(.active) i {\r\n  color: var(--text-secondary);\r\n}\r\n\r\n[data-theme=\"dark\"] .segment-button:hover:not(.active) i {\r\n   color: var(--text-primary);\r\n}"], "mappings": ";AAAA,CAAC;AACC,UAAQ,KAAK;AACb,kBAAgB;AAClB;AAEA,CAAC,MAAM,MAAM;AACX,oBAAkB;AAClB,SAAO;AACP,WAAS;AACX;AAEA,CANC,MAMM;AACL,kBAAgB;AAChB,WAAS;AACX;AAEA,CAAC;AACC,WAAS,IAAI;AACb,aAAW;AACX,UAAQ;AACR,cAAY,IAAI,KAAK;AACvB;AAEA,CAPC,KAOK;AACJ,WAAS;AACX;AAEA,CAAC;AACC,aAAW;AACb;AAEA,CAAC;AACC,aAAW;AACX,SAAO;AACP,WAAS;AACT,oBAAkB;AAClB,iBAAe;AACjB;AAEA,CAAC;AACC,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,iBAAe;AACf,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AACnC;AAEA,CAPC,aAOa,CAAC;AACb,0BAAwB;AACxB,2BAAyB;AAC3B;AAEA,CAAC;AACC,WAAS;AACT,aAAW;AACX,OAAK;AACP;AAEA,CANC,gBAMgB,CAzChB;AA0CC,aAAW;AACX,oBAAkB,KAAK,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC;AACjC,SAAO;AACP,UAAQ,IAAI,MAAM,KAAK,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC;AACjC,WAAS,IAAI;AACf;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,WAAS,IAAI;AACb,oBAAkB,IAAI,cAAc,EAAE;AACtC,iBAAe;AACf,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI,cAAc,EAAE;AAC7B;AAEA,CAAC;AACC,eAAa;AACb,aAAW;AACX,SAAO,IAAI,QAAQ,EAAE;AACrB,kBAAgB;AAChB,cAAY,IAAI,KAAK;AACvB;AAGA,OAAO,CAAC,oBAAoB,EAAE;AAC5B,GAVD;AAWG,WAAO,IAAI,QAAQ,EAAE;AACvB;AAEA,GAnBD;AAoBG,WAAO,IAAI,cAAc,EAAE;AAC7B;AAEA,GAjCD;AAkCG,sBAAkB,IAAI,cAAc,EAAE;AACxC;AACF;AAGA,CAvCC,oBAuCoB,OAAO,CAxB3B;AAyBC,aAAW,MAAM;AACnB;AAEA,CAAC,YAAY,CAAC;AACZ,cAAY,UAAU,KAAK;AAC7B;AAEA,CAJC,YAIY,CAJC,IAII;AAChB,aAAW,WAAW;AACxB;AAEA,CAAC,aAAa;AACZ,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,UAAQ;AACR,oBAAkB;AAClB,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,SAAO;AACT;AAEA,CAZC,cAYc;AACb,aAAW;AACb;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAAC;AACC,oBAAgB;AAChB,SAAK;AACP;AAEA,GAAC,MAAM,CAAC;AACN,oBAAgB;AAChB,WAAO;AACT;AAEA,GA1HD;AAAA,EA2HC,CAjCD;AAkCG,WAAO;AACT;AAEA,GAvGD;AAwGG,mBAAe;AACjB;AAEA,GA/HD;AAgIG,mBAAe;AACjB;AACF;AAGA,CAAC;AACC,WAAS,EAAE,IAAI;AACf,cAAY,IAAI;AAChB,iBAAe,IAAI;AACnB,WAAS;AACT,mBAAiB;AACnB;AAEA,CAAC;AACC,WAAS;AACT,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,WAAS,IAAI;AACb,cAAY,MAAM,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC5C;AAEA,CAAC;AACC,WAAS,IAAI,cAAc,IAAI;AAC/B,UAAQ;AACR,oBAAkB;AAClB,SAAO,IAAI;AACX,eAAa;AACb,UAAQ;AACR,iBAAe,IAAI;AACnB;AAAA,IAAY,iBAAiB,IAAI,oBAAoB,IAAI,oBAAoB;AAAA,IACjE,MAAM,IAAI,oBAAoB,IAAI,oBAAoB;AAAA,IACtD,WAAW,IAAI,oBAAoB,IAAI;AACnD,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW;AACX,cAAY;AACd;AAEA,CAlBC,cAkBc,MAAM,KAAK,CAAC;AACzB,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,SAAO,IAAI;AACb;AAEA,CAvBC,cAuBc,CALY;AAMzB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,eAAa;AACb,cAAY,IAAI;AAClB;AAEA,CA9BC,eA8Be;AACd,cAAY,MAAM,IAAI,oBAAoB,IAAI;AAChD;AAGA,CAnCC,cAmCc,CAjBY,OAiBJ;AACrB,SAAO,IAAI;AACb;AAEA,CAvCC,cAuCc,KAAK,CArBO,QAqBE;AAC3B,SAAO,IAAI;AACb;AACA,CA1CC,cA0Cc,MAAM,KAAK,CAxBC,QAwBQ;AAChC,SAAO,IAAI;AACd;AAIA,CAAC,iBAAmB,CAxDnB;AAyDC,oBAAkB,IAAI;AACtB,cAAY,MAAM,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC5C;AAEA,CAAC,iBAAmB,CArDnB;AAsDC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAzDnB,cAyDkC,MAAM,KAAK,CAvCnB;AAwCzB,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA9DnB,cA8DkC,CA5CR;AA6CzB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAAC,iBAAmB,CApEnB,cAoEkC,CAlDR,OAkDgB;AACzC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAxEnB,cAwEkC,KAAK,CAtDb,QAsDsB;AAC/C,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CA5EnB,cA4EkC,MAAM,KAAK,CA1DnB,QA0D4B;AACpD,SAAO,IAAI;AACd;", "names": []}