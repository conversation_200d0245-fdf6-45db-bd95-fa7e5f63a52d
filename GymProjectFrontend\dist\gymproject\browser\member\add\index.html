<!DOCTYPE html><html lang="en"><head>
  <meta charset="utf-8">
  <title>Spor Salonu QR</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&amp;display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="styles.css"><link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&amp;display=swap" as="style"><link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style"><style ng-app-id="ng">

.app-initializing[_ngcontent-ng-c4172328733] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background-color);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
.initializing-spinner[_ngcontent-ng-c4172328733] {
  position: relative;
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.spinner-circle[_ngcontent-ng-c4172328733] {
  position: absolute;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid transparent;
  border-top-color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  animation: _ngcontent-ng-c4172328733_spin 1.5s linear infinite;
}
.dumbbell[_ngcontent-ng-c4172328733] {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: _ngcontent-ng-c4172328733_lift 2s ease-in-out infinite;
}
.weight[_ngcontent-ng-c4172328733] {
  background:
    linear-gradient(
      135deg,
      var(--primary-color) 0%,
      var(--secondary-color) 100%);
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(67, 97, 238, 0.5);
}
.inner-weight[_ngcontent-ng-c4172328733] {
  width: 50%;
  height: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}
.weight.left[_ngcontent-ng-c4172328733] {
  animation: _ngcontent-ng-c4172328733_pulse-left 2s ease-in-out infinite;
}
.weight.right[_ngcontent-ng-c4172328733] {
  animation: _ngcontent-ng-c4172328733_pulse-right 2s ease-in-out infinite;
}
.handle[_ngcontent-ng-c4172328733] {
  height: 8px;
  width: 50px;
  background:
    linear-gradient(
      90deg,
      var(--primary-color) 0%,
      var(--secondary-color) 100%);
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(67, 97, 238, 0.5);
}
@keyframes _ngcontent-ng-c4172328733_spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes _ngcontent-ng-c4172328733_lift {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}
@keyframes _ngcontent-ng-c4172328733_pulse-left {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
@keyframes _ngcontent-ng-c4172328733_pulse-right {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
.app-container[_ngcontent-ng-c4172328733] {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  opacity: 1;
  visibility: visible;
}
.app-container.initializing[_ngcontent-ng-c4172328733] {
  opacity: 0;
  visibility: hidden;
}
.main-content[_ngcontent-ng-c4172328733] {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}
.content-area[_ngcontent-ng-c4172328733] {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  transition: all 0.3s ease;
}
.mobile-header[_ngcontent-ng-c4172328733] {
  display: none;
  padding: 15px;
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px var(--shadow-color);
  z-index: 100;
}
.mobile-title[_ngcontent-ng-c4172328733] {
  font-size: 18px;
  font-weight: 600;
}
.sidebar-toggle[_ngcontent-ng-c4172328733], 
.theme-toggle[_ngcontent-ng-c4172328733] {
  background: none;
  border: none;
  color: var(--sidebar-text);
  font-size: 18px;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}
.sidebar-toggle[_ngcontent-ng-c4172328733]:hover, 
.theme-toggle[_ngcontent-ng-c4172328733]:hover {
  background-color: var(--sidebar-hover);
}
@media (max-width: 991.98px) {
  .mobile-header[_ngcontent-ng-c4172328733] {
    display: flex;
  }
  .app-container.sidebar-collapsed[_ngcontent-ng-c4172328733]   .main-content[_ngcontent-ng-c4172328733] {
    margin-left: 0;
  }
}
body.initializing[_ngcontent-ng-c4172328733] {
  overflow: hidden;
}
/*# sourceMappingURL=/app.component.css.map */</style><style ng-app-id="ng">

[_ngcontent-ng-c228792879]:root {
  --primary-color: #4361ee;
  --secondary-color: #3a0ca3;
  --accent-color: #f72585;
  --success-color: #4cc9f0;
  --warning-color: #f8961e;
  --danger-color: #f94144;
  --text-color: #2b2d42;
  --text-muted: #6c757d;
  --background-color: #f8f9fa;
  --card-bg-color: #ffffff;
  --input-bg: #f8f9fa;
  --input-border: #ced4da;
  --input-text: #495057;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --border-radius: 12px;
}
.login-container[_ngcontent-ng-c228792879] {
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--background-color);
  background-image:
    linear-gradient(
      135deg,
      #f5f7fa 0%,
      #e4e8f0 100%);
}
.login-wrapper[_ngcontent-ng-c228792879] {
  display: flex;
  width: 90%;
  max-width: 1200px;
  height: 650px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 20px 40px var(--shadow-color);
  position: relative;
}
.login-image-panel[_ngcontent-ng-c228792879] {
  flex: 1.2;
  background-image: url(https://images.unsplash.com/photo-1571902943202-507ec2618e8f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80);
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.overlay[_ngcontent-ng-c228792879] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.85) 0%,
      rgba(58, 12, 163, 0.85) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}
.gym-branding[_ngcontent-ng-c228792879] {
  text-align: center;
  color: white;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  max-width: 500px;
}
.logo-container[_ngcontent-ng-c228792879] {
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
}
.gym-branding[_ngcontent-ng-c228792879]   i[_ngcontent-ng-c228792879] {
  font-size: 50px;
  color: white;
}
.gym-branding[_ngcontent-ng-c228792879]   h1[_ngcontent-ng-c228792879] {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.gym-branding[_ngcontent-ng-c228792879]   p[_ngcontent-ng-c228792879] {
  font-size: 18px;
  opacity: 0.9;
  margin-bottom: 30px;
}
.features[_ngcontent-ng-c228792879] {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  max-width: 300px;
}
.feature[_ngcontent-ng-c228792879] {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px 20px;
  border-radius: 10px;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}
.feature[_ngcontent-ng-c228792879]:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}
.feature[_ngcontent-ng-c228792879]   i[_ngcontent-ng-c228792879] {
  font-size: 20px;
  color: white;
}
.feature[_ngcontent-ng-c228792879]   span[_ngcontent-ng-c228792879] {
  font-size: 16px;
  font-weight: 500;
}
.login-form-panel[_ngcontent-ng-c228792879] {
  flex: 0.8;
  background-color: var(--card-bg-color);
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
}
.login-form-panel[_ngcontent-ng-c228792879]::before {
  content: "";
  position: absolute;
  top: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  background: var(--primary-color);
  opacity: 0.1;
  border-radius: 50%;
}
.login-form-panel[_ngcontent-ng-c228792879]::after {
  content: "";
  position: absolute;
  bottom: -80px;
  left: -80px;
  width: 160px;
  height: 160px;
  background: var(--secondary-color);
  opacity: 0.1;
  border-radius: 50%;
}
.login-form-container[_ngcontent-ng-c228792879] {
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
  position: relative;
  z-index: 1;
}
.login-header[_ngcontent-ng-c228792879] {
  margin-bottom: 40px;
  text-align: center;
}
.header-icon[_ngcontent-ng-c228792879] {
  width: 70px;
  height: 70px;
  background:
    linear-gradient(
      135deg,
      var(--primary-color) 0%,
      var(--secondary-color) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);
}
.header-icon[_ngcontent-ng-c228792879]   i[_ngcontent-ng-c228792879] {
  font-size: 30px;
  color: white;
}
.login-header[_ngcontent-ng-c228792879]   h2[_ngcontent-ng-c228792879] {
  color: var(--text-color);
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 10px;
}
.login-header[_ngcontent-ng-c228792879]   p[_ngcontent-ng-c228792879] {
  color: var(--text-muted);
  font-size: 16px;
}
.login-form[_ngcontent-ng-c228792879] {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.form-group[_ngcontent-ng-c228792879] {
  margin-bottom: 5px;
}
.form-group[_ngcontent-ng-c228792879]   label[_ngcontent-ng-c228792879] {
  display: block;
  margin-bottom: 6px;
  font-size: 0.9rem;
  color: var(--text-color);
}
.input-group[_ngcontent-ng-c228792879] {
  position: relative;
  display: flex;
  align-items: center;
}
.input-group[_ngcontent-ng-c228792879]   i[_ngcontent-ng-c228792879] {
  position: absolute;
  left: 12px;
  color: var(--text-muted);
}
.input-group[_ngcontent-ng-c228792879]   input[_ngcontent-ng-c228792879], 
.input-group[_ngcontent-ng-c228792879]   select[_ngcontent-ng-c228792879] {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid var(--input-border);
  border-radius: 6px;
  background-color: var(--input-bg);
  color: var(--input-text);
  font-size: 1rem;
  transition: border-color 0.3s, box-shadow 0.3s;
}
.input-group[_ngcontent-ng-c228792879]   input[_ngcontent-ng-c228792879]:focus, 
.input-group[_ngcontent-ng-c228792879]   select[_ngcontent-ng-c228792879]:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
  outline: none;
}
.input-group[_ngcontent-ng-c228792879]   input.is-invalid[_ngcontent-ng-c228792879] {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 2px rgba(249, 65, 68, 0.2);
}
.input-group[_ngcontent-ng-c228792879]   .toggle-password[_ngcontent-ng-c228792879] {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
}
.input-group[_ngcontent-ng-c228792879]   .toggle-password[_ngcontent-ng-c228792879]:hover {
  color: var(--primary-color);
}
.error-message[_ngcontent-ng-c228792879] {
  margin-top: 5px;
  color: var(--danger-color);
  font-size: 0.8rem;
}
.form-actions[_ngcontent-ng-c228792879] {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
}
.forgot-password[_ngcontent-ng-c228792879] {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
}
.forgot-password[_ngcontent-ng-c228792879]:hover {
  text-decoration: underline;
}
.login-button[_ngcontent-ng-c228792879] {
  width: 100%;
  padding: 14px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.login-button[_ngcontent-ng-c228792879]:hover {
  background-color: var(--secondary-color);
}
.login-button[_ngcontent-ng-c228792879]:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
}
.login-footer[_ngcontent-ng-c228792879] {
  margin-top: 30px;
  text-align: center;
  color: var(--text-muted);
  font-size: 0.85rem;
}
.support[_ngcontent-ng-c228792879] {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 10px;
}
.support[_ngcontent-ng-c228792879]   a[_ngcontent-ng-c228792879] {
  color: var(--primary-color);
  text-decoration: none;
}
.support[_ngcontent-ng-c228792879]   a[_ngcontent-ng-c228792879]:hover {
  text-decoration: underline;
}
.login-footer[_ngcontent-ng-c228792879]   p[_ngcontent-ng-c228792879] {
  color: var(--text-muted);
  font-size: 0.85rem;
}
@media (max-width: 992px) {
  .login-wrapper[_ngcontent-ng-c228792879] {
    flex-direction: column;
  }
  .login-image-panel[_ngcontent-ng-c228792879] {
    display: none;
  }
  .login-form-panel[_ngcontent-ng-c228792879] {
    padding: 30px 20px;
  }
}
@media (max-width: 576px) {
  .login-form-container[_ngcontent-ng-c228792879] {
    padding: 0;
  }
  .login-header[_ngcontent-ng-c228792879]   h2[_ngcontent-ng-c228792879] {
    font-size: 1.5rem;
  }
}
[data-theme="dark"][_nghost-ng-c228792879]   .input-group[_ngcontent-ng-c228792879]   input[_ngcontent-ng-c228792879], [data-theme="dark"]   [_nghost-ng-c228792879]   .input-group[_ngcontent-ng-c228792879]   input[_ngcontent-ng-c228792879], 
[data-theme="dark"][_nghost-ng-c228792879]   .input-group[_ngcontent-ng-c228792879]   select[_ngcontent-ng-c228792879], [data-theme="dark"]   [_nghost-ng-c228792879]   .input-group[_ngcontent-ng-c228792879]   select[_ngcontent-ng-c228792879] {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--input-text);
}
[data-theme="dark"][_nghost-ng-c228792879]   .login-form-panel[_ngcontent-ng-c228792879], [data-theme="dark"]   [_nghost-ng-c228792879]   .login-form-panel[_ngcontent-ng-c228792879] {
  background-color: var(--card-bg-color);
}
[data-theme="dark"][_nghost-ng-c228792879]   .login-header[_ngcontent-ng-c228792879]   h2[_ngcontent-ng-c228792879], [data-theme="dark"]   [_nghost-ng-c228792879]   .login-header[_ngcontent-ng-c228792879]   h2[_ngcontent-ng-c228792879], 
[data-theme="dark"][_nghost-ng-c228792879]   .form-group[_ngcontent-ng-c228792879]   label[_ngcontent-ng-c228792879], [data-theme="dark"]   [_nghost-ng-c228792879]   .form-group[_ngcontent-ng-c228792879]   label[_ngcontent-ng-c228792879] {
  color: var(--text-color);
}
[data-theme="dark"][_nghost-ng-c228792879]   .login-footer[_ngcontent-ng-c228792879], [data-theme="dark"]   [_nghost-ng-c228792879]   .login-footer[_ngcontent-ng-c228792879], 
[data-theme="dark"][_nghost-ng-c228792879]   .login-header[_ngcontent-ng-c228792879]   p[_ngcontent-ng-c228792879], [data-theme="dark"]   [_nghost-ng-c228792879]   .login-header[_ngcontent-ng-c228792879]   p[_ngcontent-ng-c228792879] {
  color: var(--text-muted);
}
[data-theme="dark"][_nghost-ng-c228792879]   .input-group[_ngcontent-ng-c228792879]   i[_ngcontent-ng-c228792879], [data-theme="dark"]   [_nghost-ng-c228792879]   .input-group[_ngcontent-ng-c228792879]   i[_ngcontent-ng-c228792879], 
[data-theme="dark"][_nghost-ng-c228792879]   .input-group[_ngcontent-ng-c228792879]   .toggle-password[_ngcontent-ng-c228792879], [data-theme="dark"]   [_nghost-ng-c228792879]   .input-group[_ngcontent-ng-c228792879]   .toggle-password[_ngcontent-ng-c228792879] {
  color: var(--text-muted);
}
[data-theme="dark"][_nghost-ng-c228792879]   .login-container[_ngcontent-ng-c228792879], [data-theme="dark"]   [_nghost-ng-c228792879]   .login-container[_ngcontent-ng-c228792879] {
  background-image:
    linear-gradient(
      135deg,
      #121212 0%,
      #1a1a1a 100%);
}
/*# sourceMappingURL=/login.component.css.map */</style></head>
<body class="mat-typography">
  <app-root ng-version="19.2.5" _nghost-ng-c4172328733="" ng-server-context="ssg"><div _ngcontent-ng-c4172328733="" class="app-initializing"><div _ngcontent-ng-c4172328733="" class="initializing-spinner"><div _ngcontent-ng-c4172328733="" class="spinner-circle"></div><div _ngcontent-ng-c4172328733="" class="dumbbell"><div _ngcontent-ng-c4172328733="" class="weight left"><div _ngcontent-ng-c4172328733="" class="inner-weight"></div></div><div _ngcontent-ng-c4172328733="" class="handle"></div><div _ngcontent-ng-c4172328733="" class="weight right"><div _ngcontent-ng-c4172328733="" class="inner-weight"></div></div></div></div></div><!--bindings={
  "ng-reflect-ng-if": "true"
}--><div _ngcontent-ng-c4172328733="" class="app-container initializing" ng-reflect-ng-class="[object Object]"><!--bindings={
  "ng-reflect-ng-if": "false"
}--><div _ngcontent-ng-c4172328733="" class="main-content"><!--bindings={
  "ng-reflect-ng-if": "false"
}--><main _ngcontent-ng-c4172328733="" class="content-area"><router-outlet _ngcontent-ng-c4172328733=""></router-outlet><app-login _nghost-ng-c228792879=""><div _ngcontent-ng-c228792879="" class="login-container"><div _ngcontent-ng-c228792879="" class="login-wrapper"><div _ngcontent-ng-c228792879="" class="login-image-panel"><div _ngcontent-ng-c228792879="" class="overlay"><div _ngcontent-ng-c228792879="" class="gym-branding"><div _ngcontent-ng-c228792879="" class="logo-container"><i _ngcontent-ng-c228792879="" class="fas fa-dumbbell"></i></div><h1 _ngcontent-ng-c228792879="">GymKod Pro</h1><p _ngcontent-ng-c228792879="">Profesyonel Spor Salonu Yönetim Sistemi</p><div _ngcontent-ng-c228792879="" class="features"><div _ngcontent-ng-c228792879="" class="feature"><i _ngcontent-ng-c228792879="" class="fas fa-users"></i><span _ngcontent-ng-c228792879="">Üye Yönetimi</span></div><div _ngcontent-ng-c228792879="" class="feature"><i _ngcontent-ng-c228792879="" class="fas fa-calendar-check"></i><span _ngcontent-ng-c228792879="">Program Takibi</span></div><div _ngcontent-ng-c228792879="" class="feature"><i _ngcontent-ng-c228792879="" class="fas fa-chart-line"></i><span _ngcontent-ng-c228792879="">Performans Analizi</span></div></div></div></div></div><div _ngcontent-ng-c228792879="" class="login-form-panel"><div _ngcontent-ng-c228792879="" class="login-form-container"><div _ngcontent-ng-c228792879="" class="login-header"><div _ngcontent-ng-c228792879="" class="header-icon"><i _ngcontent-ng-c228792879="" class="fas fa-user-shield"></i></div><h2 _ngcontent-ng-c228792879="">Yönetim Paneli</h2><p _ngcontent-ng-c228792879="">Hesabınıza giriş yapın</p></div><form _ngcontent-ng-c228792879="" novalidate="" class="login-form ng-untouched ng-pristine ng-invalid" ng-reflect-form="[object Object]"><div _ngcontent-ng-c228792879="" class="form-group"><label _ngcontent-ng-c228792879="" for="email">E-posta</label><div _ngcontent-ng-c228792879="" class="input-group"><i _ngcontent-ng-c228792879="" class="fas fa-envelope"></i><input _ngcontent-ng-c228792879="" id="email" type="email" formcontrolname="email" placeholder="E-posta adresinizi girin" ng-reflect-name="email" ng-reflect-ng-class="[object Object]" class="ng-untouched ng-pristine ng-invalid" value=""></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div><div _ngcontent-ng-c228792879="" class="form-group"><label _ngcontent-ng-c228792879="" for="password">Şifre</label><div _ngcontent-ng-c228792879="" class="input-group"><i _ngcontent-ng-c228792879="" class="fas fa-lock"></i><input _ngcontent-ng-c228792879="" id="password" formcontrolname="password" placeholder="Şifrenizi girin" ng-reflect-name="password" type="password" ng-reflect-ng-class="[object Object]" class="ng-untouched ng-pristine ng-invalid" value=""><button _ngcontent-ng-c228792879="" type="button" class="toggle-password"><i _ngcontent-ng-c228792879="" class="fas fa-eye"></i></button></div><!--bindings={
  "ng-reflect-ng-if": "false"
}--></div><div _ngcontent-ng-c228792879="" class="form-actions"><a _ngcontent-ng-c228792879="" href="#" class="forgot-password">Şifremi unuttum</a></div><button _ngcontent-ng-c228792879="" type="submit" class="login-button" disabled=""><span _ngcontent-ng-c228792879="">Giriş Yap</span><!--bindings={
  "ng-reflect-ng-if": "true"
}--><!--bindings={
  "ng-reflect-ng-if": "false"
}--></button></form><div _ngcontent-ng-c228792879="" class="login-footer"><div _ngcontent-ng-c228792879="" class="support"><i _ngcontent-ng-c228792879="" class="fas fa-headset"></i><span _ngcontent-ng-c228792879="">Destek: <a _ngcontent-ng-c228792879="" href="mailto:<EMAIL>"><EMAIL></a></span></div><p _ngcontent-ng-c228792879="">© 2025 GymKod Pro. Tüm hakları saklıdır.</p></div></div></div></div></div></app-login><!--container--></main></div></div></app-root>
<script src="polyfills.js" type="module"></script><script src="scripts.js" defer=""></script><script src="main.js" type="module"></script>

</body></html>