{"version": 3, "sources": ["src/app/components/expense-dialog/expense-dialog.component.css"], "sourcesContent": ["/* Expense Dialog specific styles can be added here if needed */\r\n.expense-form {\r\n  padding-top: 10px; /* Add some padding to the top of the form */\r\n}\r\n\r\n/* Ensure spinner aligns well within the button */\r\n.mat-mdc-button-persistent-ripple {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.spinner-border-sm {\r\n    width: 1rem;\r\n    height: 1rem;\r\n    border-width: .2em;\r\n    margin-right: 0.5rem; /* Add space between spinner and text */\r\n}\r\n\r\n/* Dark mode styles moved to global styles.css */"], "mappings": ";AACA,CAAC;AACC,eAAa;AACf;AAGA,CAAC;AACG,WAAS;AACT,eAAa;AACjB;AAEA,CAAC;AACG,SAAO;AACP,UAAQ;AACR,gBAAc;AACd,gBAAc;AAClB;", "names": []}