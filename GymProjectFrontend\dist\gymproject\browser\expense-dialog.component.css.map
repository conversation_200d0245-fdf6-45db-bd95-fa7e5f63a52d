{"version": 3, "sources": ["src/app/components/expense-dialog/expense-dialog.component.css"], "sourcesContent": ["/* Expense Dialog specific styles can be added here if needed */\r\n.expense-form {\r\n  padding-top: 10px; /* Add some padding to the top of the form */\r\n}\r\n\r\n/* Ensure spinner aligns well within the button */\r\n.mat-mdc-button-persistent-ripple {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.spinner-border-sm {\r\n    width: 1rem;\r\n    height: 1rem;\r\n    border-width: .2em;\r\n    margin-right: 0.5rem; /* Add space between spinner and text */\r\n}\r\n\r\n/* Dark Mode Styles for Expense Dialog */\r\n[data-theme=\"dark\"] .mat-mdc-dialog-container {\r\n  background-color: var(--bg-primary) !important;\r\n  color: var(--text-primary) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-mdc-dialog-title {\r\n  color: var(--text-primary) !important;\r\n}\r\n\r\n/* Form Field Styles for Dark Mode */\r\n[data-theme=\"dark\"] .mat-mdc-form-field {\r\n  --mdc-filled-text-field-container-color: transparent !important;\r\n  --mdc-outlined-text-field-outline-color: var(--border-color) !important;\r\n  --mdc-outlined-text-field-hover-outline-color: var(--primary) !important;\r\n  --mdc-outlined-text-field-focus-outline-color: var(--primary) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-mdc-form-field .mdc-notched-outline__leading,\r\n[data-theme=\"dark\"] .mat-mdc-form-field .mdc-notched-outline__notch,\r\n[data-theme=\"dark\"] .mat-mdc-form-field .mdc-notched-outline__trailing {\r\n  border-color: var(--border-color) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-mdc-form-field:hover .mdc-notched-outline__leading,\r\n[data-theme=\"dark\"] .mat-mdc-form-field:hover .mdc-notched-outline__notch,\r\n[data-theme=\"dark\"] .mat-mdc-form-field:hover .mdc-notched-outline__trailing {\r\n  border-color: var(--primary) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-mdc-form-field .mdc-floating-label {\r\n  color: var(--text-secondary) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-mdc-form-field .mdc-floating-label--float-above {\r\n  color: var(--primary) !important;\r\n}\r\n\r\n/* Input and Textarea Styles */\r\n[data-theme=\"dark\"] .mat-mdc-input-element {\r\n  color: var(--text-primary) !important;\r\n  background-color: var(--bg-primary) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-mdc-text-field-wrapper {\r\n  background-color: var(--bg-primary) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-mdc-form-field-flex {\r\n  background-color: var(--bg-primary) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-mdc-input-element::placeholder {\r\n  color: var(--text-secondary) !important;\r\n  opacity: 0.7 !important;\r\n}\r\n\r\n/* Select Styles */\r\n[data-theme=\"dark\"] .mat-mdc-select-value {\r\n  color: var(--text-primary) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-mdc-select-arrow {\r\n  color: var(--text-secondary) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-mdc-select-trigger {\r\n  background-color: var(--bg-primary) !important;\r\n}\r\n\r\n/* Hint and Error Styles */\r\n[data-theme=\"dark\"] .mat-mdc-form-field .mat-mdc-form-field-hint {\r\n  color: var(--text-secondary) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-mdc-form-field .mat-mdc-form-field-error {\r\n  color: var(--danger) !important;\r\n}\r\n\r\n/* Button Styles */\r\n[data-theme=\"dark\"] .mat-mdc-outlined-button {\r\n  color: var(--text-primary) !important;\r\n  border-color: var(--border-color) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-mdc-outlined-button:hover {\r\n  background-color: var(--bg-secondary) !important;\r\n  border-color: var(--primary) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-mdc-raised-button.mat-primary {\r\n  background-color: var(--primary) !important;\r\n  color: white !important;\r\n}\r\n\r\n/* Datepicker Icon */\r\n[data-theme=\"dark\"] .mat-datepicker-toggle .mat-icon {\r\n  color: var(--text-secondary) !important;\r\n}\r\n\r\n[data-theme=\"dark\"] .mat-datepicker-toggle:hover .mat-icon {\r\n  color: var(--primary) !important;\r\n}\r\n\r\n/* Text Prefix (₺ symbol) */\r\n[data-theme=\"dark\"] .mat-mdc-form-field .mat-mdc-form-field-text-prefix {\r\n  color: var(--text-secondary) !important;\r\n}"], "mappings": ";AACA,CAAC;AACC,eAAa;AACf;AAGA,CAAC;AACG,WAAS;AACT,eAAa;AACjB;AAEA,CAAC;AACG,SAAO;AACP,UAAQ;AACR,gBAAc;AACd,gBAAc;AAClB;AAGA,CAAC,iBAAmB,CAAC;AACnB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAAC;AACnB,SAAO,IAAI;AACb;AAGA,CAAC,iBAAmB,CAAC;AACnB,2CAAyC;AACzC,2CAAyC,IAAI;AAC7C,iDAA+C,IAAI;AACnD,iDAA+C,IAAI;AACrD;AAEA,CAAC,iBAAmB,CAPC,mBAOmB,CAAC;AACzC,CAAC,iBAAmB,CARC,mBAQmB,CAAC;AACzC,CAAC,iBAAmB,CATC,mBASmB,CAAC;AACvC,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAbC,kBAakB,OAAO,CANL;AAOzC,CAAC,iBAAmB,CAdC,kBAckB,OAAO,CANL;AAOzC,CAAC,iBAAmB,CAfC,kBAekB,OAAO,CANL;AAOvC,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAnBC,mBAmBmB,CAAC;AACvC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAvBC,mBAuBmB,CAAC;AACvC,SAAO,IAAI;AACb;AAGA,CAAC,iBAAmB,CAAC;AACnB,SAAO,IAAI;AACX,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CAAC;AACnB,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CAAC;AACnB,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CAbC,qBAaqB;AACxC,SAAO,IAAI;AACX,WAAS;AACX;AAGA,CAAC,iBAAmB,CAAC;AACnB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAAC;AACnB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAAC;AACnB,oBAAkB,IAAI;AACxB;AAGA,CAAC,iBAAmB,CA5DC,mBA4DmB,CAAC;AACvC,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAhEC,mBAgEmB,CAAC;AACvC,SAAO,IAAI;AACb;AAGA,CAAC,iBAAmB,CAAC;AACnB,SAAO,IAAI;AACX,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CALC,uBAKuB;AAC1C,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAAC,qBAAqB,CAAC;AACzC,oBAAkB,IAAI;AACtB,SAAO;AACT;AAGA,CAAC,iBAAmB,CAAC,sBAAsB,CAAC;AAC1C,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAJC,qBAIqB,OAAO,CAJL;AAK1C,SAAO,IAAI;AACb;AAGA,CAAC,iBAAmB,CA9FC,mBA8FmB,CAAC;AACvC,SAAO,IAAI;AACb;", "names": []}