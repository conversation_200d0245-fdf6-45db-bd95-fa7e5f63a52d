{"version": 3, "sources": ["src/app/components/rate-limit-test/rate-limit-test.component.css"], "sourcesContent": [".rate-limit-test-container {\r\n  padding: 20px;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  background: var(--bg-color);\r\n  color: var(--text-color);\r\n  min-height: 100vh;\r\n}\r\n\r\n/* Header */\r\n.test-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: var(--primary-color);\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 15px;\r\n}\r\n\r\n.page-description {\r\n  font-size: 1.1rem;\r\n  color: var(--text-muted);\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* Cards */\r\n.test-config-card,\r\n.status-card,\r\n.results-card,\r\n.info-card {\r\n  background: var(--card-bg);\r\n  border-radius: 12px;\r\n  padding: 25px;\r\n  margin-bottom: 25px;\r\n  box-shadow: var(--shadow);\r\n  border: 1px solid var(--border-color);\r\n}\r\n\r\n.card-title,\r\n.status-title,\r\n.results-title,\r\n.info-title {\r\n  font-size: 1.4rem;\r\n  font-weight: 600;\r\n  color: var(--primary-color);\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n/* Configuration Grid */\r\n.config-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.config-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.config-item label {\r\n  font-weight: 600;\r\n  color: var(--text-color);\r\n  font-size: 0.95rem;\r\n}\r\n\r\n.form-select,\r\n.form-input {\r\n  padding: 12px 15px;\r\n  border: 2px solid var(--border-color);\r\n  border-radius: 8px;\r\n  background: var(--input-bg);\r\n  color: var(--text-color);\r\n  font-size: 1rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.form-select:focus,\r\n.form-input:focus {\r\n  outline: none;\r\n  border-color: var(--primary-color);\r\n  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);\r\n}\r\n\r\n.form-select:disabled,\r\n.form-input:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Test Controls */\r\n.test-controls {\r\n  display: flex;\r\n  gap: 15px;\r\n  margin-bottom: 25px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.btn {\r\n  padding: 12px 24px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  text-decoration: none;\r\n}\r\n\r\n.btn:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.btn-primary {\r\n  background: var(--primary-color);\r\n  color: white;\r\n}\r\n\r\n.btn-primary:hover:not(:disabled) {\r\n  background: var(--primary-hover);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.btn-danger {\r\n  background: #dc3545;\r\n  color: white;\r\n}\r\n\r\n.btn-danger:hover:not(:disabled) {\r\n  background: #c82333;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.btn-secondary {\r\n  background: #6c757d;\r\n  color: white;\r\n}\r\n\r\n.btn-secondary:hover:not(:disabled) {\r\n  background: #5a6268;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.btn-success {\r\n  background: #28a745;\r\n  color: white;\r\n}\r\n\r\n.btn-success:hover:not(:disabled) {\r\n  background: #218838;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* Status Grid */\r\n.status-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.status-label {\r\n  font-weight: 600;\r\n  color: var(--text-muted);\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.status-value {\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.status-value.success {\r\n  color: #28a745;\r\n}\r\n\r\n.status-value.rate-limited {\r\n  color: #ffc107;\r\n}\r\n\r\n.status-value.error {\r\n  color: #dc3545;\r\n}\r\n\r\n/* Progress Bar */\r\n.progress-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.progress-bar {\r\n  flex: 1;\r\n  height: 8px;\r\n  background: var(--border-color);\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: var(--primary-color);\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  font-weight: 600;\r\n  color: var(--text-color);\r\n  min-width: 80px;\r\n}\r\n\r\n/* Results Table */\r\n.results-table-container {\r\n  overflow-x: auto;\r\n  border-radius: 8px;\r\n  border: 1px solid var(--border-color);\r\n}\r\n\r\n.results-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  background: var(--card-bg);\r\n}\r\n\r\n.results-table th,\r\n.results-table td {\r\n  padding: 12px 15px;\r\n  text-align: left;\r\n  border-bottom: 1px solid var(--border-color);\r\n}\r\n\r\n.results-table th {\r\n  background: var(--primary-color);\r\n  color: white;\r\n  font-weight: 600;\r\n  position: sticky;\r\n  top: 0;\r\n}\r\n\r\n.results-table tr:hover {\r\n  background: var(--hover-bg);\r\n}\r\n\r\n.message-cell {\r\n  max-width: 200px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* Status Badges */\r\n.status-badge {\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.status-success .status-badge,\r\n.status-badge.status-success {\r\n  background: rgba(40, 167, 69, 0.1);\r\n  color: #28a745;\r\n}\r\n\r\n.status-rate-limited .status-badge,\r\n.status-badge.status-rate-limited {\r\n  background: rgba(255, 193, 7, 0.1);\r\n  color: #ffc107;\r\n}\r\n\r\n.status-error .status-badge,\r\n.status-badge.status-error {\r\n  background: rgba(220, 53, 69, 0.1);\r\n  color: #dc3545;\r\n}\r\n\r\n/* Info Content */\r\n.info-content h4 {\r\n  color: var(--primary-color);\r\n  margin: 20px 0 10px 0;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.info-content ul {\r\n  margin: 10px 0;\r\n  padding-left: 20px;\r\n}\r\n\r\n.info-content li {\r\n  margin: 8px 0;\r\n  line-height: 1.5;\r\n}\r\n\r\n.warning-box {\r\n  background: rgba(255, 193, 7, 0.1);\r\n  border: 1px solid #ffc107;\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-top: 20px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 10px;\r\n}\r\n\r\n.warning-box i {\r\n  color: #ffc107;\r\n  margin-top: 2px;\r\n}\r\n\r\n/* Responsive */\r\n@media (max-width: 768px) {\r\n  .rate-limit-test-container {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .page-title {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .config-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .test-controls {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .btn {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .status-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .progress-container {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .results-table {\r\n    font-size: 0.9rem;\r\n  }\r\n  \r\n  .results-table th,\r\n  .results-table td {\r\n    padding: 8px 10px;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,CAAC;AACC,WAAS;AACT,aAAW;AACX,UAAQ,EAAE;AACV,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,cAAY;AACd;AAGA,CAAC;AACC,cAAY;AACZ,iBAAe;AACjB;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACX,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,OAAK;AACP;AAEA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACX,aAAW;AACX,UAAQ,EAAE;AACV,eAAa;AACf;AAGA,CAAC;AACD,CAAC;AACD,CAAC;AACD,CAAC;AACC,cAAY,IAAI;AAChB,iBAAe;AACf,WAAS;AACT,iBAAe;AACf,cAAY,IAAI;AAChB,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACD,CAAC;AACD,CAAC;AACD,CAAC;AACC,aAAW;AACX,eAAa;AACb,SAAO,IAAI;AACX,iBAAe;AACf,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAGA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,QAAQ,EAAE,OAAO,KAAK,EAAE;AACtD,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CANC,YAMY;AACX,eAAa;AACb,SAAO,IAAI;AACX,aAAW;AACb;AAEA,CAAC;AACD,CAAC;AACC,WAAS,KAAK;AACd,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe;AACf,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,aAAW;AACX,cAAY,IAAI,KAAK;AACvB;AAEA,CAXC,WAWW;AACZ,CAXC,UAWU;AACT,WAAS;AACT,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AAC1C;AAEA,CAlBC,WAkBW;AACZ,CAlBC,UAkBU;AACT,WAAS;AACT,UAAQ;AACV;AAGA,CAAC;AACC,WAAS;AACT,OAAK;AACL,iBAAe;AACf,aAAW;AACb;AAEA,CAAC;AACC,WAAS,KAAK;AACd,UAAQ;AACR,iBAAe;AACf,eAAa;AACb,aAAW;AACX,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,WAAS;AACT,eAAa;AACb,OAAK;AACL,mBAAiB;AACnB;AAEA,CAdC,GAcG;AACF,WAAS;AACT,UAAQ;AACV;AAEA,CAAC;AACC,cAAY,IAAI;AAChB,SAAO;AACT;AAEA,CALC,WAKW,MAAM,KAAK;AACrB,cAAY,IAAI;AAChB,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,UAKU,MAAM,KAAK;AACpB,cAAY;AACZ,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,aAKa,MAAM,KAAK;AACvB,cAAY;AACZ,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,WAKW,MAAM,KAAK;AACrB,cAAY;AACZ,aAAW,WAAW;AACxB;AAGA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,QAAQ,EAAE,OAAO,KAAK,EAAE;AACtD,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACX,aAAW;AACb;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACf;AAEA,CALC,YAKY,CAAC;AACZ,SAAO;AACT;AAEA,CATC,YASY,CAAC;AACZ,SAAO;AACT;AAEA,CAbC,YAaY,CAAC;AACZ,SAAO;AACT;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,QAAM;AACN,UAAQ;AACR,cAAY,IAAI;AAChB,iBAAe;AACf,YAAU;AACZ;AAEA,CAAC;AACC,UAAQ;AACR,cAAY,IAAI;AAChB,cAAY,MAAM,KAAK;AACzB;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACX,aAAW;AACb;AAGA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,SAAO;AACP,mBAAiB;AACjB,cAAY,IAAI;AAClB;AAEA,CANC,cAMc;AACf,CAPC,cAOc;AACb,WAAS,KAAK;AACd,cAAY;AACZ,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CAbC,cAac;AACb,cAAY,IAAI;AAChB,SAAO;AACP,eAAa;AACb,YAAU;AACV,OAAK;AACP;AAEA,CArBC,cAqBc,EAAE;AACf,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,aAAW;AACX,YAAU;AACV,iBAAe;AACf,eAAa;AACf;AAGA,CAAC;AACC,WAAS,IAAI;AACb,iBAAe;AACf,aAAW;AACX,eAAa;AACb,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC,eAAe,CAVf;AAWD,CAXC,YAWY,CADZ;AAEC,cAAY,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;AAC9B,SAAO;AACT;AAEA,CAAC,oBAAoB,CAhBpB;AAiBD,CAjBC,YAiBY,CADZ;AAEC,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE;AAC9B,SAAO;AACT;AAEA,CAAC,aAAa,CAtBb;AAuBD,CAvBC,YAuBY,CADZ;AAEC,cAAY,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC9B,SAAO;AACT;AAGA,CAAC,aAAa;AACZ,SAAO,IAAI;AACX,UAAQ,KAAK,EAAE,KAAK;AACpB,aAAW;AACb;AAEA,CANC,aAMa;AACZ,UAAQ,KAAK;AACb,gBAAc;AAChB;AAEA,CAXC,aAWa;AACZ,UAAQ,IAAI;AACZ,eAAa;AACf;AAEA,CAAC;AACC,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE;AAC9B,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,WAAS;AACT,cAAY;AACZ,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAXC,YAWY;AACX,SAAO;AACP,cAAY;AACd;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAhVD;AAiVG,aAAS;AACX;AAEA,GArUD;AAsUG,eAAW;AACb;AAEA,GA3RD;AA4RG,2BAAuB;AACzB;AAEA,GApPD;AAqPG,oBAAgB;AAClB;AAEA,GAjPD;AAkPG,qBAAiB;AACnB;AAEA,GAzLD;AA0LG,2BAAuB;AACzB;AAEA,GAzJD;AA0JG,oBAAgB;AAChB,iBAAa;AACb,SAAK;AACP;AAEA,GA9HD;AA+HG,eAAW;AACb;AAEA,GAlID,cAkIgB;AAAA,EACf,CAnID,cAmIgB;AACb,aAAS,IAAI;AACf;AACF;", "names": []}