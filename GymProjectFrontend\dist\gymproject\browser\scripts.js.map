{"version": 3, "sources": ["angular:script/global:scripts.js"], "sourcesContent": ["/*! jQuery v3.7.1 | (c) OpenJS Foundation and other contributors | jquery.org/license */\n!function(e,t){\"use strict\";\"object\"==typeof module&&\"object\"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error(\"jQuery requires a window with a document\");return t(e)}:t(e)}(\"undefined\"!=typeof window?window:this,function(ie,e){\"use strict\";var oe=[],r=Object.getPrototypeOf,ae=oe.slice,g=oe.flat?function(e){return oe.flat.call(e)}:function(e){return oe.concat.apply([],e)},s=oe.push,se=oe.indexOf,n={},i=n.toString,ue=n.hasOwnProperty,o=ue.toString,a=o.call(Object),le={},v=function(e){return\"function\"==typeof e&&\"number\"!=typeof e.nodeType&&\"function\"!=typeof e.item},y=function(e){return null!=e&&e===e.window},C=ie.document,u={type:!0,src:!0,nonce:!0,noModule:!0};function m(e,t,n){var r,i,o=(n=n||C).createElement(\"script\");if(o.text=e,t)for(r in u)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function x(e){return null==e?e+\"\":\"object\"==typeof e||\"function\"==typeof e?n[i.call(e)]||\"object\":typeof e}var t=\"3.7.1\",l=/HTML$/i,ce=function(e,t){return new ce.fn.init(e,t)};function c(e){var t=!!e&&\"length\"in e&&e.length,n=x(e);return!v(e)&&!y(e)&&(\"array\"===n||0===t||\"number\"==typeof t&&0<t&&t-1 in e)}function fe(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}ce.fn=ce.prototype={jquery:t,constructor:ce,length:0,toArray:function(){return ae.call(this)},get:function(e){return null==e?ae.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=ce.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return ce.each(this,e)},map:function(n){return this.pushStack(ce.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(ae.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(ce.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(ce.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(0<=n&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:s,sort:oe.sort,splice:oe.splice},ce.extend=ce.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,u=arguments.length,l=!1;for(\"boolean\"==typeof a&&(l=a,a=arguments[s]||{},s++),\"object\"==typeof a||v(a)||(a={}),s===u&&(a=this,s--);s<u;s++)if(null!=(e=arguments[s]))for(t in e)r=e[t],\"__proto__\"!==t&&a!==r&&(l&&r&&(ce.isPlainObject(r)||(i=Array.isArray(r)))?(n=a[t],o=i&&!Array.isArray(n)?[]:i||ce.isPlainObject(n)?n:{},i=!1,a[t]=ce.extend(l,o,r)):void 0!==r&&(a[t]=r));return a},ce.extend({expando:\"jQuery\"+(t+Math.random()).replace(/\\D/g,\"\"),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||\"[object Object]\"!==i.call(e))&&(!(t=r(e))||\"function\"==typeof(n=ue.call(t,\"constructor\")&&t.constructor)&&o.call(n)===a)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){m(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(c(e)){for(n=e.length;r<n;r++)if(!1===t.call(e[r],r,e[r]))break}else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},text:function(e){var t,n=\"\",r=0,i=e.nodeType;if(!i)while(t=e[r++])n+=ce.text(t);return 1===i||11===i?e.textContent:9===i?e.documentElement.textContent:3===i||4===i?e.nodeValue:n},makeArray:function(e,t){var n=t||[];return null!=e&&(c(Object(e))?ce.merge(n,\"string\"==typeof e?[e]:e):s.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:se.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!l.test(t||n&&n.nodeName||\"HTML\")},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!==a&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,a=[];if(c(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&a.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&a.push(i);return g(a)},guid:1,support:le}),\"function\"==typeof Symbol&&(ce.fn[Symbol.iterator]=oe[Symbol.iterator]),ce.each(\"Boolean Number String Function Array Date RegExp Object Error Symbol\".split(\" \"),function(e,t){n[\"[object \"+t+\"]\"]=t.toLowerCase()});var pe=oe.pop,de=oe.sort,he=oe.splice,ge=\"[\\\\x20\\\\t\\\\r\\\\n\\\\f]\",ve=new RegExp(\"^\"+ge+\"+|((?:^|[^\\\\\\\\])(?:\\\\\\\\.)*)\"+ge+\"+$\",\"g\");ce.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var f=/([\\0-\\x1f\\x7f]|^-?\\d)|^-$|[^\\x80-\\uFFFF\\w-]/g;function p(e,t){return t?\"\\0\"===e?\"\\ufffd\":e.slice(0,-1)+\"\\\\\"+e.charCodeAt(e.length-1).toString(16)+\" \":\"\\\\\"+e}ce.escapeSelector=function(e){return(e+\"\").replace(f,p)};var ye=C,me=s;!function(){var e,b,w,o,a,T,r,C,d,i,k=me,S=ce.expando,E=0,n=0,s=W(),c=W(),u=W(),h=W(),l=function(e,t){return e===t&&(a=!0),0},f=\"checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped\",t=\"(?:\\\\\\\\[\\\\da-fA-F]{1,6}\"+ge+\"?|\\\\\\\\[^\\\\r\\\\n\\\\f]|[\\\\w-]|[^\\0-\\\\x7f])+\",p=\"\\\\[\"+ge+\"*(\"+t+\")(?:\"+ge+\"*([*^$|!~]?=)\"+ge+\"*(?:'((?:\\\\\\\\.|[^\\\\\\\\'])*)'|\\\"((?:\\\\\\\\.|[^\\\\\\\\\\\"])*)\\\"|(\"+t+\"))|)\"+ge+\"*\\\\]\",g=\":(\"+t+\")(?:\\\\((('((?:\\\\\\\\.|[^\\\\\\\\'])*)'|\\\"((?:\\\\\\\\.|[^\\\\\\\\\\\"])*)\\\")|((?:\\\\\\\\.|[^\\\\\\\\()[\\\\]]|\"+p+\")*)|.*)\\\\)|)\",v=new RegExp(ge+\"+\",\"g\"),y=new RegExp(\"^\"+ge+\"*,\"+ge+\"*\"),m=new RegExp(\"^\"+ge+\"*([>+~]|\"+ge+\")\"+ge+\"*\"),x=new RegExp(ge+\"|>\"),j=new RegExp(g),A=new RegExp(\"^\"+t+\"$\"),D={ID:new RegExp(\"^#(\"+t+\")\"),CLASS:new RegExp(\"^\\\\.(\"+t+\")\"),TAG:new RegExp(\"^(\"+t+\"|[*])\"),ATTR:new RegExp(\"^\"+p),PSEUDO:new RegExp(\"^\"+g),CHILD:new RegExp(\"^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\\\(\"+ge+\"*(even|odd|(([+-]|)(\\\\d*)n|)\"+ge+\"*(?:([+-]|)\"+ge+\"*(\\\\d+)|))\"+ge+\"*\\\\)|)\",\"i\"),bool:new RegExp(\"^(?:\"+f+\")$\",\"i\"),needsContext:new RegExp(\"^\"+ge+\"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\\\(\"+ge+\"*((?:-\\\\d)?\\\\d*)\"+ge+\"*\\\\)|)(?=[^-]|$)\",\"i\")},N=/^(?:input|select|textarea|button)$/i,q=/^h\\d$/i,L=/^(?:#([\\w-]+)|(\\w+)|\\.([\\w-]+))$/,H=/[+~]/,O=new RegExp(\"\\\\\\\\[\\\\da-fA-F]{1,6}\"+ge+\"?|\\\\\\\\([^\\\\r\\\\n\\\\f])\",\"g\"),P=function(e,t){var n=\"0x\"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},M=function(){V()},R=J(function(e){return!0===e.disabled&&fe(e,\"fieldset\")},{dir:\"parentNode\",next:\"legend\"});try{k.apply(oe=ae.call(ye.childNodes),ye.childNodes),oe[ye.childNodes.length].nodeType}catch(e){k={apply:function(e,t){me.apply(e,ae.call(t))},call:function(e){me.apply(e,ae.call(arguments,1))}}}function I(t,e,n,r){var i,o,a,s,u,l,c,f=e&&e.ownerDocument,p=e?e.nodeType:9;if(n=n||[],\"string\"!=typeof t||!t||1!==p&&9!==p&&11!==p)return n;if(!r&&(V(e),e=e||T,C)){if(11!==p&&(u=L.exec(t)))if(i=u[1]){if(9===p){if(!(a=e.getElementById(i)))return n;if(a.id===i)return k.call(n,a),n}else if(f&&(a=f.getElementById(i))&&I.contains(e,a)&&a.id===i)return k.call(n,a),n}else{if(u[2])return k.apply(n,e.getElementsByTagName(t)),n;if((i=u[3])&&e.getElementsByClassName)return k.apply(n,e.getElementsByClassName(i)),n}if(!(h[t+\" \"]||d&&d.test(t))){if(c=t,f=e,1===p&&(x.test(t)||m.test(t))){(f=H.test(t)&&U(e.parentNode)||e)==e&&le.scope||((s=e.getAttribute(\"id\"))?s=ce.escapeSelector(s):e.setAttribute(\"id\",s=S)),o=(l=Y(t)).length;while(o--)l[o]=(s?\"#\"+s:\":scope\")+\" \"+Q(l[o]);c=l.join(\",\")}try{return k.apply(n,f.querySelectorAll(c)),n}catch(e){h(t,!0)}finally{s===S&&e.removeAttribute(\"id\")}}}return re(t.replace(ve,\"$1\"),e,n,r)}function W(){var r=[];return function e(t,n){return r.push(t+\" \")>b.cacheLength&&delete e[r.shift()],e[t+\" \"]=n}}function F(e){return e[S]=!0,e}function $(e){var t=T.createElement(\"fieldset\");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function B(t){return function(e){return fe(e,\"input\")&&e.type===t}}function _(t){return function(e){return(fe(e,\"input\")||fe(e,\"button\"))&&e.type===t}}function z(t){return function(e){return\"form\"in e?e.parentNode&&!1===e.disabled?\"label\"in e?\"label\"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&R(e)===t:e.disabled===t:\"label\"in e&&e.disabled===t}}function X(a){return F(function(o){return o=+o,F(function(e,t){var n,r=a([],e.length,o),i=r.length;while(i--)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}function U(e){return e&&\"undefined\"!=typeof e.getElementsByTagName&&e}function V(e){var t,n=e?e.ownerDocument||e:ye;return n!=T&&9===n.nodeType&&n.documentElement&&(r=(T=n).documentElement,C=!ce.isXMLDoc(T),i=r.matches||r.webkitMatchesSelector||r.msMatchesSelector,r.msMatchesSelector&&ye!=T&&(t=T.defaultView)&&t.top!==t&&t.addEventListener(\"unload\",M),le.getById=$(function(e){return r.appendChild(e).id=ce.expando,!T.getElementsByName||!T.getElementsByName(ce.expando).length}),le.disconnectedMatch=$(function(e){return i.call(e,\"*\")}),le.scope=$(function(){return T.querySelectorAll(\":scope\")}),le.cssHas=$(function(){try{return T.querySelector(\":has(*,:jqfake)\"),!1}catch(e){return!0}}),le.getById?(b.filter.ID=function(e){var t=e.replace(O,P);return function(e){return e.getAttribute(\"id\")===t}},b.find.ID=function(e,t){if(\"undefined\"!=typeof t.getElementById&&C){var n=t.getElementById(e);return n?[n]:[]}}):(b.filter.ID=function(e){var n=e.replace(O,P);return function(e){var t=\"undefined\"!=typeof e.getAttributeNode&&e.getAttributeNode(\"id\");return t&&t.value===n}},b.find.ID=function(e,t){if(\"undefined\"!=typeof t.getElementById&&C){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode(\"id\"))&&n.value===e)return[o];i=t.getElementsByName(e),r=0;while(o=i[r++])if((n=o.getAttributeNode(\"id\"))&&n.value===e)return[o]}return[]}}),b.find.TAG=function(e,t){return\"undefined\"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},b.find.CLASS=function(e,t){if(\"undefined\"!=typeof t.getElementsByClassName&&C)return t.getElementsByClassName(e)},d=[],$(function(e){var t;r.appendChild(e).innerHTML=\"<a id='\"+S+\"' href='' disabled='disabled'></a><select id='\"+S+\"-\\r\\\\' disabled='disabled'><option selected=''></option></select>\",e.querySelectorAll(\"[selected]\").length||d.push(\"\\\\[\"+ge+\"*(?:value|\"+f+\")\"),e.querySelectorAll(\"[id~=\"+S+\"-]\").length||d.push(\"~=\"),e.querySelectorAll(\"a#\"+S+\"+*\").length||d.push(\".#.+[+~]\"),e.querySelectorAll(\":checked\").length||d.push(\":checked\"),(t=T.createElement(\"input\")).setAttribute(\"type\",\"hidden\"),e.appendChild(t).setAttribute(\"name\",\"D\"),r.appendChild(e).disabled=!0,2!==e.querySelectorAll(\":disabled\").length&&d.push(\":enabled\",\":disabled\"),(t=T.createElement(\"input\")).setAttribute(\"name\",\"\"),e.appendChild(t),e.querySelectorAll(\"[name='']\").length||d.push(\"\\\\[\"+ge+\"*name\"+ge+\"*=\"+ge+\"*(?:''|\\\"\\\")\")}),le.cssHas||d.push(\":has\"),d=d.length&&new RegExp(d.join(\"|\")),l=function(e,t){if(e===t)return a=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!le.sortDetached&&t.compareDocumentPosition(e)===n?e===T||e.ownerDocument==ye&&I.contains(ye,e)?-1:t===T||t.ownerDocument==ye&&I.contains(ye,t)?1:o?se.call(o,e)-se.call(o,t):0:4&n?-1:1)}),T}for(e in I.matches=function(e,t){return I(e,null,null,t)},I.matchesSelector=function(e,t){if(V(e),C&&!h[t+\" \"]&&(!d||!d.test(t)))try{var n=i.call(e,t);if(n||le.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){h(t,!0)}return 0<I(t,T,null,[e]).length},I.contains=function(e,t){return(e.ownerDocument||e)!=T&&V(e),ce.contains(e,t)},I.attr=function(e,t){(e.ownerDocument||e)!=T&&V(e);var n=b.attrHandle[t.toLowerCase()],r=n&&ue.call(b.attrHandle,t.toLowerCase())?n(e,t,!C):void 0;return void 0!==r?r:e.getAttribute(t)},I.error=function(e){throw new Error(\"Syntax error, unrecognized expression: \"+e)},ce.uniqueSort=function(e){var t,n=[],r=0,i=0;if(a=!le.sortStable,o=!le.sortStable&&ae.call(e,0),de.call(e,l),a){while(t=e[i++])t===e[i]&&(r=n.push(i));while(r--)he.call(e,n[r],1)}return o=null,e},ce.fn.uniqueSort=function(){return this.pushStack(ce.uniqueSort(ae.apply(this)))},(b=ce.expr={cacheLength:50,createPseudo:F,match:D,attrHandle:{},find:{},relative:{\">\":{dir:\"parentNode\",first:!0},\" \":{dir:\"parentNode\"},\"+\":{dir:\"previousSibling\",first:!0},\"~\":{dir:\"previousSibling\"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(O,P),e[3]=(e[3]||e[4]||e[5]||\"\").replace(O,P),\"~=\"===e[2]&&(e[3]=\" \"+e[3]+\" \"),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),\"nth\"===e[1].slice(0,3)?(e[3]||I.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*(\"even\"===e[3]||\"odd\"===e[3])),e[5]=+(e[7]+e[8]||\"odd\"===e[3])):e[3]&&I.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return D.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||\"\":n&&j.test(n)&&(t=Y(n,!0))&&(t=n.indexOf(\")\",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(O,P).toLowerCase();return\"*\"===e?function(){return!0}:function(e){return fe(e,t)}},CLASS:function(e){var t=s[e+\" \"];return t||(t=new RegExp(\"(^|\"+ge+\")\"+e+\"(\"+ge+\"|$)\"))&&s(e,function(e){return t.test(\"string\"==typeof e.className&&e.className||\"undefined\"!=typeof e.getAttribute&&e.getAttribute(\"class\")||\"\")})},ATTR:function(n,r,i){return function(e){var t=I.attr(e,n);return null==t?\"!=\"===r:!r||(t+=\"\",\"=\"===r?t===i:\"!=\"===r?t!==i:\"^=\"===r?i&&0===t.indexOf(i):\"*=\"===r?i&&-1<t.indexOf(i):\"$=\"===r?i&&t.slice(-i.length)===i:\"~=\"===r?-1<(\" \"+t.replace(v,\" \")+\" \").indexOf(i):\"|=\"===r&&(t===i||t.slice(0,i.length+1)===i+\"-\"))}},CHILD:function(d,e,t,h,g){var v=\"nth\"!==d.slice(0,3),y=\"last\"!==d.slice(-4),m=\"of-type\"===e;return 1===h&&0===g?function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,a,s,u=v!==y?\"nextSibling\":\"previousSibling\",l=e.parentNode,c=m&&e.nodeName.toLowerCase(),f=!n&&!m,p=!1;if(l){if(v){while(u){o=e;while(o=o[u])if(m?fe(o,c):1===o.nodeType)return!1;s=u=\"only\"===d&&!s&&\"nextSibling\"}return!0}if(s=[y?l.firstChild:l.lastChild],y&&f){p=(a=(r=(i=l[S]||(l[S]={}))[d]||[])[0]===E&&r[1])&&r[2],o=a&&l.childNodes[a];while(o=++a&&o&&o[u]||(p=a=0)||s.pop())if(1===o.nodeType&&++p&&o===e){i[d]=[E,a,p];break}}else if(f&&(p=a=(r=(i=e[S]||(e[S]={}))[d]||[])[0]===E&&r[1]),!1===p)while(o=++a&&o&&o[u]||(p=a=0)||s.pop())if((m?fe(o,c):1===o.nodeType)&&++p&&(f&&((i=o[S]||(o[S]={}))[d]=[E,p]),o===e))break;return(p-=g)===h||p%h==0&&0<=p/h}}},PSEUDO:function(e,o){var t,a=b.pseudos[e]||b.setFilters[e.toLowerCase()]||I.error(\"unsupported pseudo: \"+e);return a[S]?a(o):1<a.length?(t=[e,e,\"\",o],b.setFilters.hasOwnProperty(e.toLowerCase())?F(function(e,t){var n,r=a(e,o),i=r.length;while(i--)e[n=se.call(e,r[i])]=!(t[n]=r[i])}):function(e){return a(e,0,t)}):a}},pseudos:{not:F(function(e){var r=[],i=[],s=ne(e.replace(ve,\"$1\"));return s[S]?F(function(e,t,n,r){var i,o=s(e,null,r,[]),a=e.length;while(a--)(i=o[a])&&(e[a]=!(t[a]=i))}):function(e,t,n){return r[0]=e,s(r,null,n,i),r[0]=null,!i.pop()}}),has:F(function(t){return function(e){return 0<I(t,e).length}}),contains:F(function(t){return t=t.replace(O,P),function(e){return-1<(e.textContent||ce.text(e)).indexOf(t)}}),lang:F(function(n){return A.test(n||\"\")||I.error(\"unsupported lang: \"+n),n=n.replace(O,P).toLowerCase(),function(e){var t;do{if(t=C?e.lang:e.getAttribute(\"xml:lang\")||e.getAttribute(\"lang\"))return(t=t.toLowerCase())===n||0===t.indexOf(n+\"-\")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=ie.location&&ie.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===r},focus:function(e){return e===function(){try{return T.activeElement}catch(e){}}()&&T.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:z(!1),disabled:z(!0),checked:function(e){return fe(e,\"input\")&&!!e.checked||fe(e,\"option\")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!b.pseudos.empty(e)},header:function(e){return q.test(e.nodeName)},input:function(e){return N.test(e.nodeName)},button:function(e){return fe(e,\"input\")&&\"button\"===e.type||fe(e,\"button\")},text:function(e){var t;return fe(e,\"input\")&&\"text\"===e.type&&(null==(t=e.getAttribute(\"type\"))||\"text\"===t.toLowerCase())},first:X(function(){return[0]}),last:X(function(e,t){return[t-1]}),eq:X(function(e,t,n){return[n<0?n+t:n]}),even:X(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:X(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:X(function(e,t,n){var r;for(r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:X(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=b.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})b.pseudos[e]=B(e);for(e in{submit:!0,reset:!0})b.pseudos[e]=_(e);function G(){}function Y(e,t){var n,r,i,o,a,s,u,l=c[e+\" \"];if(l)return t?0:l.slice(0);a=e,s=[],u=b.preFilter;while(a){for(o in n&&!(r=y.exec(a))||(r&&(a=a.slice(r[0].length)||a),s.push(i=[])),n=!1,(r=m.exec(a))&&(n=r.shift(),i.push({value:n,type:r[0].replace(ve,\" \")}),a=a.slice(n.length)),b.filter)!(r=D[o].exec(a))||u[o]&&!(r=u[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?I.error(e):c(e,s).slice(0)}function Q(e){for(var t=0,n=e.length,r=\"\";t<n;t++)r+=e[t].value;return r}function J(a,e,t){var s=e.dir,u=e.next,l=u||s,c=t&&\"parentNode\"===l,f=n++;return e.first?function(e,t,n){while(e=e[s])if(1===e.nodeType||c)return a(e,t,n);return!1}:function(e,t,n){var r,i,o=[E,f];if(n){while(e=e[s])if((1===e.nodeType||c)&&a(e,t,n))return!0}else while(e=e[s])if(1===e.nodeType||c)if(i=e[S]||(e[S]={}),u&&fe(e,u))e=e[s]||e;else{if((r=i[l])&&r[0]===E&&r[1]===f)return o[2]=r[2];if((i[l]=o)[2]=a(e,t,n))return!0}return!1}}function K(i){return 1<i.length?function(e,t,n){var r=i.length;while(r--)if(!i[r](e,t,n))return!1;return!0}:i[0]}function Z(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),l&&t.push(s)));return a}function ee(d,h,g,v,y,e){return v&&!v[S]&&(v=ee(v)),y&&!y[S]&&(y=ee(y,e)),F(function(e,t,n,r){var i,o,a,s,u=[],l=[],c=t.length,f=e||function(e,t,n){for(var r=0,i=t.length;r<i;r++)I(e,t[r],n);return n}(h||\"*\",n.nodeType?[n]:n,[]),p=!d||!e&&h?f:Z(f,u,d,n,r);if(g?g(p,s=y||(e?d:c||v)?[]:t,n,r):s=p,v){i=Z(s,l),v(i,[],n,r),o=i.length;while(o--)(a=i[o])&&(s[l[o]]=!(p[l[o]]=a))}if(e){if(y||d){if(y){i=[],o=s.length;while(o--)(a=s[o])&&i.push(p[o]=a);y(null,s=[],i,r)}o=s.length;while(o--)(a=s[o])&&-1<(i=y?se.call(e,a):u[o])&&(e[i]=!(t[i]=a))}}else s=Z(s===t?s.splice(c,s.length):s),y?y(null,t,s,r):k.apply(t,s)})}function te(e){for(var i,t,n,r=e.length,o=b.relative[e[0].type],a=o||b.relative[\" \"],s=o?1:0,u=J(function(e){return e===i},a,!0),l=J(function(e){return-1<se.call(i,e)},a,!0),c=[function(e,t,n){var r=!o&&(n||t!=w)||((i=t).nodeType?u(e,t,n):l(e,t,n));return i=null,r}];s<r;s++)if(t=b.relative[e[s].type])c=[J(K(c),t)];else{if((t=b.filter[e[s].type].apply(null,e[s].matches))[S]){for(n=++s;n<r;n++)if(b.relative[e[n].type])break;return ee(1<s&&K(c),1<s&&Q(e.slice(0,s-1).concat({value:\" \"===e[s-2].type?\"*\":\"\"})).replace(ve,\"$1\"),t,s<n&&te(e.slice(s,n)),n<r&&te(e=e.slice(n)),n<r&&Q(e))}c.push(t)}return K(c)}function ne(e,t){var n,v,y,m,x,r,i=[],o=[],a=u[e+\" \"];if(!a){t||(t=Y(e)),n=t.length;while(n--)(a=te(t[n]))[S]?i.push(a):o.push(a);(a=u(e,(v=o,m=0<(y=i).length,x=0<v.length,r=function(e,t,n,r,i){var o,a,s,u=0,l=\"0\",c=e&&[],f=[],p=w,d=e||x&&b.find.TAG(\"*\",i),h=E+=null==p?1:Math.random()||.1,g=d.length;for(i&&(w=t==T||t||i);l!==g&&null!=(o=d[l]);l++){if(x&&o){a=0,t||o.ownerDocument==T||(V(o),n=!C);while(s=v[a++])if(s(o,t||T,n)){k.call(r,o);break}i&&(E=h)}m&&((o=!s&&o)&&u--,e&&c.push(o))}if(u+=l,m&&l!==u){a=0;while(s=y[a++])s(c,f,t,n);if(e){if(0<u)while(l--)c[l]||f[l]||(f[l]=pe.call(r));f=Z(f)}k.apply(r,f),i&&!e&&0<f.length&&1<u+y.length&&ce.uniqueSort(r)}return i&&(E=h,w=p),c},m?F(r):r))).selector=e}return a}function re(e,t,n,r){var i,o,a,s,u,l=\"function\"==typeof e&&e,c=!r&&Y(e=l.selector||e);if(n=n||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&\"ID\"===(a=o[0]).type&&9===t.nodeType&&C&&b.relative[o[1].type]){if(!(t=(b.find.ID(a.matches[0].replace(O,P),t)||[])[0]))return n;l&&(t=t.parentNode),e=e.slice(o.shift().value.length)}i=D.needsContext.test(e)?0:o.length;while(i--){if(a=o[i],b.relative[s=a.type])break;if((u=b.find[s])&&(r=u(a.matches[0].replace(O,P),H.test(o[0].type)&&U(t.parentNode)||t))){if(o.splice(i,1),!(e=r.length&&Q(o)))return k.apply(n,r),n;break}}}return(l||ne(e,c))(r,t,!C,n,!t||H.test(e)&&U(t.parentNode)||t),n}G.prototype=b.filters=b.pseudos,b.setFilters=new G,le.sortStable=S.split(\"\").sort(l).join(\"\")===S,V(),le.sortDetached=$(function(e){return 1&e.compareDocumentPosition(T.createElement(\"fieldset\"))}),ce.find=I,ce.expr[\":\"]=ce.expr.pseudos,ce.unique=ce.uniqueSort,I.compile=ne,I.select=re,I.setDocument=V,I.tokenize=Y,I.escape=ce.escapeSelector,I.getText=ce.text,I.isXML=ce.isXMLDoc,I.selectors=ce.expr,I.support=ce.support,I.uniqueSort=ce.uniqueSort}();var d=function(e,t,n){var r=[],i=void 0!==n;while((e=e[t])&&9!==e.nodeType)if(1===e.nodeType){if(i&&ce(e).is(n))break;r.push(e)}return r},h=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},b=ce.expr.match.needsContext,w=/^<([a-z][^\\/\\0>:\\x20\\t\\r\\n\\f]*)[\\x20\\t\\r\\n\\f]*\\/?>(?:<\\/\\1>|)$/i;function T(e,n,r){return v(n)?ce.grep(e,function(e,t){return!!n.call(e,t,e)!==r}):n.nodeType?ce.grep(e,function(e){return e===n!==r}):\"string\"!=typeof n?ce.grep(e,function(e){return-1<se.call(n,e)!==r}):ce.filter(n,e,r)}ce.filter=function(e,t,n){var r=t[0];return n&&(e=\":not(\"+e+\")\"),1===t.length&&1===r.nodeType?ce.find.matchesSelector(r,e)?[r]:[]:ce.find.matches(e,ce.grep(t,function(e){return 1===e.nodeType}))},ce.fn.extend({find:function(e){var t,n,r=this.length,i=this;if(\"string\"!=typeof e)return this.pushStack(ce(e).filter(function(){for(t=0;t<r;t++)if(ce.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)ce.find(e,i[t],n);return 1<r?ce.uniqueSort(n):n},filter:function(e){return this.pushStack(T(this,e||[],!1))},not:function(e){return this.pushStack(T(this,e||[],!0))},is:function(e){return!!T(this,\"string\"==typeof e&&b.test(e)?ce(e):e||[],!1).length}});var k,S=/^(?:\\s*(<[\\w\\W]+>)[^>]*|#([\\w-]+))$/;(ce.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||k,\"string\"==typeof e){if(!(r=\"<\"===e[0]&&\">\"===e[e.length-1]&&3<=e.length?[null,e,null]:S.exec(e))||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof ce?t[0]:t,ce.merge(this,ce.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:C,!0)),w.test(r[1])&&ce.isPlainObject(t))for(r in t)v(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(i=C.getElementById(r[2]))&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):v(e)?void 0!==n.ready?n.ready(e):e(ce):ce.makeArray(e,this)}).prototype=ce.fn,k=ce(C);var E=/^(?:parents|prev(?:Until|All))/,j={children:!0,contents:!0,next:!0,prev:!0};function A(e,t){while((e=e[t])&&1!==e.nodeType);return e}ce.fn.extend({has:function(e){var t=ce(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(ce.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a=\"string\"!=typeof e&&ce(e);if(!b.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&ce.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?ce.uniqueSort(o):o)},index:function(e){return e?\"string\"==typeof e?se.call(ce(e),this[0]):se.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(ce.uniqueSort(ce.merge(this.get(),ce(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),ce.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return d(e,\"parentNode\")},parentsUntil:function(e,t,n){return d(e,\"parentNode\",n)},next:function(e){return A(e,\"nextSibling\")},prev:function(e){return A(e,\"previousSibling\")},nextAll:function(e){return d(e,\"nextSibling\")},prevAll:function(e){return d(e,\"previousSibling\")},nextUntil:function(e,t,n){return d(e,\"nextSibling\",n)},prevUntil:function(e,t,n){return d(e,\"previousSibling\",n)},siblings:function(e){return h((e.parentNode||{}).firstChild,e)},children:function(e){return h(e.firstChild)},contents:function(e){return null!=e.contentDocument&&r(e.contentDocument)?e.contentDocument:(fe(e,\"template\")&&(e=e.content||e),ce.merge([],e.childNodes))}},function(r,i){ce.fn[r]=function(e,t){var n=ce.map(this,i,e);return\"Until\"!==r.slice(-5)&&(t=e),t&&\"string\"==typeof t&&(n=ce.filter(t,n)),1<this.length&&(j[r]||ce.uniqueSort(n),E.test(r)&&n.reverse()),this.pushStack(n)}});var D=/[^\\x20\\t\\r\\n\\f]+/g;function N(e){return e}function q(e){throw e}function L(e,t,n,r){var i;try{e&&v(i=e.promise)?i.call(e).done(t).fail(n):e&&v(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}ce.Callbacks=function(r){var e,n;r=\"string\"==typeof r?(e=r,n={},ce.each(e.match(D)||[],function(e,t){n[t]=!0}),n):ce.extend({},r);var i,t,o,a,s=[],u=[],l=-1,c=function(){for(a=a||r.once,o=i=!0;u.length;l=-1){t=u.shift();while(++l<s.length)!1===s[l].apply(t[0],t[1])&&r.stopOnFalse&&(l=s.length,t=!1)}r.memory||(t=!1),i=!1,a&&(s=t?[]:\"\")},f={add:function(){return s&&(t&&!i&&(l=s.length-1,u.push(t)),function n(e){ce.each(e,function(e,t){v(t)?r.unique&&f.has(t)||s.push(t):t&&t.length&&\"string\"!==x(t)&&n(t)})}(arguments),t&&!i&&c()),this},remove:function(){return ce.each(arguments,function(e,t){var n;while(-1<(n=ce.inArray(t,s,n)))s.splice(n,1),n<=l&&l--}),this},has:function(e){return e?-1<ce.inArray(e,s):0<s.length},empty:function(){return s&&(s=[]),this},disable:function(){return a=u=[],s=t=\"\",this},disabled:function(){return!s},lock:function(){return a=u=[],t||i||(s=t=\"\"),this},locked:function(){return!!a},fireWith:function(e,t){return a||(t=[e,(t=t||[]).slice?t.slice():t],u.push(t),i||c()),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return!!o}};return f},ce.extend({Deferred:function(e){var o=[[\"notify\",\"progress\",ce.Callbacks(\"memory\"),ce.Callbacks(\"memory\"),2],[\"resolve\",\"done\",ce.Callbacks(\"once memory\"),ce.Callbacks(\"once memory\"),0,\"resolved\"],[\"reject\",\"fail\",ce.Callbacks(\"once memory\"),ce.Callbacks(\"once memory\"),1,\"rejected\"]],i=\"pending\",a={state:function(){return i},always:function(){return s.done(arguments).fail(arguments),this},\"catch\":function(e){return a.then(null,e)},pipe:function(){var i=arguments;return ce.Deferred(function(r){ce.each(o,function(e,t){var n=v(i[t[4]])&&i[t[4]];s[t[1]](function(){var e=n&&n.apply(this,arguments);e&&v(e.promise)?e.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[t[0]+\"With\"](this,n?[e]:arguments)})}),i=null}).promise()},then:function(t,n,r){var u=0;function l(i,o,a,s){return function(){var n=this,r=arguments,e=function(){var e,t;if(!(i<u)){if((e=a.apply(n,r))===o.promise())throw new TypeError(\"Thenable self-resolution\");t=e&&(\"object\"==typeof e||\"function\"==typeof e)&&e.then,v(t)?s?t.call(e,l(u,o,N,s),l(u,o,q,s)):(u++,t.call(e,l(u,o,N,s),l(u,o,q,s),l(u,o,N,o.notifyWith))):(a!==N&&(n=void 0,r=[e]),(s||o.resolveWith)(n,r))}},t=s?e:function(){try{e()}catch(e){ce.Deferred.exceptionHook&&ce.Deferred.exceptionHook(e,t.error),u<=i+1&&(a!==q&&(n=void 0,r=[e]),o.rejectWith(n,r))}};i?t():(ce.Deferred.getErrorHook?t.error=ce.Deferred.getErrorHook():ce.Deferred.getStackHook&&(t.error=ce.Deferred.getStackHook()),ie.setTimeout(t))}}return ce.Deferred(function(e){o[0][3].add(l(0,e,v(r)?r:N,e.notifyWith)),o[1][3].add(l(0,e,v(t)?t:N)),o[2][3].add(l(0,e,v(n)?n:q))}).promise()},promise:function(e){return null!=e?ce.extend(e,a):a}},s={};return ce.each(o,function(e,t){var n=t[2],r=t[5];a[t[1]]=n.add,r&&n.add(function(){i=r},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock),n.add(t[3].fire),s[t[0]]=function(){return s[t[0]+\"With\"](this===s?void 0:this,arguments),this},s[t[0]+\"With\"]=n.fireWith}),a.promise(s),e&&e.call(s,s),s},when:function(e){var n=arguments.length,t=n,r=Array(t),i=ae.call(arguments),o=ce.Deferred(),a=function(t){return function(e){r[t]=this,i[t]=1<arguments.length?ae.call(arguments):e,--n||o.resolveWith(r,i)}};if(n<=1&&(L(e,o.done(a(t)).resolve,o.reject,!n),\"pending\"===o.state()||v(i[t]&&i[t].then)))return o.then();while(t--)L(i[t],a(t),o.reject);return o.promise()}});var H=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;ce.Deferred.exceptionHook=function(e,t){ie.console&&ie.console.warn&&e&&H.test(e.name)&&ie.console.warn(\"jQuery.Deferred exception: \"+e.message,e.stack,t)},ce.readyException=function(e){ie.setTimeout(function(){throw e})};var O=ce.Deferred();function P(){C.removeEventListener(\"DOMContentLoaded\",P),ie.removeEventListener(\"load\",P),ce.ready()}ce.fn.ready=function(e){return O.then(e)[\"catch\"](function(e){ce.readyException(e)}),this},ce.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--ce.readyWait:ce.isReady)||(ce.isReady=!0)!==e&&0<--ce.readyWait||O.resolveWith(C,[ce])}}),ce.ready.then=O.then,\"complete\"===C.readyState||\"loading\"!==C.readyState&&!C.documentElement.doScroll?ie.setTimeout(ce.ready):(C.addEventListener(\"DOMContentLoaded\",P),ie.addEventListener(\"load\",P));var M=function(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if(\"object\"===x(n))for(s in i=!0,n)M(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,v(r)||(a=!0),l&&(a?(t.call(e,r),t=null):(l=t,t=function(e,t,n){return l.call(ce(e),n)})),t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o},R=/^-ms-/,I=/-([a-z])/g;function W(e,t){return t.toUpperCase()}function F(e){return e.replace(R,\"ms-\").replace(I,W)}var $=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function B(){this.expando=ce.expando+B.uid++}B.uid=1,B.prototype={cache:function(e){var t=e[this.expando];return t||(t={},$(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if(\"string\"==typeof t)i[F(t)]=n;else for(r in t)i[F(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][F(t)]},access:function(e,t,n){return void 0===t||t&&\"string\"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(F):(t=F(t))in r?[t]:t.match(D)||[]).length;while(n--)delete r[t[n]]}(void 0===t||ce.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!ce.isEmptyObject(t)}};var _=new B,z=new B,X=/^(?:\\{[\\w\\W]*\\}|\\[[\\w\\W]*\\])$/,U=/[A-Z]/g;function V(e,t,n){var r,i;if(void 0===n&&1===e.nodeType)if(r=\"data-\"+t.replace(U,\"-$&\").toLowerCase(),\"string\"==typeof(n=e.getAttribute(r))){try{n=\"true\"===(i=n)||\"false\"!==i&&(\"null\"===i?null:i===+i+\"\"?+i:X.test(i)?JSON.parse(i):i)}catch(e){}z.set(e,t,n)}else n=void 0;return n}ce.extend({hasData:function(e){return z.hasData(e)||_.hasData(e)},data:function(e,t,n){return z.access(e,t,n)},removeData:function(e,t){z.remove(e,t)},_data:function(e,t,n){return _.access(e,t,n)},_removeData:function(e,t){_.remove(e,t)}}),ce.fn.extend({data:function(n,e){var t,r,i,o=this[0],a=o&&o.attributes;if(void 0===n){if(this.length&&(i=z.get(o),1===o.nodeType&&!_.get(o,\"hasDataAttrs\"))){t=a.length;while(t--)a[t]&&0===(r=a[t].name).indexOf(\"data-\")&&(r=F(r.slice(5)),V(o,r,i[r]));_.set(o,\"hasDataAttrs\",!0)}return i}return\"object\"==typeof n?this.each(function(){z.set(this,n)}):M(this,function(e){var t;if(o&&void 0===e)return void 0!==(t=z.get(o,n))?t:void 0!==(t=V(o,n))?t:void 0;this.each(function(){z.set(this,n,e)})},null,e,1<arguments.length,null,!0)},removeData:function(e){return this.each(function(){z.remove(this,e)})}}),ce.extend({queue:function(e,t,n){var r;if(e)return t=(t||\"fx\")+\"queue\",r=_.get(e,t),n&&(!r||Array.isArray(n)?r=_.access(e,t,ce.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||\"fx\";var n=ce.queue(e,t),r=n.length,i=n.shift(),o=ce._queueHooks(e,t);\"inprogress\"===i&&(i=n.shift(),r--),i&&(\"fx\"===t&&n.unshift(\"inprogress\"),delete o.stop,i.call(e,function(){ce.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+\"queueHooks\";return _.get(e,n)||_.access(e,n,{empty:ce.Callbacks(\"once memory\").add(function(){_.remove(e,[t+\"queue\",n])})})}}),ce.fn.extend({queue:function(t,n){var e=2;return\"string\"!=typeof t&&(n=t,t=\"fx\",e--),arguments.length<e?ce.queue(this[0],t):void 0===n?this:this.each(function(){var e=ce.queue(this,t,n);ce._queueHooks(this,t),\"fx\"===t&&\"inprogress\"!==e[0]&&ce.dequeue(this,t)})},dequeue:function(e){return this.each(function(){ce.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||\"fx\",[])},promise:function(e,t){var n,r=1,i=ce.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};\"string\"!=typeof e&&(t=e,e=void 0),e=e||\"fx\";while(a--)(n=_.get(o[a],e+\"queueHooks\"))&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var G=/[+-]?(?:\\d*\\.|)\\d+(?:[eE][+-]?\\d+|)/.source,Y=new RegExp(\"^(?:([+-])=|)(\"+G+\")([a-z%]*)$\",\"i\"),Q=[\"Top\",\"Right\",\"Bottom\",\"Left\"],J=C.documentElement,K=function(e){return ce.contains(e.ownerDocument,e)},Z={composed:!0};J.getRootNode&&(K=function(e){return ce.contains(e.ownerDocument,e)||e.getRootNode(Z)===e.ownerDocument});var ee=function(e,t){return\"none\"===(e=t||e).style.display||\"\"===e.style.display&&K(e)&&\"none\"===ce.css(e,\"display\")};function te(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return ce.css(e,t,\"\")},u=s(),l=n&&n[3]||(ce.cssNumber[t]?\"\":\"px\"),c=e.nodeType&&(ce.cssNumber[t]||\"px\"!==l&&+u)&&Y.exec(ce.css(e,t));if(c&&c[3]!==l){u/=2,l=l||c[3],c=+u||1;while(a--)ce.style(e,t,c+l),(1-o)*(1-(o=s()/u||.5))<=0&&(a=0),c/=o;c*=2,ce.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}var ne={};function re(e,t){for(var n,r,i,o,a,s,u,l=[],c=0,f=e.length;c<f;c++)(r=e[c]).style&&(n=r.style.display,t?(\"none\"===n&&(l[c]=_.get(r,\"display\")||null,l[c]||(r.style.display=\"\")),\"\"===r.style.display&&ee(r)&&(l[c]=(u=a=o=void 0,a=(i=r).ownerDocument,s=i.nodeName,(u=ne[s])||(o=a.body.appendChild(a.createElement(s)),u=ce.css(o,\"display\"),o.parentNode.removeChild(o),\"none\"===u&&(u=\"block\"),ne[s]=u)))):\"none\"!==n&&(l[c]=\"none\",_.set(r,\"display\",n)));for(c=0;c<f;c++)null!=l[c]&&(e[c].style.display=l[c]);return e}ce.fn.extend({show:function(){return re(this,!0)},hide:function(){return re(this)},toggle:function(e){return\"boolean\"==typeof e?e?this.show():this.hide():this.each(function(){ee(this)?ce(this).show():ce(this).hide()})}});var xe,be,we=/^(?:checkbox|radio)$/i,Te=/<([a-z][^\\/\\0>\\x20\\t\\r\\n\\f]*)/i,Ce=/^$|^module$|\\/(?:java|ecma)script/i;xe=C.createDocumentFragment().appendChild(C.createElement(\"div\")),(be=C.createElement(\"input\")).setAttribute(\"type\",\"radio\"),be.setAttribute(\"checked\",\"checked\"),be.setAttribute(\"name\",\"t\"),xe.appendChild(be),le.checkClone=xe.cloneNode(!0).cloneNode(!0).lastChild.checked,xe.innerHTML=\"<textarea>x</textarea>\",le.noCloneChecked=!!xe.cloneNode(!0).lastChild.defaultValue,xe.innerHTML=\"<option></option>\",le.option=!!xe.lastChild;var ke={thead:[1,\"<table>\",\"</table>\"],col:[2,\"<table><colgroup>\",\"</colgroup></table>\"],tr:[2,\"<table><tbody>\",\"</tbody></table>\"],td:[3,\"<table><tbody><tr>\",\"</tr></tbody></table>\"],_default:[0,\"\",\"\"]};function Se(e,t){var n;return n=\"undefined\"!=typeof e.getElementsByTagName?e.getElementsByTagName(t||\"*\"):\"undefined\"!=typeof e.querySelectorAll?e.querySelectorAll(t||\"*\"):[],void 0===t||t&&fe(e,t)?ce.merge([e],n):n}function Ee(e,t){for(var n=0,r=e.length;n<r;n++)_.set(e[n],\"globalEval\",!t||_.get(t[n],\"globalEval\"))}ke.tbody=ke.tfoot=ke.colgroup=ke.caption=ke.thead,ke.th=ke.td,le.option||(ke.optgroup=ke.option=[1,\"<select multiple='multiple'>\",\"</select>\"]);var je=/<|&#?\\w+;/;function Ae(e,t,n,r,i){for(var o,a,s,u,l,c,f=t.createDocumentFragment(),p=[],d=0,h=e.length;d<h;d++)if((o=e[d])||0===o)if(\"object\"===x(o))ce.merge(p,o.nodeType?[o]:o);else if(je.test(o)){a=a||f.appendChild(t.createElement(\"div\")),s=(Te.exec(o)||[\"\",\"\"])[1].toLowerCase(),u=ke[s]||ke._default,a.innerHTML=u[1]+ce.htmlPrefilter(o)+u[2],c=u[0];while(c--)a=a.lastChild;ce.merge(p,a.childNodes),(a=f.firstChild).textContent=\"\"}else p.push(t.createTextNode(o));f.textContent=\"\",d=0;while(o=p[d++])if(r&&-1<ce.inArray(o,r))i&&i.push(o);else if(l=K(o),a=Se(f.appendChild(o),\"script\"),l&&Ee(a),n){c=0;while(o=a[c++])Ce.test(o.type||\"\")&&n.push(o)}return f}var De=/^([^.]*)(?:\\.(.+)|)/;function Ne(){return!0}function qe(){return!1}function Le(e,t,n,r,i,o){var a,s;if(\"object\"==typeof t){for(s in\"string\"!=typeof n&&(r=r||n,n=void 0),t)Le(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&(\"string\"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=qe;else if(!i)return e;return 1===o&&(a=i,(i=function(e){return ce().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=ce.guid++)),e.each(function(){ce.event.add(this,t,i,r,n)})}function He(e,r,t){t?(_.set(e,r,!1),ce.event.add(e,r,{namespace:!1,handler:function(e){var t,n=_.get(this,r);if(1&e.isTrigger&&this[r]){if(n)(ce.event.special[r]||{}).delegateType&&e.stopPropagation();else if(n=ae.call(arguments),_.set(this,r,n),this[r](),t=_.get(this,r),_.set(this,r,!1),n!==t)return e.stopImmediatePropagation(),e.preventDefault(),t}else n&&(_.set(this,r,ce.event.trigger(n[0],n.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=Ne)}})):void 0===_.get(e,r)&&ce.event.add(e,r,Ne)}ce.event={global:{},add:function(t,e,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=_.get(t);if($(t)){n.handler&&(n=(o=n).handler,i=o.selector),i&&ce.find.matchesSelector(J,i),n.guid||(n.guid=ce.guid++),(u=v.events)||(u=v.events=Object.create(null)),(a=v.handle)||(a=v.handle=function(e){return\"undefined\"!=typeof ce&&ce.event.triggered!==e.type?ce.event.dispatch.apply(t,arguments):void 0}),l=(e=(e||\"\").match(D)||[\"\"]).length;while(l--)d=g=(s=De.exec(e[l])||[])[1],h=(s[2]||\"\").split(\".\").sort(),d&&(f=ce.event.special[d]||{},d=(i?f.delegateType:f.bindType)||d,f=ce.event.special[d]||{},c=ce.extend({type:d,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&ce.expr.match.needsContext.test(i),namespace:h.join(\".\")},o),(p=u[d])||((p=u[d]=[]).delegateCount=0,f.setup&&!1!==f.setup.call(t,r,h,a)||t.addEventListener&&t.addEventListener(d,a)),f.add&&(f.add.call(t,c),c.handler.guid||(c.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,c):p.push(c),ce.event.global[d]=!0)}},remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=_.hasData(e)&&_.get(e);if(v&&(u=v.events)){l=(t=(t||\"\").match(D)||[\"\"]).length;while(l--)if(d=g=(s=De.exec(t[l])||[])[1],h=(s[2]||\"\").split(\".\").sort(),d){f=ce.event.special[d]||{},p=u[d=(r?f.delegateType:f.bindType)||d]||[],s=s[2]&&new RegExp(\"(^|\\\\.)\"+h.join(\"\\\\.(?:.*\\\\.|)\")+\"(\\\\.|$)\"),a=o=p.length;while(o--)c=p[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&(\"**\"!==r||!c.selector)||(p.splice(o,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c));a&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,h,v.handle)||ce.removeEvent(e,d,v.handle),delete u[d])}else for(d in u)ce.event.remove(e,d+t[l],n,r,!0);ce.isEmptyObject(u)&&_.remove(e,\"handle events\")}},dispatch:function(e){var t,n,r,i,o,a,s=new Array(arguments.length),u=ce.event.fix(e),l=(_.get(this,\"events\")||Object.create(null))[u.type]||[],c=ce.event.special[u.type]||{};for(s[0]=u,t=1;t<arguments.length;t++)s[t]=arguments[t];if(u.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,u)){a=ce.event.handlers.call(this,u,l),t=0;while((i=a[t++])&&!u.isPropagationStopped()){u.currentTarget=i.elem,n=0;while((o=i.handlers[n++])&&!u.isImmediatePropagationStopped())u.rnamespace&&!1!==o.namespace&&!u.rnamespace.test(o.namespace)||(u.handleObj=o,u.data=o.data,void 0!==(r=((ce.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,s))&&!1===(u.result=r)&&(u.preventDefault(),u.stopPropagation()))}return c.postDispatch&&c.postDispatch.call(this,u),u.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!(\"click\"===e.type&&1<=e.button))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&(\"click\"!==e.type||!0!==l.disabled)){for(o=[],a={},n=0;n<u;n++)void 0===a[i=(r=t[n]).selector+\" \"]&&(a[i]=r.needsContext?-1<ce(i,this).index(l):ce.find(i,this,null,[l]).length),a[i]&&o.push(r);o.length&&s.push({elem:l,handlers:o})}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(t,e){Object.defineProperty(ce.Event.prototype,t,{enumerable:!0,configurable:!0,get:v(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[ce.expando]?e:new ce.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return we.test(t.type)&&t.click&&fe(t,\"input\")&&He(t,\"click\",!0),!1},trigger:function(e){var t=this||e;return we.test(t.type)&&t.click&&fe(t,\"input\")&&He(t,\"click\"),!0},_default:function(e){var t=e.target;return we.test(t.type)&&t.click&&fe(t,\"input\")&&_.get(t,\"click\")||fe(t,\"a\")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},ce.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},ce.Event=function(e,t){if(!(this instanceof ce.Event))return new ce.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Ne:qe,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&ce.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[ce.expando]=!0},ce.Event.prototype={constructor:ce.Event,isDefaultPrevented:qe,isPropagationStopped:qe,isImmediatePropagationStopped:qe,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Ne,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Ne,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Ne,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},ce.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,\"char\":!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},ce.event.addProp),ce.each({focus:\"focusin\",blur:\"focusout\"},function(r,i){function o(e){if(C.documentMode){var t=_.get(this,\"handle\"),n=ce.event.fix(e);n.type=\"focusin\"===e.type?\"focus\":\"blur\",n.isSimulated=!0,t(e),n.target===n.currentTarget&&t(n)}else ce.event.simulate(i,e.target,ce.event.fix(e))}ce.event.special[r]={setup:function(){var e;if(He(this,r,!0),!C.documentMode)return!1;(e=_.get(this,i))||this.addEventListener(i,o),_.set(this,i,(e||0)+1)},trigger:function(){return He(this,r),!0},teardown:function(){var e;if(!C.documentMode)return!1;(e=_.get(this,i)-1)?_.set(this,i,e):(this.removeEventListener(i,o),_.remove(this,i))},_default:function(e){return _.get(e.target,r)},delegateType:i},ce.event.special[i]={setup:function(){var e=this.ownerDocument||this.document||this,t=C.documentMode?this:e,n=_.get(t,i);n||(C.documentMode?this.addEventListener(i,o):e.addEventListener(r,o,!0)),_.set(t,i,(n||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=C.documentMode?this:e,n=_.get(t,i)-1;n?_.set(t,i,n):(C.documentMode?this.removeEventListener(i,o):e.removeEventListener(r,o,!0),_.remove(t,i))}}}),ce.each({mouseenter:\"mouseover\",mouseleave:\"mouseout\",pointerenter:\"pointerover\",pointerleave:\"pointerout\"},function(e,i){ce.event.special[e]={delegateType:i,bindType:i,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;return n&&(n===this||ce.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=i),t}}}),ce.fn.extend({on:function(e,t,n,r){return Le(this,e,t,n,r)},one:function(e,t,n,r){return Le(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,ce(e.delegateTarget).off(r.namespace?r.origType+\".\"+r.namespace:r.origType,r.selector,r.handler),this;if(\"object\"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&\"function\"!=typeof t||(n=t,t=void 0),!1===n&&(n=qe),this.each(function(){ce.event.remove(this,e,n,t)})}});var Oe=/<script|<style|<link/i,Pe=/checked\\s*(?:[^=]|=\\s*.checked.)/i,Me=/^\\s*<!\\[CDATA\\[|\\]\\]>\\s*$/g;function Re(e,t){return fe(e,\"table\")&&fe(11!==t.nodeType?t:t.firstChild,\"tr\")&&ce(e).children(\"tbody\")[0]||e}function Ie(e){return e.type=(null!==e.getAttribute(\"type\"))+\"/\"+e.type,e}function We(e){return\"true/\"===(e.type||\"\").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute(\"type\"),e}function Fe(e,t){var n,r,i,o,a,s;if(1===t.nodeType){if(_.hasData(e)&&(s=_.get(e).events))for(i in _.remove(t,\"handle events\"),s)for(n=0,r=s[i].length;n<r;n++)ce.event.add(t,i,s[i][n]);z.hasData(e)&&(o=z.access(e),a=ce.extend({},o),z.set(t,a))}}function $e(n,r,i,o){r=g(r);var e,t,a,s,u,l,c=0,f=n.length,p=f-1,d=r[0],h=v(d);if(h||1<f&&\"string\"==typeof d&&!le.checkClone&&Pe.test(d))return n.each(function(e){var t=n.eq(e);h&&(r[0]=d.call(this,e,t.html())),$e(t,r,i,o)});if(f&&(t=(e=Ae(r,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){for(s=(a=ce.map(Se(e,\"script\"),Ie)).length;c<f;c++)u=e,c!==p&&(u=ce.clone(u,!0,!0),s&&ce.merge(a,Se(u,\"script\"))),i.call(n[c],u,c);if(s)for(l=a[a.length-1].ownerDocument,ce.map(a,We),c=0;c<s;c++)u=a[c],Ce.test(u.type||\"\")&&!_.access(u,\"globalEval\")&&ce.contains(l,u)&&(u.src&&\"module\"!==(u.type||\"\").toLowerCase()?ce._evalUrl&&!u.noModule&&ce._evalUrl(u.src,{nonce:u.nonce||u.getAttribute(\"nonce\")},l):m(u.textContent.replace(Me,\"\"),u,l))}return n}function Be(e,t,n){for(var r,i=t?ce.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||ce.cleanData(Se(r)),r.parentNode&&(n&&K(r)&&Ee(Se(r,\"script\")),r.parentNode.removeChild(r));return e}ce.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s,u,l,c=e.cloneNode(!0),f=K(e);if(!(le.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||ce.isXMLDoc(e)))for(a=Se(c),r=0,i=(o=Se(e)).length;r<i;r++)s=o[r],u=a[r],void 0,\"input\"===(l=u.nodeName.toLowerCase())&&we.test(s.type)?u.checked=s.checked:\"input\"!==l&&\"textarea\"!==l||(u.defaultValue=s.defaultValue);if(t)if(n)for(o=o||Se(e),a=a||Se(c),r=0,i=o.length;r<i;r++)Fe(o[r],a[r]);else Fe(e,c);return 0<(a=Se(c,\"script\")).length&&Ee(a,!f&&Se(e,\"script\")),c},cleanData:function(e){for(var t,n,r,i=ce.event.special,o=0;void 0!==(n=e[o]);o++)if($(n)){if(t=n[_.expando]){if(t.events)for(r in t.events)i[r]?ce.event.remove(n,r):ce.removeEvent(n,r,t.handle);n[_.expando]=void 0}n[z.expando]&&(n[z.expando]=void 0)}}}),ce.fn.extend({detach:function(e){return Be(this,e,!0)},remove:function(e){return Be(this,e)},text:function(e){return M(this,function(e){return void 0===e?ce.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return $e(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Re(this,e).appendChild(e)})},prepend:function(){return $e(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Re(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return $e(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return $e(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(ce.cleanData(Se(e,!1)),e.textContent=\"\");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return ce.clone(this,e,t)})},html:function(e){return M(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if(\"string\"==typeof e&&!Oe.test(e)&&!ke[(Te.exec(e)||[\"\",\"\"])[1].toLowerCase()]){e=ce.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(ce.cleanData(Se(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return $e(this,arguments,function(e){var t=this.parentNode;ce.inArray(this,n)<0&&(ce.cleanData(Se(this)),t&&t.replaceChild(e,this))},n)}}),ce.each({appendTo:\"append\",prependTo:\"prepend\",insertBefore:\"before\",insertAfter:\"after\",replaceAll:\"replaceWith\"},function(e,a){ce.fn[e]=function(e){for(var t,n=[],r=ce(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),ce(r[o])[a](t),s.apply(n,t.get());return this.pushStack(n)}});var _e=new RegExp(\"^(\"+G+\")(?!px)[a-z%]+$\",\"i\"),ze=/^--/,Xe=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=ie),t.getComputedStyle(e)},Ue=function(e,t,n){var r,i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.call(e),t)e.style[i]=o[i];return r},Ve=new RegExp(Q.join(\"|\"),\"i\");function Ge(e,t,n){var r,i,o,a,s=ze.test(t),u=e.style;return(n=n||Xe(e))&&(a=n.getPropertyValue(t)||n[t],s&&a&&(a=a.replace(ve,\"$1\")||void 0),\"\"!==a||K(e)||(a=ce.style(e,t)),!le.pixelBoxStyles()&&_e.test(a)&&Ve.test(t)&&(r=u.width,i=u.minWidth,o=u.maxWidth,u.minWidth=u.maxWidth=u.width=a,a=n.width,u.width=r,u.minWidth=i,u.maxWidth=o)),void 0!==a?a+\"\":a}function Ye(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(l){u.style.cssText=\"position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0\",l.style.cssText=\"position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%\",J.appendChild(u).appendChild(l);var e=ie.getComputedStyle(l);n=\"1%\"!==e.top,s=12===t(e.marginLeft),l.style.right=\"60%\",o=36===t(e.right),r=36===t(e.width),l.style.position=\"absolute\",i=12===t(l.offsetWidth/3),J.removeChild(u),l=null}}function t(e){return Math.round(parseFloat(e))}var n,r,i,o,a,s,u=C.createElement(\"div\"),l=C.createElement(\"div\");l.style&&(l.style.backgroundClip=\"content-box\",l.cloneNode(!0).style.backgroundClip=\"\",le.clearCloneStyle=\"content-box\"===l.style.backgroundClip,ce.extend(le,{boxSizingReliable:function(){return e(),r},pixelBoxStyles:function(){return e(),o},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),s},scrollboxSize:function(){return e(),i},reliableTrDimensions:function(){var e,t,n,r;return null==a&&(e=C.createElement(\"table\"),t=C.createElement(\"tr\"),n=C.createElement(\"div\"),e.style.cssText=\"position:absolute;left:-11111px;border-collapse:separate\",t.style.cssText=\"box-sizing:content-box;border:1px solid\",t.style.height=\"1px\",n.style.height=\"9px\",n.style.display=\"block\",J.appendChild(e).appendChild(t).appendChild(n),r=ie.getComputedStyle(t),a=parseInt(r.height,10)+parseInt(r.borderTopWidth,10)+parseInt(r.borderBottomWidth,10)===t.offsetHeight,J.removeChild(e)),a}}))}();var Qe=[\"Webkit\",\"Moz\",\"ms\"],Je=C.createElement(\"div\").style,Ke={};function Ze(e){var t=ce.cssProps[e]||Ke[e];return t||(e in Je?e:Ke[e]=function(e){var t=e[0].toUpperCase()+e.slice(1),n=Qe.length;while(n--)if((e=Qe[n]+t)in Je)return e}(e)||e)}var et=/^(none|table(?!-c[ea]).+)/,tt={position:\"absolute\",visibility:\"hidden\",display:\"block\"},nt={letterSpacing:\"0\",fontWeight:\"400\"};function rt(e,t,n){var r=Y.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||\"px\"):t}function it(e,t,n,r,i,o){var a=\"width\"===t?1:0,s=0,u=0,l=0;if(n===(r?\"border\":\"content\"))return 0;for(;a<4;a+=2)\"margin\"===n&&(l+=ce.css(e,n+Q[a],!0,i)),r?(\"content\"===n&&(u-=ce.css(e,\"padding\"+Q[a],!0,i)),\"margin\"!==n&&(u-=ce.css(e,\"border\"+Q[a]+\"Width\",!0,i))):(u+=ce.css(e,\"padding\"+Q[a],!0,i),\"padding\"!==n?u+=ce.css(e,\"border\"+Q[a]+\"Width\",!0,i):s+=ce.css(e,\"border\"+Q[a]+\"Width\",!0,i));return!r&&0<=o&&(u+=Math.max(0,Math.ceil(e[\"offset\"+t[0].toUpperCase()+t.slice(1)]-o-u-s-.5))||0),u+l}function ot(e,t,n){var r=Xe(e),i=(!le.boxSizingReliable()||n)&&\"border-box\"===ce.css(e,\"boxSizing\",!1,r),o=i,a=Ge(e,t,r),s=\"offset\"+t[0].toUpperCase()+t.slice(1);if(_e.test(a)){if(!n)return a;a=\"auto\"}return(!le.boxSizingReliable()&&i||!le.reliableTrDimensions()&&fe(e,\"tr\")||\"auto\"===a||!parseFloat(a)&&\"inline\"===ce.css(e,\"display\",!1,r))&&e.getClientRects().length&&(i=\"border-box\"===ce.css(e,\"boxSizing\",!1,r),(o=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+it(e,t,n||(i?\"border\":\"content\"),o,r,a)+\"px\"}function at(e,t,n,r,i){return new at.prototype.init(e,t,n,r,i)}ce.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Ge(e,\"opacity\");return\"\"===n?\"1\":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=F(t),u=ze.test(t),l=e.style;if(u||(t=Ze(s)),a=ce.cssHooks[t]||ce.cssHooks[s],void 0===n)return a&&\"get\"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];\"string\"===(o=typeof n)&&(i=Y.exec(n))&&i[1]&&(n=te(e,t,i),o=\"number\"),null!=n&&n==n&&(\"number\"!==o||u||(n+=i&&i[3]||(ce.cssNumber[s]?\"\":\"px\")),le.clearCloneStyle||\"\"!==n||0!==t.indexOf(\"background\")||(l[t]=\"inherit\"),a&&\"set\"in a&&void 0===(n=a.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n))}},css:function(e,t,n,r){var i,o,a,s=F(t);return ze.test(t)||(t=Ze(s)),(a=ce.cssHooks[t]||ce.cssHooks[s])&&\"get\"in a&&(i=a.get(e,!0,n)),void 0===i&&(i=Ge(e,t,r)),\"normal\"===i&&t in nt&&(i=nt[t]),\"\"===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),ce.each([\"height\",\"width\"],function(e,u){ce.cssHooks[u]={get:function(e,t,n){if(t)return!et.test(ce.css(e,\"display\"))||e.getClientRects().length&&e.getBoundingClientRect().width?ot(e,u,n):Ue(e,tt,function(){return ot(e,u,n)})},set:function(e,t,n){var r,i=Xe(e),o=!le.scrollboxSize()&&\"absolute\"===i.position,a=(o||n)&&\"border-box\"===ce.css(e,\"boxSizing\",!1,i),s=n?it(e,u,n,a,i):0;return a&&o&&(s-=Math.ceil(e[\"offset\"+u[0].toUpperCase()+u.slice(1)]-parseFloat(i[u])-it(e,u,\"border\",!1,i)-.5)),s&&(r=Y.exec(t))&&\"px\"!==(r[3]||\"px\")&&(e.style[u]=t,t=ce.css(e,u)),rt(0,t,s)}}}),ce.cssHooks.marginLeft=Ye(le.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Ge(e,\"marginLeft\"))||e.getBoundingClientRect().left-Ue(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+\"px\"}),ce.each({margin:\"\",padding:\"\",border:\"Width\"},function(i,o){ce.cssHooks[i+o]={expand:function(e){for(var t=0,n={},r=\"string\"==typeof e?e.split(\" \"):[e];t<4;t++)n[i+Q[t]+o]=r[t]||r[t-2]||r[0];return n}},\"margin\"!==i&&(ce.cssHooks[i+o].set=rt)}),ce.fn.extend({css:function(e,t){return M(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=Xe(e),i=t.length;a<i;a++)o[t[a]]=ce.css(e,t[a],!1,r);return o}return void 0!==n?ce.style(e,t,n):ce.css(e,t)},e,t,1<arguments.length)}}),((ce.Tween=at).prototype={constructor:at,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||ce.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(ce.cssNumber[n]?\"\":\"px\")},cur:function(){var e=at.propHooks[this.prop];return e&&e.get?e.get(this):at.propHooks._default.get(this)},run:function(e){var t,n=at.propHooks[this.prop];return this.options.duration?this.pos=t=ce.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):at.propHooks._default.set(this),this}}).init.prototype=at.prototype,(at.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=ce.css(e.elem,e.prop,\"\"))&&\"auto\"!==t?t:0},set:function(e){ce.fx.step[e.prop]?ce.fx.step[e.prop](e):1!==e.elem.nodeType||!ce.cssHooks[e.prop]&&null==e.elem.style[Ze(e.prop)]?e.elem[e.prop]=e.now:ce.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=at.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},ce.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:\"swing\"},ce.fx=at.prototype.init,ce.fx.step={};var st,ut,lt,ct,ft=/^(?:toggle|show|hide)$/,pt=/queueHooks$/;function dt(){ut&&(!1===C.hidden&&ie.requestAnimationFrame?ie.requestAnimationFrame(dt):ie.setTimeout(dt,ce.fx.interval),ce.fx.tick())}function ht(){return ie.setTimeout(function(){st=void 0}),st=Date.now()}function gt(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i[\"margin\"+(n=Q[r])]=i[\"padding\"+n]=e;return t&&(i.opacity=i.width=e),i}function vt(e,t,n){for(var r,i=(yt.tweeners[t]||[]).concat(yt.tweeners[\"*\"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function yt(o,e,t){var n,a,r=0,i=yt.prefilters.length,s=ce.Deferred().always(function(){delete u.elem}),u=function(){if(a)return!1;for(var e=st||ht(),t=Math.max(0,l.startTime+l.duration-e),n=1-(t/l.duration||0),r=0,i=l.tweens.length;r<i;r++)l.tweens[r].run(n);return s.notifyWith(o,[l,n,t]),n<1&&i?t:(i||s.notifyWith(o,[l,1,0]),s.resolveWith(o,[l]),!1)},l=s.promise({elem:o,props:ce.extend({},e),opts:ce.extend(!0,{specialEasing:{},easing:ce.easing._default},t),originalProperties:e,originalOptions:t,startTime:st||ht(),duration:t.duration,tweens:[],createTween:function(e,t){var n=ce.Tween(o,l.opts,e,t,l.opts.specialEasing[e]||l.opts.easing);return l.tweens.push(n),n},stop:function(e){var t=0,n=e?l.tweens.length:0;if(a)return this;for(a=!0;t<n;t++)l.tweens[t].run(1);return e?(s.notifyWith(o,[l,1,0]),s.resolveWith(o,[l,e])):s.rejectWith(o,[l,e]),this}}),c=l.props;for(!function(e,t){var n,r,i,o,a;for(n in e)if(i=t[r=F(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=ce.cssHooks[r])&&\"expand\"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(c,l.opts.specialEasing);r<i;r++)if(n=yt.prefilters[r].call(l,o,c,l.opts))return v(n.stop)&&(ce._queueHooks(l.elem,l.opts.queue).stop=n.stop.bind(n)),n;return ce.map(c,vt,l),v(l.opts.start)&&l.opts.start.call(o,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),ce.fx.timer(ce.extend(u,{elem:o,anim:l,queue:l.opts.queue})),l}ce.Animation=ce.extend(yt,{tweeners:{\"*\":[function(e,t){var n=this.createTween(e,t);return te(n.elem,e,Y.exec(t),n),n}]},tweener:function(e,t){v(e)?(t=e,e=[\"*\"]):e=e.match(D);for(var n,r=0,i=e.length;r<i;r++)n=e[r],yt.tweeners[n]=yt.tweeners[n]||[],yt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,a,s,u,l,c,f=\"width\"in t||\"height\"in t,p=this,d={},h=e.style,g=e.nodeType&&ee(e),v=_.get(e,\"fxshow\");for(r in n.queue||(null==(a=ce._queueHooks(e,\"fx\")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,p.always(function(){p.always(function(){a.unqueued--,ce.queue(e,\"fx\").length||a.empty.fire()})})),t)if(i=t[r],ft.test(i)){if(delete t[r],o=o||\"toggle\"===i,i===(g?\"hide\":\"show\")){if(\"show\"!==i||!v||void 0===v[r])continue;g=!0}d[r]=v&&v[r]||ce.style(e,r)}if((u=!ce.isEmptyObject(t))||!ce.isEmptyObject(d))for(r in f&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(l=v&&v.display)&&(l=_.get(e,\"display\")),\"none\"===(c=ce.css(e,\"display\"))&&(l?c=l:(re([e],!0),l=e.style.display||l,c=ce.css(e,\"display\"),re([e]))),(\"inline\"===c||\"inline-block\"===c&&null!=l)&&\"none\"===ce.css(e,\"float\")&&(u||(p.done(function(){h.display=l}),null==l&&(c=h.display,l=\"none\"===c?\"\":c)),h.display=\"inline-block\")),n.overflow&&(h.overflow=\"hidden\",p.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),u=!1,d)u||(v?\"hidden\"in v&&(g=v.hidden):v=_.access(e,\"fxshow\",{display:l}),o&&(v.hidden=!g),g&&re([e],!0),p.done(function(){for(r in g||re([e]),_.remove(e,\"fxshow\"),d)ce.style(e,r,d[r])})),u=vt(g?v[r]:0,r,p),r in v||(v[r]=u.start,g&&(u.end=u.start,u.start=0))}],prefilter:function(e,t){t?yt.prefilters.unshift(e):yt.prefilters.push(e)}}),ce.speed=function(e,t,n){var r=e&&\"object\"==typeof e?ce.extend({},e):{complete:n||!n&&t||v(e)&&e,duration:e,easing:n&&t||t&&!v(t)&&t};return ce.fx.off?r.duration=0:\"number\"!=typeof r.duration&&(r.duration in ce.fx.speeds?r.duration=ce.fx.speeds[r.duration]:r.duration=ce.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue=\"fx\"),r.old=r.complete,r.complete=function(){v(r.old)&&r.old.call(this),r.queue&&ce.dequeue(this,r.queue)},r},ce.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ee).css(\"opacity\",0).show().end().animate({opacity:t},e,n,r)},animate:function(t,e,n,r){var i=ce.isEmptyObject(t),o=ce.speed(e,n,r),a=function(){var e=yt(this,ce.extend({},t),o);(i||_.get(this,\"finish\"))&&e.stop(!0)};return a.finish=a,i||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(i,e,o){var a=function(e){var t=e.stop;delete e.stop,t(o)};return\"string\"!=typeof i&&(o=e,e=i,i=void 0),e&&this.queue(i||\"fx\",[]),this.each(function(){var e=!0,t=null!=i&&i+\"queueHooks\",n=ce.timers,r=_.get(this);if(t)r[t]&&r[t].stop&&a(r[t]);else for(t in r)r[t]&&r[t].stop&&pt.test(t)&&a(r[t]);for(t=n.length;t--;)n[t].elem!==this||null!=i&&n[t].queue!==i||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||ce.dequeue(this,i)})},finish:function(a){return!1!==a&&(a=a||\"fx\"),this.each(function(){var e,t=_.get(this),n=t[a+\"queue\"],r=t[a+\"queueHooks\"],i=ce.timers,o=n?n.length:0;for(t.finish=!0,ce.queue(this,a,[]),r&&r.stop&&r.stop.call(this,!0),e=i.length;e--;)i[e].elem===this&&i[e].queue===a&&(i[e].anim.stop(!0),i.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),ce.each([\"toggle\",\"show\",\"hide\"],function(e,r){var i=ce.fn[r];ce.fn[r]=function(e,t,n){return null==e||\"boolean\"==typeof e?i.apply(this,arguments):this.animate(gt(r,!0),e,t,n)}}),ce.each({slideDown:gt(\"show\"),slideUp:gt(\"hide\"),slideToggle:gt(\"toggle\"),fadeIn:{opacity:\"show\"},fadeOut:{opacity:\"hide\"},fadeToggle:{opacity:\"toggle\"}},function(e,r){ce.fn[e]=function(e,t,n){return this.animate(r,e,t,n)}}),ce.timers=[],ce.fx.tick=function(){var e,t=0,n=ce.timers;for(st=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||ce.fx.stop(),st=void 0},ce.fx.timer=function(e){ce.timers.push(e),ce.fx.start()},ce.fx.interval=13,ce.fx.start=function(){ut||(ut=!0,dt())},ce.fx.stop=function(){ut=null},ce.fx.speeds={slow:600,fast:200,_default:400},ce.fn.delay=function(r,e){return r=ce.fx&&ce.fx.speeds[r]||r,e=e||\"fx\",this.queue(e,function(e,t){var n=ie.setTimeout(e,r);t.stop=function(){ie.clearTimeout(n)}})},lt=C.createElement(\"input\"),ct=C.createElement(\"select\").appendChild(C.createElement(\"option\")),lt.type=\"checkbox\",le.checkOn=\"\"!==lt.value,le.optSelected=ct.selected,(lt=C.createElement(\"input\")).value=\"t\",lt.type=\"radio\",le.radioValue=\"t\"===lt.value;var mt,xt=ce.expr.attrHandle;ce.fn.extend({attr:function(e,t){return M(this,ce.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){ce.removeAttr(this,e)})}}),ce.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return\"undefined\"==typeof e.getAttribute?ce.prop(e,t,n):(1===o&&ce.isXMLDoc(e)||(i=ce.attrHooks[t.toLowerCase()]||(ce.expr.match.bool.test(t)?mt:void 0)),void 0!==n?null===n?void ce.removeAttr(e,t):i&&\"set\"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+\"\"),n):i&&\"get\"in i&&null!==(r=i.get(e,t))?r:null==(r=ce.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!le.radioValue&&\"radio\"===t&&fe(e,\"input\")){var n=e.value;return e.setAttribute(\"type\",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(D);if(i&&1===e.nodeType)while(n=i[r++])e.removeAttribute(n)}}),mt={set:function(e,t,n){return!1===t?ce.removeAttr(e,n):e.setAttribute(n,n),n}},ce.each(ce.expr.match.bool.source.match(/\\w+/g),function(e,t){var a=xt[t]||ce.find.attr;xt[t]=function(e,t,n){var r,i,o=t.toLowerCase();return n||(i=xt[o],xt[o]=r,r=null!=a(e,t,n)?o:null,xt[o]=i),r}});var bt=/^(?:input|select|textarea|button)$/i,wt=/^(?:a|area)$/i;function Tt(e){return(e.match(D)||[]).join(\" \")}function Ct(e){return e.getAttribute&&e.getAttribute(\"class\")||\"\"}function kt(e){return Array.isArray(e)?e:\"string\"==typeof e&&e.match(D)||[]}ce.fn.extend({prop:function(e,t){return M(this,ce.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[ce.propFix[e]||e]})}}),ce.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&ce.isXMLDoc(e)||(t=ce.propFix[t]||t,i=ce.propHooks[t]),void 0!==n?i&&\"set\"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&\"get\"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=ce.find.attr(e,\"tabindex\");return t?parseInt(t,10):bt.test(e.nodeName)||wt.test(e.nodeName)&&e.href?0:-1}}},propFix:{\"for\":\"htmlFor\",\"class\":\"className\"}}),le.optSelected||(ce.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),ce.each([\"tabIndex\",\"readOnly\",\"maxLength\",\"cellSpacing\",\"cellPadding\",\"rowSpan\",\"colSpan\",\"useMap\",\"frameBorder\",\"contentEditable\"],function(){ce.propFix[this.toLowerCase()]=this}),ce.fn.extend({addClass:function(t){var e,n,r,i,o,a;return v(t)?this.each(function(e){ce(this).addClass(t.call(this,e,Ct(this)))}):(e=kt(t)).length?this.each(function(){if(r=Ct(this),n=1===this.nodeType&&\" \"+Tt(r)+\" \"){for(o=0;o<e.length;o++)i=e[o],n.indexOf(\" \"+i+\" \")<0&&(n+=i+\" \");a=Tt(n),r!==a&&this.setAttribute(\"class\",a)}}):this},removeClass:function(t){var e,n,r,i,o,a;return v(t)?this.each(function(e){ce(this).removeClass(t.call(this,e,Ct(this)))}):arguments.length?(e=kt(t)).length?this.each(function(){if(r=Ct(this),n=1===this.nodeType&&\" \"+Tt(r)+\" \"){for(o=0;o<e.length;o++){i=e[o];while(-1<n.indexOf(\" \"+i+\" \"))n=n.replace(\" \"+i+\" \",\" \")}a=Tt(n),r!==a&&this.setAttribute(\"class\",a)}}):this:this.attr(\"class\",\"\")},toggleClass:function(t,n){var e,r,i,o,a=typeof t,s=\"string\"===a||Array.isArray(t);return v(t)?this.each(function(e){ce(this).toggleClass(t.call(this,e,Ct(this),n),n)}):\"boolean\"==typeof n&&s?n?this.addClass(t):this.removeClass(t):(e=kt(t),this.each(function(){if(s)for(o=ce(this),i=0;i<e.length;i++)r=e[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else void 0!==t&&\"boolean\"!==a||((r=Ct(this))&&_.set(this,\"__className__\",r),this.setAttribute&&this.setAttribute(\"class\",r||!1===t?\"\":_.get(this,\"__className__\")||\"\"))}))},hasClass:function(e){var t,n,r=0;t=\" \"+e+\" \";while(n=this[r++])if(1===n.nodeType&&-1<(\" \"+Tt(Ct(n))+\" \").indexOf(t))return!0;return!1}});var St=/\\r/g;ce.fn.extend({val:function(n){var r,e,i,t=this[0];return arguments.length?(i=v(n),this.each(function(e){var t;1===this.nodeType&&(null==(t=i?n.call(this,e,ce(this).val()):n)?t=\"\":\"number\"==typeof t?t+=\"\":Array.isArray(t)&&(t=ce.map(t,function(e){return null==e?\"\":e+\"\"})),(r=ce.valHooks[this.type]||ce.valHooks[this.nodeName.toLowerCase()])&&\"set\"in r&&void 0!==r.set(this,t,\"value\")||(this.value=t))})):t?(r=ce.valHooks[t.type]||ce.valHooks[t.nodeName.toLowerCase()])&&\"get\"in r&&void 0!==(e=r.get(t,\"value\"))?e:\"string\"==typeof(e=t.value)?e.replace(St,\"\"):null==e?\"\":e:void 0}}),ce.extend({valHooks:{option:{get:function(e){var t=ce.find.attr(e,\"value\");return null!=t?t:Tt(ce.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,a=\"select-one\"===e.type,s=a?null:[],u=a?o+1:i.length;for(r=o<0?u:a?o:0;r<u;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!fe(n.parentNode,\"optgroup\"))){if(t=ce(n).val(),a)return t;s.push(t)}return s},set:function(e,t){var n,r,i=e.options,o=ce.makeArray(t),a=i.length;while(a--)((r=i[a]).selected=-1<ce.inArray(ce.valHooks.option.get(r),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),ce.each([\"radio\",\"checkbox\"],function(){ce.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<ce.inArray(ce(e).val(),t)}},le.checkOn||(ce.valHooks[this].get=function(e){return null===e.getAttribute(\"value\")?\"on\":e.value})});var Et=ie.location,jt={guid:Date.now()},At=/\\?/;ce.parseXML=function(e){var t,n;if(!e||\"string\"!=typeof e)return null;try{t=(new ie.DOMParser).parseFromString(e,\"text/xml\")}catch(e){}return n=t&&t.getElementsByTagName(\"parsererror\")[0],t&&!n||ce.error(\"Invalid XML: \"+(n?ce.map(n.childNodes,function(e){return e.textContent}).join(\"\\n\"):e)),t};var Dt=/^(?:focusinfocus|focusoutblur)$/,Nt=function(e){e.stopPropagation()};ce.extend(ce.event,{trigger:function(e,t,n,r){var i,o,a,s,u,l,c,f,p=[n||C],d=ue.call(e,\"type\")?e.type:e,h=ue.call(e,\"namespace\")?e.namespace.split(\".\"):[];if(o=f=a=n=n||C,3!==n.nodeType&&8!==n.nodeType&&!Dt.test(d+ce.event.triggered)&&(-1<d.indexOf(\".\")&&(d=(h=d.split(\".\")).shift(),h.sort()),u=d.indexOf(\":\")<0&&\"on\"+d,(e=e[ce.expando]?e:new ce.Event(d,\"object\"==typeof e&&e)).isTrigger=r?2:3,e.namespace=h.join(\".\"),e.rnamespace=e.namespace?new RegExp(\"(^|\\\\.)\"+h.join(\"\\\\.(?:.*\\\\.|)\")+\"(\\\\.|$)\"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:ce.makeArray(t,[e]),c=ce.event.special[d]||{},r||!c.trigger||!1!==c.trigger.apply(n,t))){if(!r&&!c.noBubble&&!y(n)){for(s=c.delegateType||d,Dt.test(s+d)||(o=o.parentNode);o;o=o.parentNode)p.push(o),a=o;a===(n.ownerDocument||C)&&p.push(a.defaultView||a.parentWindow||ie)}i=0;while((o=p[i++])&&!e.isPropagationStopped())f=o,e.type=1<i?s:c.bindType||d,(l=(_.get(o,\"events\")||Object.create(null))[e.type]&&_.get(o,\"handle\"))&&l.apply(o,t),(l=u&&o[u])&&l.apply&&$(o)&&(e.result=l.apply(o,t),!1===e.result&&e.preventDefault());return e.type=d,r||e.isDefaultPrevented()||c._default&&!1!==c._default.apply(p.pop(),t)||!$(n)||u&&v(n[d])&&!y(n)&&((a=n[u])&&(n[u]=null),ce.event.triggered=d,e.isPropagationStopped()&&f.addEventListener(d,Nt),n[d](),e.isPropagationStopped()&&f.removeEventListener(d,Nt),ce.event.triggered=void 0,a&&(n[u]=a)),e.result}},simulate:function(e,t,n){var r=ce.extend(new ce.Event,n,{type:e,isSimulated:!0});ce.event.trigger(r,null,t)}}),ce.fn.extend({trigger:function(e,t){return this.each(function(){ce.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return ce.event.trigger(e,t,n,!0)}});var qt=/\\[\\]$/,Lt=/\\r?\\n/g,Ht=/^(?:submit|button|image|reset|file)$/i,Ot=/^(?:input|select|textarea|keygen)/i;function Pt(n,e,r,i){var t;if(Array.isArray(e))ce.each(e,function(e,t){r||qt.test(n)?i(n,t):Pt(n+\"[\"+(\"object\"==typeof t&&null!=t?e:\"\")+\"]\",t,r,i)});else if(r||\"object\"!==x(e))i(n,e);else for(t in e)Pt(n+\"[\"+t+\"]\",e[t],r,i)}ce.param=function(e,t){var n,r=[],i=function(e,t){var n=v(t)?t():t;r[r.length]=encodeURIComponent(e)+\"=\"+encodeURIComponent(null==n?\"\":n)};if(null==e)return\"\";if(Array.isArray(e)||e.jquery&&!ce.isPlainObject(e))ce.each(e,function(){i(this.name,this.value)});else for(n in e)Pt(n,e[n],t,i);return r.join(\"&\")},ce.fn.extend({serialize:function(){return ce.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=ce.prop(this,\"elements\");return e?ce.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!ce(this).is(\":disabled\")&&Ot.test(this.nodeName)&&!Ht.test(e)&&(this.checked||!we.test(e))}).map(function(e,t){var n=ce(this).val();return null==n?null:Array.isArray(n)?ce.map(n,function(e){return{name:t.name,value:e.replace(Lt,\"\\r\\n\")}}):{name:t.name,value:n.replace(Lt,\"\\r\\n\")}}).get()}});var Mt=/%20/g,Rt=/#.*$/,It=/([?&])_=[^&]*/,Wt=/^(.*?):[ \\t]*([^\\r\\n]*)$/gm,Ft=/^(?:GET|HEAD)$/,$t=/^\\/\\//,Bt={},_t={},zt=\"*/\".concat(\"*\"),Xt=C.createElement(\"a\");function Ut(o){return function(e,t){\"string\"!=typeof e&&(t=e,e=\"*\");var n,r=0,i=e.toLowerCase().match(D)||[];if(v(t))while(n=i[r++])\"+\"===n[0]?(n=n.slice(1)||\"*\",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function Vt(t,i,o,a){var s={},u=t===_t;function l(e){var r;return s[e]=!0,ce.each(t[e]||[],function(e,t){var n=t(i,o,a);return\"string\"!=typeof n||u||s[n]?u?!(r=n):void 0:(i.dataTypes.unshift(n),l(n),!1)}),r}return l(i.dataTypes[0])||!s[\"*\"]&&l(\"*\")}function Gt(e,t){var n,r,i=ce.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&ce.extend(!0,e,r),e}Xt.href=Et.href,ce.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Et.href,type:\"GET\",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Et.protocol),global:!0,processData:!0,async:!0,contentType:\"application/x-www-form-urlencoded; charset=UTF-8\",accepts:{\"*\":zt,text:\"text/plain\",html:\"text/html\",xml:\"application/xml, text/xml\",json:\"application/json, text/javascript\"},contents:{xml:/\\bxml\\b/,html:/\\bhtml/,json:/\\bjson\\b/},responseFields:{xml:\"responseXML\",text:\"responseText\",json:\"responseJSON\"},converters:{\"* text\":String,\"text html\":!0,\"text json\":JSON.parse,\"text xml\":ce.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Gt(Gt(e,ce.ajaxSettings),t):Gt(ce.ajaxSettings,e)},ajaxPrefilter:Ut(Bt),ajaxTransport:Ut(_t),ajax:function(e,t){\"object\"==typeof e&&(t=e,e=void 0),t=t||{};var c,f,p,n,d,r,h,g,i,o,v=ce.ajaxSetup({},t),y=v.context||v,m=v.context&&(y.nodeType||y.jquery)?ce(y):ce.event,x=ce.Deferred(),b=ce.Callbacks(\"once memory\"),w=v.statusCode||{},a={},s={},u=\"canceled\",T={readyState:0,getResponseHeader:function(e){var t;if(h){if(!n){n={};while(t=Wt.exec(p))n[t[1].toLowerCase()+\" \"]=(n[t[1].toLowerCase()+\" \"]||[]).concat(t[2])}t=n[e.toLowerCase()+\" \"]}return null==t?null:t.join(\", \")},getAllResponseHeaders:function(){return h?p:null},setRequestHeader:function(e,t){return null==h&&(e=s[e.toLowerCase()]=s[e.toLowerCase()]||e,a[e]=t),this},overrideMimeType:function(e){return null==h&&(v.mimeType=e),this},statusCode:function(e){var t;if(e)if(h)T.always(e[T.status]);else for(t in e)w[t]=[w[t],e[t]];return this},abort:function(e){var t=e||u;return c&&c.abort(t),l(0,t),this}};if(x.promise(T),v.url=((e||v.url||Et.href)+\"\").replace($t,Et.protocol+\"//\"),v.type=t.method||t.type||v.method||v.type,v.dataTypes=(v.dataType||\"*\").toLowerCase().match(D)||[\"\"],null==v.crossDomain){r=C.createElement(\"a\");try{r.href=v.url,r.href=r.href,v.crossDomain=Xt.protocol+\"//\"+Xt.host!=r.protocol+\"//\"+r.host}catch(e){v.crossDomain=!0}}if(v.data&&v.processData&&\"string\"!=typeof v.data&&(v.data=ce.param(v.data,v.traditional)),Vt(Bt,v,t,T),h)return T;for(i in(g=ce.event&&v.global)&&0==ce.active++&&ce.event.trigger(\"ajaxStart\"),v.type=v.type.toUpperCase(),v.hasContent=!Ft.test(v.type),f=v.url.replace(Rt,\"\"),v.hasContent?v.data&&v.processData&&0===(v.contentType||\"\").indexOf(\"application/x-www-form-urlencoded\")&&(v.data=v.data.replace(Mt,\"+\")):(o=v.url.slice(f.length),v.data&&(v.processData||\"string\"==typeof v.data)&&(f+=(At.test(f)?\"&\":\"?\")+v.data,delete v.data),!1===v.cache&&(f=f.replace(It,\"$1\"),o=(At.test(f)?\"&\":\"?\")+\"_=\"+jt.guid+++o),v.url=f+o),v.ifModified&&(ce.lastModified[f]&&T.setRequestHeader(\"If-Modified-Since\",ce.lastModified[f]),ce.etag[f]&&T.setRequestHeader(\"If-None-Match\",ce.etag[f])),(v.data&&v.hasContent&&!1!==v.contentType||t.contentType)&&T.setRequestHeader(\"Content-Type\",v.contentType),T.setRequestHeader(\"Accept\",v.dataTypes[0]&&v.accepts[v.dataTypes[0]]?v.accepts[v.dataTypes[0]]+(\"*\"!==v.dataTypes[0]?\", \"+zt+\"; q=0.01\":\"\"):v.accepts[\"*\"]),v.headers)T.setRequestHeader(i,v.headers[i]);if(v.beforeSend&&(!1===v.beforeSend.call(y,T,v)||h))return T.abort();if(u=\"abort\",b.add(v.complete),T.done(v.success),T.fail(v.error),c=Vt(_t,v,t,T)){if(T.readyState=1,g&&m.trigger(\"ajaxSend\",[T,v]),h)return T;v.async&&0<v.timeout&&(d=ie.setTimeout(function(){T.abort(\"timeout\")},v.timeout));try{h=!1,c.send(a,l)}catch(e){if(h)throw e;l(-1,e)}}else l(-1,\"No Transport\");function l(e,t,n,r){var i,o,a,s,u,l=t;h||(h=!0,d&&ie.clearTimeout(d),c=void 0,p=r||\"\",T.readyState=0<e?4:0,i=200<=e&&e<300||304===e,n&&(s=function(e,t,n){var r,i,o,a,s=e.contents,u=e.dataTypes;while(\"*\"===u[0])u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader(\"Content-Type\"));if(r)for(i in s)if(s[i]&&s[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+\" \"+u[0]]){o=i;break}a||(a=i)}o=o||a}if(o)return o!==u[0]&&u.unshift(o),n[o]}(v,T,n)),!i&&-1<ce.inArray(\"script\",v.dataTypes)&&ce.inArray(\"json\",v.dataTypes)<0&&(v.converters[\"text script\"]=function(){}),s=function(e,t,n,r){var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];o=c.shift();while(o)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if(\"*\"===o)o=u;else if(\"*\"!==u&&u!==o){if(!(a=l[u+\" \"+o]||l[\"* \"+o]))for(i in l)if((s=i.split(\" \"))[1]===o&&(a=l[u+\" \"+s[0]]||l[\"* \"+s[0]])){!0===a?a=l[i]:!0!==l[i]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e[\"throws\"])t=a(t);else try{t=a(t)}catch(e){return{state:\"parsererror\",error:a?e:\"No conversion from \"+u+\" to \"+o}}}return{state:\"success\",data:t}}(v,s,T,i),i?(v.ifModified&&((u=T.getResponseHeader(\"Last-Modified\"))&&(ce.lastModified[f]=u),(u=T.getResponseHeader(\"etag\"))&&(ce.etag[f]=u)),204===e||\"HEAD\"===v.type?l=\"nocontent\":304===e?l=\"notmodified\":(l=s.state,o=s.data,i=!(a=s.error))):(a=l,!e&&l||(l=\"error\",e<0&&(e=0))),T.status=e,T.statusText=(t||l)+\"\",i?x.resolveWith(y,[o,l,T]):x.rejectWith(y,[T,l,a]),T.statusCode(w),w=void 0,g&&m.trigger(i?\"ajaxSuccess\":\"ajaxError\",[T,v,i?o:a]),b.fireWith(y,[T,l]),g&&(m.trigger(\"ajaxComplete\",[T,v]),--ce.active||ce.event.trigger(\"ajaxStop\")))}return T},getJSON:function(e,t,n){return ce.get(e,t,n,\"json\")},getScript:function(e,t){return ce.get(e,void 0,t,\"script\")}}),ce.each([\"get\",\"post\"],function(e,i){ce[i]=function(e,t,n,r){return v(t)&&(r=r||n,n=t,t=void 0),ce.ajax(ce.extend({url:e,type:i,dataType:r,data:t,success:n},ce.isPlainObject(e)&&e))}}),ce.ajaxPrefilter(function(e){var t;for(t in e.headers)\"content-type\"===t.toLowerCase()&&(e.contentType=e.headers[t]||\"\")}),ce._evalUrl=function(e,t,n){return ce.ajax({url:e,type:\"GET\",dataType:\"script\",cache:!0,async:!1,global:!1,converters:{\"text script\":function(){}},dataFilter:function(e){ce.globalEval(e,t,n)}})},ce.fn.extend({wrapAll:function(e){var t;return this[0]&&(v(e)&&(e=e.call(this[0])),t=ce(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){var e=this;while(e.firstElementChild)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return v(n)?this.each(function(e){ce(this).wrapInner(n.call(this,e))}):this.each(function(){var e=ce(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=v(t);return this.each(function(e){ce(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not(\"body\").each(function(){ce(this).replaceWith(this.childNodes)}),this}}),ce.expr.pseudos.hidden=function(e){return!ce.expr.pseudos.visible(e)},ce.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},ce.ajaxSettings.xhr=function(){try{return new ie.XMLHttpRequest}catch(e){}};var Yt={0:200,1223:204},Qt=ce.ajaxSettings.xhr();le.cors=!!Qt&&\"withCredentials\"in Qt,le.ajax=Qt=!!Qt,ce.ajaxTransport(function(i){var o,a;if(le.cors||Qt&&!i.crossDomain)return{send:function(e,t){var n,r=i.xhr();if(r.open(i.type,i.url,i.async,i.username,i.password),i.xhrFields)for(n in i.xhrFields)r[n]=i.xhrFields[n];for(n in i.mimeType&&r.overrideMimeType&&r.overrideMimeType(i.mimeType),i.crossDomain||e[\"X-Requested-With\"]||(e[\"X-Requested-With\"]=\"XMLHttpRequest\"),e)r.setRequestHeader(n,e[n]);o=function(e){return function(){o&&(o=a=r.onload=r.onerror=r.onabort=r.ontimeout=r.onreadystatechange=null,\"abort\"===e?r.abort():\"error\"===e?\"number\"!=typeof r.status?t(0,\"error\"):t(r.status,r.statusText):t(Yt[r.status]||r.status,r.statusText,\"text\"!==(r.responseType||\"text\")||\"string\"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=o(),a=r.onerror=r.ontimeout=o(\"error\"),void 0!==r.onabort?r.onabort=a:r.onreadystatechange=function(){4===r.readyState&&ie.setTimeout(function(){o&&a()})},o=o(\"abort\");try{r.send(i.hasContent&&i.data||null)}catch(e){if(o)throw e}},abort:function(){o&&o()}}}),ce.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),ce.ajaxSetup({accepts:{script:\"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript\"},contents:{script:/\\b(?:java|ecma)script\\b/},converters:{\"text script\":function(e){return ce.globalEval(e),e}}}),ce.ajaxPrefilter(\"script\",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type=\"GET\")}),ce.ajaxTransport(\"script\",function(n){var r,i;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){r=ce(\"<script>\").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on(\"load error\",i=function(e){r.remove(),i=null,e&&t(\"error\"===e.type?404:200,e.type)}),C.head.appendChild(r[0])},abort:function(){i&&i()}}});var Jt,Kt=[],Zt=/(=)\\?(?=&|$)|\\?\\?/;ce.ajaxSetup({jsonp:\"callback\",jsonpCallback:function(){var e=Kt.pop()||ce.expando+\"_\"+jt.guid++;return this[e]=!0,e}}),ce.ajaxPrefilter(\"json jsonp\",function(e,t,n){var r,i,o,a=!1!==e.jsonp&&(Zt.test(e.url)?\"url\":\"string\"==typeof e.data&&0===(e.contentType||\"\").indexOf(\"application/x-www-form-urlencoded\")&&Zt.test(e.data)&&\"data\");if(a||\"jsonp\"===e.dataTypes[0])return r=e.jsonpCallback=v(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Zt,\"$1\"+r):!1!==e.jsonp&&(e.url+=(At.test(e.url)?\"&\":\"?\")+e.jsonp+\"=\"+r),e.converters[\"script json\"]=function(){return o||ce.error(r+\" was not called\"),o[0]},e.dataTypes[0]=\"json\",i=ie[r],ie[r]=function(){o=arguments},n.always(function(){void 0===i?ce(ie).removeProp(r):ie[r]=i,e[r]&&(e.jsonpCallback=t.jsonpCallback,Kt.push(r)),o&&v(i)&&i(o[0]),o=i=void 0}),\"script\"}),le.createHTMLDocument=((Jt=C.implementation.createHTMLDocument(\"\").body).innerHTML=\"<form></form><form></form>\",2===Jt.childNodes.length),ce.parseHTML=function(e,t,n){return\"string\"!=typeof e?[]:(\"boolean\"==typeof t&&(n=t,t=!1),t||(le.createHTMLDocument?((r=(t=C.implementation.createHTMLDocument(\"\")).createElement(\"base\")).href=C.location.href,t.head.appendChild(r)):t=C),o=!n&&[],(i=w.exec(e))?[t.createElement(i[1])]:(i=Ae([e],t,o),o&&o.length&&ce(o).remove(),ce.merge([],i.childNodes)));var r,i,o},ce.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(\" \");return-1<s&&(r=Tt(e.slice(s)),e=e.slice(0,s)),v(t)?(n=t,t=void 0):t&&\"object\"==typeof t&&(i=\"POST\"),0<a.length&&ce.ajax({url:e,type:i||\"GET\",dataType:\"html\",data:t}).done(function(e){o=arguments,a.html(r?ce(\"<div>\").append(ce.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},ce.expr.pseudos.animated=function(t){return ce.grep(ce.timers,function(e){return t===e.elem}).length},ce.offset={setOffset:function(e,t,n){var r,i,o,a,s,u,l=ce.css(e,\"position\"),c=ce(e),f={};\"static\"===l&&(e.style.position=\"relative\"),s=c.offset(),o=ce.css(e,\"top\"),u=ce.css(e,\"left\"),(\"absolute\"===l||\"fixed\"===l)&&-1<(o+u).indexOf(\"auto\")?(a=(r=c.position()).top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(u)||0),v(t)&&(t=t.call(e,n,ce.extend({},s))),null!=t.top&&(f.top=t.top-s.top+a),null!=t.left&&(f.left=t.left-s.left+i),\"using\"in t?t.using.call(e,f):c.css(f)}},ce.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){ce.offset.setOffset(this,t,e)});var e,n,r=this[0];return r?r.getClientRects().length?(e=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if(\"fixed\"===ce.css(r,\"position\"))t=r.getBoundingClientRect();else{t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;while(e&&(e===n.body||e===n.documentElement)&&\"static\"===ce.css(e,\"position\"))e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=ce(e).offset()).top+=ce.css(e,\"borderTopWidth\",!0),i.left+=ce.css(e,\"borderLeftWidth\",!0))}return{top:t.top-i.top-ce.css(r,\"marginTop\",!0),left:t.left-i.left-ce.css(r,\"marginLeft\",!0)}}},offsetParent:function(){return this.map(function(){var e=this.offsetParent;while(e&&\"static\"===ce.css(e,\"position\"))e=e.offsetParent;return e||J})}}),ce.each({scrollLeft:\"pageXOffset\",scrollTop:\"pageYOffset\"},function(t,i){var o=\"pageYOffset\"===i;ce.fn[t]=function(e){return M(this,function(e,t,n){var r;if(y(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===n)return r?r[i]:e[t];r?r.scrollTo(o?r.pageXOffset:n,o?n:r.pageYOffset):e[t]=n},t,e,arguments.length)}}),ce.each([\"top\",\"left\"],function(e,n){ce.cssHooks[n]=Ye(le.pixelPosition,function(e,t){if(t)return t=Ge(e,n),_e.test(t)?ce(e).position()[n]+\"px\":t})}),ce.each({Height:\"height\",Width:\"width\"},function(a,s){ce.each({padding:\"inner\"+a,content:s,\"\":\"outer\"+a},function(r,o){ce.fn[o]=function(e,t){var n=arguments.length&&(r||\"boolean\"!=typeof e),i=r||(!0===e||!0===t?\"margin\":\"border\");return M(this,function(e,t,n){var r;return y(e)?0===o.indexOf(\"outer\")?e[\"inner\"+a]:e.document.documentElement[\"client\"+a]:9===e.nodeType?(r=e.documentElement,Math.max(e.body[\"scroll\"+a],r[\"scroll\"+a],e.body[\"offset\"+a],r[\"offset\"+a],r[\"client\"+a])):void 0===n?ce.css(e,t,i):ce.style(e,t,n,i)},s,n?e:void 0,n)}})}),ce.each([\"ajaxStart\",\"ajaxStop\",\"ajaxComplete\",\"ajaxError\",\"ajaxSuccess\",\"ajaxSend\"],function(e,t){ce.fn[t]=function(e){return this.on(t,e)}}),ce.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,\"**\"):this.off(t,e||\"**\",n)},hover:function(e,t){return this.on(\"mouseenter\",e).on(\"mouseleave\",t||e)}}),ce.each(\"blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu\".split(\" \"),function(e,n){ce.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});var en=/^[\\s\\uFEFF\\xA0]+|([^\\s\\uFEFF\\xA0])[\\s\\uFEFF\\xA0]+$/g;ce.proxy=function(e,t){var n,r,i;if(\"string\"==typeof t&&(n=e[t],t=e,e=n),v(e))return r=ae.call(arguments,2),(i=function(){return e.apply(t||this,r.concat(ae.call(arguments)))}).guid=e.guid=e.guid||ce.guid++,i},ce.holdReady=function(e){e?ce.readyWait++:ce.ready(!0)},ce.isArray=Array.isArray,ce.parseJSON=JSON.parse,ce.nodeName=fe,ce.isFunction=v,ce.isWindow=y,ce.camelCase=F,ce.type=x,ce.now=Date.now,ce.isNumeric=function(e){var t=ce.type(e);return(\"number\"===t||\"string\"===t)&&!isNaN(e-parseFloat(e))},ce.trim=function(e){return null==e?\"\":(e+\"\").replace(en,\"$1\")},\"function\"==typeof define&&define.amd&&define(\"jquery\",[],function(){return ce});var tn=ie.jQuery,nn=ie.$;return ce.noConflict=function(e){return ie.$===ce&&(ie.$=nn),e&&ie.jQuery===ce&&(ie.jQuery=tn),ce},\"undefined\"==typeof e&&(ie.jQuery=ie.$=ce),ce});\n\n/*!\n  * Bootstrap v5.3.5 (https://getbootstrap.com/)\n  * Copyright 2011-2025 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)\n  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n  */\n!function(t,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define(e):(t=\"undefined\"!=typeof globalThis?globalThis:t||self).bootstrap=e()}(this,(function(){\"use strict\";const t=new Map,e={set(e,i,n){t.has(e)||t.set(e,new Map);const s=t.get(e);s.has(i)||0===s.size?s.set(i,n):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`)},get:(e,i)=>t.has(e)&&t.get(e).get(i)||null,remove(e,i){if(!t.has(e))return;const n=t.get(e);n.delete(i),0===n.size&&t.delete(e)}},i=\"transitionend\",n=t=>(t&&window.CSS&&window.CSS.escape&&(t=t.replace(/#([^\\s\"#']+)/g,((t,e)=>`#${CSS.escape(e)}`))),t),s=t=>{t.dispatchEvent(new Event(i))},o=t=>!(!t||\"object\"!=typeof t)&&(void 0!==t.jquery&&(t=t[0]),void 0!==t.nodeType),r=t=>o(t)?t.jquery?t[0]:t:\"string\"==typeof t&&t.length>0?document.querySelector(n(t)):null,a=t=>{if(!o(t)||0===t.getClientRects().length)return!1;const e=\"visible\"===getComputedStyle(t).getPropertyValue(\"visibility\"),i=t.closest(\"details:not([open])\");if(!i)return e;if(i!==t){const e=t.closest(\"summary\");if(e&&e.parentNode!==i)return!1;if(null===e)return!1}return e},l=t=>!t||t.nodeType!==Node.ELEMENT_NODE||!!t.classList.contains(\"disabled\")||(void 0!==t.disabled?t.disabled:t.hasAttribute(\"disabled\")&&\"false\"!==t.getAttribute(\"disabled\")),c=t=>{if(!document.documentElement.attachShadow)return null;if(\"function\"==typeof t.getRootNode){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?c(t.parentNode):null},h=()=>{},d=t=>{t.offsetHeight},u=()=>window.jQuery&&!document.body.hasAttribute(\"data-bs-no-jquery\")?window.jQuery:null,f=[],p=()=>\"rtl\"===document.documentElement.dir,m=t=>{var e;e=()=>{const e=u();if(e){const i=t.NAME,n=e.fn[i];e.fn[i]=t.jQueryInterface,e.fn[i].Constructor=t,e.fn[i].noConflict=()=>(e.fn[i]=n,t.jQueryInterface)}},\"loading\"===document.readyState?(f.length||document.addEventListener(\"DOMContentLoaded\",(()=>{for(const t of f)t()})),f.push(e)):e()},g=(t,e=[],i=t)=>\"function\"==typeof t?t.call(...e):i,_=(t,e,n=!0)=>{if(!n)return void g(t);const o=(t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:i}=window.getComputedStyle(t);const n=Number.parseFloat(e),s=Number.parseFloat(i);return n||s?(e=e.split(\",\")[0],i=i.split(\",\")[0],1e3*(Number.parseFloat(e)+Number.parseFloat(i))):0})(e)+5;let r=!1;const a=({target:n})=>{n===e&&(r=!0,e.removeEventListener(i,a),g(t))};e.addEventListener(i,a),setTimeout((()=>{r||s(e)}),o)},b=(t,e,i,n)=>{const s=t.length;let o=t.indexOf(e);return-1===o?!i&&n?t[s-1]:t[0]:(o+=i?1:-1,n&&(o=(o+s)%s),t[Math.max(0,Math.min(o,s-1))])},v=/[^.]*(?=\\..*)\\.|.*/,y=/\\..*/,w=/::\\d+$/,A={};let E=1;const T={mouseenter:\"mouseover\",mouseleave:\"mouseout\"},C=new Set([\"click\",\"dblclick\",\"mouseup\",\"mousedown\",\"contextmenu\",\"mousewheel\",\"DOMMouseScroll\",\"mouseover\",\"mouseout\",\"mousemove\",\"selectstart\",\"selectend\",\"keydown\",\"keypress\",\"keyup\",\"orientationchange\",\"touchstart\",\"touchmove\",\"touchend\",\"touchcancel\",\"pointerdown\",\"pointermove\",\"pointerup\",\"pointerleave\",\"pointercancel\",\"gesturestart\",\"gesturechange\",\"gestureend\",\"focus\",\"blur\",\"change\",\"reset\",\"select\",\"submit\",\"focusin\",\"focusout\",\"load\",\"unload\",\"beforeunload\",\"resize\",\"move\",\"DOMContentLoaded\",\"readystatechange\",\"error\",\"abort\",\"scroll\"]);function O(t,e){return e&&`${e}::${E++}`||t.uidEvent||E++}function x(t){const e=O(t);return t.uidEvent=e,A[e]=A[e]||{},A[e]}function k(t,e,i=null){return Object.values(t).find((t=>t.callable===e&&t.delegationSelector===i))}function L(t,e,i){const n=\"string\"==typeof e,s=n?i:e||i;let o=I(t);return C.has(o)||(o=t),[n,s,o]}function S(t,e,i,n,s){if(\"string\"!=typeof e||!t)return;let[o,r,a]=L(e,i,n);if(e in T){const t=t=>function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return t.call(this,e)};r=t(r)}const l=x(t),c=l[a]||(l[a]={}),h=k(c,r,o?i:null);if(h)return void(h.oneOff=h.oneOff&&s);const d=O(r,e.replace(v,\"\")),u=o?function(t,e,i){return function n(s){const o=t.querySelectorAll(e);for(let{target:r}=s;r&&r!==this;r=r.parentNode)for(const a of o)if(a===r)return P(s,{delegateTarget:r}),n.oneOff&&N.off(t,s.type,e,i),i.apply(r,[s])}}(t,i,r):function(t,e){return function i(n){return P(n,{delegateTarget:t}),i.oneOff&&N.off(t,n.type,e),e.apply(t,[n])}}(t,r);u.delegationSelector=o?i:null,u.callable=r,u.oneOff=s,u.uidEvent=d,c[d]=u,t.addEventListener(a,u,o)}function D(t,e,i,n,s){const o=k(e[i],n,s);o&&(t.removeEventListener(i,o,Boolean(s)),delete e[i][o.uidEvent])}function $(t,e,i,n){const s=e[i]||{};for(const[o,r]of Object.entries(s))o.includes(n)&&D(t,e,i,r.callable,r.delegationSelector)}function I(t){return t=t.replace(y,\"\"),T[t]||t}const N={on(t,e,i,n){S(t,e,i,n,!1)},one(t,e,i,n){S(t,e,i,n,!0)},off(t,e,i,n){if(\"string\"!=typeof e||!t)return;const[s,o,r]=L(e,i,n),a=r!==e,l=x(t),c=l[r]||{},h=e.startsWith(\".\");if(void 0===o){if(h)for(const i of Object.keys(l))$(t,l,i,e.slice(1));for(const[i,n]of Object.entries(c)){const s=i.replace(w,\"\");a&&!e.includes(s)||D(t,l,r,n.callable,n.delegationSelector)}}else{if(!Object.keys(c).length)return;D(t,l,r,o,s?i:null)}},trigger(t,e,i){if(\"string\"!=typeof e||!t)return null;const n=u();let s=null,o=!0,r=!0,a=!1;e!==I(e)&&n&&(s=n.Event(e,i),n(t).trigger(s),o=!s.isPropagationStopped(),r=!s.isImmediatePropagationStopped(),a=s.isDefaultPrevented());const l=P(new Event(e,{bubbles:o,cancelable:!0}),i);return a&&l.preventDefault(),r&&t.dispatchEvent(l),l.defaultPrevented&&s&&s.preventDefault(),l}};function P(t,e={}){for(const[i,n]of Object.entries(e))try{t[i]=n}catch(e){Object.defineProperty(t,i,{configurable:!0,get:()=>n})}return t}function j(t){if(\"true\"===t)return!0;if(\"false\"===t)return!1;if(t===Number(t).toString())return Number(t);if(\"\"===t||\"null\"===t)return null;if(\"string\"!=typeof t)return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function M(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const F={setDataAttribute(t,e,i){t.setAttribute(`data-bs-${M(e)}`,i)},removeDataAttribute(t,e){t.removeAttribute(`data-bs-${M(e)}`)},getDataAttributes(t){if(!t)return{};const e={},i=Object.keys(t.dataset).filter((t=>t.startsWith(\"bs\")&&!t.startsWith(\"bsConfig\")));for(const n of i){let i=n.replace(/^bs/,\"\");i=i.charAt(0).toLowerCase()+i.slice(1),e[i]=j(t.dataset[n])}return e},getDataAttribute:(t,e)=>j(t.getAttribute(`data-bs-${M(e)}`))};class H{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method \"NAME\", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const i=o(e)?F.getDataAttribute(e,\"config\"):{};return{...this.constructor.Default,...\"object\"==typeof i?i:{},...o(e)?F.getDataAttributes(e):{},...\"object\"==typeof t?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[n,s]of Object.entries(e)){const e=t[n],r=o(e)?\"element\":null==(i=e)?`${i}`:Object.prototype.toString.call(i).match(/\\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(s).test(r))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option \"${n}\" provided type \"${r}\" but expected type \"${s}\".`)}var i}}class W extends H{constructor(t,i){super(),(t=r(t))&&(this._element=t,this._config=this._getConfig(i),e.set(this._element,this.constructor.DATA_KEY,this))}dispose(){e.remove(this._element,this.constructor.DATA_KEY),N.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,i=!0){_(t,e,i)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return e.get(r(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,\"object\"==typeof e?e:null)}static get VERSION(){return\"5.3.5\"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const B=t=>{let e=t.getAttribute(\"data-bs-target\");if(!e||\"#\"===e){let i=t.getAttribute(\"href\");if(!i||!i.includes(\"#\")&&!i.startsWith(\".\"))return null;i.includes(\"#\")&&!i.startsWith(\"#\")&&(i=`#${i.split(\"#\")[1]}`),e=i&&\"#\"!==i?i.trim():null}return e?e.split(\",\").map((t=>n(t))).join(\",\"):null},z={find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const i=[];let n=t.parentNode.closest(e);for(;n;)i.push(n),n=n.parentNode.closest(e);return i},prev(t,e){let i=t.previousElementSibling;for(;i;){if(i.matches(e))return[i];i=i.previousElementSibling}return[]},next(t,e){let i=t.nextElementSibling;for(;i;){if(i.matches(e))return[i];i=i.nextElementSibling}return[]},focusableChildren(t){const e=[\"a\",\"button\",\"input\",\"textarea\",\"select\",\"details\",\"[tabindex]\",'[contenteditable=\"true\"]'].map((t=>`${t}:not([tabindex^=\"-\"])`)).join(\",\");return this.find(e,t).filter((t=>!l(t)&&a(t)))},getSelectorFromElement(t){const e=B(t);return e&&z.findOne(e)?e:null},getElementFromSelector(t){const e=B(t);return e?z.findOne(e):null},getMultipleElementsFromSelector(t){const e=B(t);return e?z.find(e):[]}},R=(t,e=\"hide\")=>{const i=`click.dismiss${t.EVENT_KEY}`,n=t.NAME;N.on(document,i,`[data-bs-dismiss=\"${n}\"]`,(function(i){if([\"A\",\"AREA\"].includes(this.tagName)&&i.preventDefault(),l(this))return;const s=z.getElementFromSelector(this)||this.closest(`.${n}`);t.getOrCreateInstance(s)[e]()}))},q=\".bs.alert\",V=`close${q}`,K=`closed${q}`;class Q extends W{static get NAME(){return\"alert\"}close(){if(N.trigger(this._element,V).defaultPrevented)return;this._element.classList.remove(\"show\");const t=this._element.classList.contains(\"fade\");this._queueCallback((()=>this._destroyElement()),this._element,t)}_destroyElement(){this._element.remove(),N.trigger(this._element,K),this.dispose()}static jQueryInterface(t){return this.each((function(){const e=Q.getOrCreateInstance(this);if(\"string\"==typeof t){if(void 0===e[t]||t.startsWith(\"_\")||\"constructor\"===t)throw new TypeError(`No method named \"${t}\"`);e[t](this)}}))}}R(Q,\"close\"),m(Q);const X='[data-bs-toggle=\"button\"]';class Y extends W{static get NAME(){return\"button\"}toggle(){this._element.setAttribute(\"aria-pressed\",this._element.classList.toggle(\"active\"))}static jQueryInterface(t){return this.each((function(){const e=Y.getOrCreateInstance(this);\"toggle\"===t&&e[t]()}))}}N.on(document,\"click.bs.button.data-api\",X,(t=>{t.preventDefault();const e=t.target.closest(X);Y.getOrCreateInstance(e).toggle()})),m(Y);const U=\".bs.swipe\",G=`touchstart${U}`,J=`touchmove${U}`,Z=`touchend${U}`,tt=`pointerdown${U}`,et=`pointerup${U}`,it={endCallback:null,leftCallback:null,rightCallback:null},nt={endCallback:\"(function|null)\",leftCallback:\"(function|null)\",rightCallback:\"(function|null)\"};class st extends H{constructor(t,e){super(),this._element=t,t&&st.isSupported()&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return it}static get DefaultType(){return nt}static get NAME(){return\"swipe\"}dispose(){N.off(this._element,U)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),g(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=40)return;const e=t/this._deltaX;this._deltaX=0,e&&g(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(N.on(this._element,tt,(t=>this._start(t))),N.on(this._element,et,(t=>this._end(t))),this._element.classList.add(\"pointer-event\")):(N.on(this._element,G,(t=>this._start(t))),N.on(this._element,J,(t=>this._move(t))),N.on(this._element,Z,(t=>this._end(t))))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(\"pen\"===t.pointerType||\"touch\"===t.pointerType)}static isSupported(){return\"ontouchstart\"in document.documentElement||navigator.maxTouchPoints>0}}const ot=\".bs.carousel\",rt=\".data-api\",at=\"ArrowLeft\",lt=\"ArrowRight\",ct=\"next\",ht=\"prev\",dt=\"left\",ut=\"right\",ft=`slide${ot}`,pt=`slid${ot}`,mt=`keydown${ot}`,gt=`mouseenter${ot}`,_t=`mouseleave${ot}`,bt=`dragstart${ot}`,vt=`load${ot}${rt}`,yt=`click${ot}${rt}`,wt=\"carousel\",At=\"active\",Et=\".active\",Tt=\".carousel-item\",Ct=Et+Tt,Ot={[at]:ut,[lt]:dt},xt={interval:5e3,keyboard:!0,pause:\"hover\",ride:!1,touch:!0,wrap:!0},kt={interval:\"(number|boolean)\",keyboard:\"boolean\",pause:\"(string|boolean)\",ride:\"(boolean|string)\",touch:\"boolean\",wrap:\"boolean\"};class Lt extends W{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=z.findOne(\".carousel-indicators\",this._element),this._addEventListeners(),this._config.ride===wt&&this.cycle()}static get Default(){return xt}static get DefaultType(){return kt}static get NAME(){return\"carousel\"}next(){this._slide(ct)}nextWhenVisible(){!document.hidden&&a(this._element)&&this.next()}prev(){this._slide(ht)}pause(){this._isSliding&&s(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?N.one(this._element,pt,(()=>this.cycle())):this.cycle())}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding)return void N.one(this._element,pt,(()=>this.to(t)));const i=this._getItemIndex(this._getActive());if(i===t)return;const n=t>i?ct:ht;this._slide(n,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&N.on(this._element,mt,(t=>this._keydown(t))),\"hover\"===this._config.pause&&(N.on(this._element,gt,(()=>this.pause())),N.on(this._element,_t,(()=>this._maybeEnableCycle()))),this._config.touch&&st.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of z.find(\".carousel-item img\",this._element))N.on(t,bt,(t=>t.preventDefault()));const t={leftCallback:()=>this._slide(this._directionToOrder(dt)),rightCallback:()=>this._slide(this._directionToOrder(ut)),endCallback:()=>{\"hover\"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new st(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=Ot[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=z.findOne(Et,this._indicatorsElement);e.classList.remove(At),e.removeAttribute(\"aria-current\");const i=z.findOne(`[data-bs-slide-to=\"${t}\"]`,this._indicatorsElement);i&&(i.classList.add(At),i.setAttribute(\"aria-current\",\"true\"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute(\"data-bs-interval\"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const i=this._getActive(),n=t===ct,s=e||b(this._getItems(),i,n,this._config.wrap);if(s===i)return;const o=this._getItemIndex(s),r=e=>N.trigger(this._element,e,{relatedTarget:s,direction:this._orderToDirection(t),from:this._getItemIndex(i),to:o});if(r(ft).defaultPrevented)return;if(!i||!s)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=s;const l=n?\"carousel-item-start\":\"carousel-item-end\",c=n?\"carousel-item-next\":\"carousel-item-prev\";s.classList.add(c),d(s),i.classList.add(l),s.classList.add(l),this._queueCallback((()=>{s.classList.remove(l,c),s.classList.add(At),i.classList.remove(At,c,l),this._isSliding=!1,r(pt)}),i,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains(\"slide\")}_getActive(){return z.findOne(Ct,this._element)}_getItems(){return z.find(Tt,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return p()?t===dt?ht:ct:t===dt?ct:ht}_orderToDirection(t){return p()?t===ht?dt:ut:t===ht?ut:dt}static jQueryInterface(t){return this.each((function(){const e=Lt.getOrCreateInstance(this,t);if(\"number\"!=typeof t){if(\"string\"==typeof t){if(void 0===e[t]||t.startsWith(\"_\")||\"constructor\"===t)throw new TypeError(`No method named \"${t}\"`);e[t]()}}else e.to(t)}))}}N.on(document,yt,\"[data-bs-slide], [data-bs-slide-to]\",(function(t){const e=z.getElementFromSelector(this);if(!e||!e.classList.contains(wt))return;t.preventDefault();const i=Lt.getOrCreateInstance(e),n=this.getAttribute(\"data-bs-slide-to\");return n?(i.to(n),void i._maybeEnableCycle()):\"next\"===F.getDataAttribute(this,\"slide\")?(i.next(),void i._maybeEnableCycle()):(i.prev(),void i._maybeEnableCycle())})),N.on(window,vt,(()=>{const t=z.find('[data-bs-ride=\"carousel\"]');for(const e of t)Lt.getOrCreateInstance(e)})),m(Lt);const St=\".bs.collapse\",Dt=`show${St}`,$t=`shown${St}`,It=`hide${St}`,Nt=`hidden${St}`,Pt=`click${St}.data-api`,jt=\"show\",Mt=\"collapse\",Ft=\"collapsing\",Ht=`:scope .${Mt} .${Mt}`,Wt='[data-bs-toggle=\"collapse\"]',Bt={parent:null,toggle:!0},zt={parent:\"(null|element)\",toggle:\"boolean\"};class Rt extends W{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const i=z.find(Wt);for(const t of i){const e=z.getSelectorFromElement(t),i=z.find(e).filter((t=>t===this._element));null!==e&&i.length&&this._triggerArray.push(t)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Bt}static get DefaultType(){return zt}static get NAME(){return\"collapse\"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(\".collapse.show, .collapse.collapsing\").filter((t=>t!==this._element)).map((t=>Rt.getOrCreateInstance(t,{toggle:!1})))),t.length&&t[0]._isTransitioning)return;if(N.trigger(this._element,Dt).defaultPrevented)return;for(const e of t)e.hide();const e=this._getDimension();this._element.classList.remove(Mt),this._element.classList.add(Ft),this._element.style[e]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const i=`scroll${e[0].toUpperCase()+e.slice(1)}`;this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(Ft),this._element.classList.add(Mt,jt),this._element.style[e]=\"\",N.trigger(this._element,$t)}),this._element,!0),this._element.style[e]=`${this._element[i]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(N.trigger(this._element,It).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,d(this._element),this._element.classList.add(Ft),this._element.classList.remove(Mt,jt);for(const t of this._triggerArray){const e=z.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[t]=\"\",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(Ft),this._element.classList.add(Mt),N.trigger(this._element,Nt)}),this._element,!0)}_isShown(t=this._element){return t.classList.contains(jt)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=r(t.parent),t}_getDimension(){return this._element.classList.contains(\"collapse-horizontal\")?\"width\":\"height\"}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(Wt);for(const e of t){const t=z.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(t){const e=z.find(Ht,this._config.parent);return z.find(t,this._config.parent).filter((t=>!e.includes(t)))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const i of t)i.classList.toggle(\"collapsed\",!e),i.setAttribute(\"aria-expanded\",e)}static jQueryInterface(t){const e={};return\"string\"==typeof t&&/show|hide/.test(t)&&(e.toggle=!1),this.each((function(){const i=Rt.getOrCreateInstance(this,e);if(\"string\"==typeof t){if(void 0===i[t])throw new TypeError(`No method named \"${t}\"`);i[t]()}}))}}N.on(document,Pt,Wt,(function(t){(\"A\"===t.target.tagName||t.delegateTarget&&\"A\"===t.delegateTarget.tagName)&&t.preventDefault();for(const t of z.getMultipleElementsFromSelector(this))Rt.getOrCreateInstance(t,{toggle:!1}).toggle()})),m(Rt);var qt=\"top\",Vt=\"bottom\",Kt=\"right\",Qt=\"left\",Xt=\"auto\",Yt=[qt,Vt,Kt,Qt],Ut=\"start\",Gt=\"end\",Jt=\"clippingParents\",Zt=\"viewport\",te=\"popper\",ee=\"reference\",ie=Yt.reduce((function(t,e){return t.concat([e+\"-\"+Ut,e+\"-\"+Gt])}),[]),ne=[].concat(Yt,[Xt]).reduce((function(t,e){return t.concat([e,e+\"-\"+Ut,e+\"-\"+Gt])}),[]),se=\"beforeRead\",oe=\"read\",re=\"afterRead\",ae=\"beforeMain\",le=\"main\",ce=\"afterMain\",he=\"beforeWrite\",de=\"write\",ue=\"afterWrite\",fe=[se,oe,re,ae,le,ce,he,de,ue];function pe(t){return t?(t.nodeName||\"\").toLowerCase():null}function me(t){if(null==t)return window;if(\"[object Window]\"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function ge(t){return t instanceof me(t).Element||t instanceof Element}function _e(t){return t instanceof me(t).HTMLElement||t instanceof HTMLElement}function be(t){return\"undefined\"!=typeof ShadowRoot&&(t instanceof me(t).ShadowRoot||t instanceof ShadowRoot)}const ve={name:\"applyStyles\",enabled:!0,phase:\"write\",fn:function(t){var e=t.state;Object.keys(e.elements).forEach((function(t){var i=e.styles[t]||{},n=e.attributes[t]||{},s=e.elements[t];_e(s)&&pe(s)&&(Object.assign(s.style,i),Object.keys(n).forEach((function(t){var e=n[t];!1===e?s.removeAttribute(t):s.setAttribute(t,!0===e?\"\":e)})))}))},effect:function(t){var e=t.state,i={popper:{position:e.options.strategy,left:\"0\",top:\"0\",margin:\"0\"},arrow:{position:\"absolute\"},reference:{}};return Object.assign(e.elements.popper.style,i.popper),e.styles=i,e.elements.arrow&&Object.assign(e.elements.arrow.style,i.arrow),function(){Object.keys(e.elements).forEach((function(t){var n=e.elements[t],s=e.attributes[t]||{},o=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:i[t]).reduce((function(t,e){return t[e]=\"\",t}),{});_e(n)&&pe(n)&&(Object.assign(n.style,o),Object.keys(s).forEach((function(t){n.removeAttribute(t)})))}))}},requires:[\"computeStyles\"]};function ye(t){return t.split(\"-\")[0]}var we=Math.max,Ae=Math.min,Ee=Math.round;function Te(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map((function(t){return t.brand+\"/\"+t.version})).join(\" \"):navigator.userAgent}function Ce(){return!/^((?!chrome|android).)*safari/i.test(Te())}function Oe(t,e,i){void 0===e&&(e=!1),void 0===i&&(i=!1);var n=t.getBoundingClientRect(),s=1,o=1;e&&_e(t)&&(s=t.offsetWidth>0&&Ee(n.width)/t.offsetWidth||1,o=t.offsetHeight>0&&Ee(n.height)/t.offsetHeight||1);var r=(ge(t)?me(t):window).visualViewport,a=!Ce()&&i,l=(n.left+(a&&r?r.offsetLeft:0))/s,c=(n.top+(a&&r?r.offsetTop:0))/o,h=n.width/s,d=n.height/o;return{width:h,height:d,top:c,right:l+h,bottom:c+d,left:l,x:l,y:c}}function xe(t){var e=Oe(t),i=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-i)<=1&&(i=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:i,height:n}}function ke(t,e){var i=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(i&&be(i)){var n=e;do{if(n&&t.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function Le(t){return me(t).getComputedStyle(t)}function Se(t){return[\"table\",\"td\",\"th\"].indexOf(pe(t))>=0}function De(t){return((ge(t)?t.ownerDocument:t.document)||window.document).documentElement}function $e(t){return\"html\"===pe(t)?t:t.assignedSlot||t.parentNode||(be(t)?t.host:null)||De(t)}function Ie(t){return _e(t)&&\"fixed\"!==Le(t).position?t.offsetParent:null}function Ne(t){for(var e=me(t),i=Ie(t);i&&Se(i)&&\"static\"===Le(i).position;)i=Ie(i);return i&&(\"html\"===pe(i)||\"body\"===pe(i)&&\"static\"===Le(i).position)?e:i||function(t){var e=/firefox/i.test(Te());if(/Trident/i.test(Te())&&_e(t)&&\"fixed\"===Le(t).position)return null;var i=$e(t);for(be(i)&&(i=i.host);_e(i)&&[\"html\",\"body\"].indexOf(pe(i))<0;){var n=Le(i);if(\"none\"!==n.transform||\"none\"!==n.perspective||\"paint\"===n.contain||-1!==[\"transform\",\"perspective\"].indexOf(n.willChange)||e&&\"filter\"===n.willChange||e&&n.filter&&\"none\"!==n.filter)return i;i=i.parentNode}return null}(t)||e}function Pe(t){return[\"top\",\"bottom\"].indexOf(t)>=0?\"x\":\"y\"}function je(t,e,i){return we(t,Ae(e,i))}function Me(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function Fe(t,e){return e.reduce((function(e,i){return e[i]=t,e}),{})}const He={name:\"arrow\",enabled:!0,phase:\"main\",fn:function(t){var e,i=t.state,n=t.name,s=t.options,o=i.elements.arrow,r=i.modifiersData.popperOffsets,a=ye(i.placement),l=Pe(a),c=[Qt,Kt].indexOf(a)>=0?\"height\":\"width\";if(o&&r){var h=function(t,e){return Me(\"number\"!=typeof(t=\"function\"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:Fe(t,Yt))}(s.padding,i),d=xe(o),u=\"y\"===l?qt:Qt,f=\"y\"===l?Vt:Kt,p=i.rects.reference[c]+i.rects.reference[l]-r[l]-i.rects.popper[c],m=r[l]-i.rects.reference[l],g=Ne(o),_=g?\"y\"===l?g.clientHeight||0:g.clientWidth||0:0,b=p/2-m/2,v=h[u],y=_-d[c]-h[f],w=_/2-d[c]/2+b,A=je(v,w,y),E=l;i.modifiersData[n]=((e={})[E]=A,e.centerOffset=A-w,e)}},effect:function(t){var e=t.state,i=t.options.element,n=void 0===i?\"[data-popper-arrow]\":i;null!=n&&(\"string\"!=typeof n||(n=e.elements.popper.querySelector(n)))&&ke(e.elements.popper,n)&&(e.elements.arrow=n)},requires:[\"popperOffsets\"],requiresIfExists:[\"preventOverflow\"]};function We(t){return t.split(\"-\")[1]}var Be={top:\"auto\",right:\"auto\",bottom:\"auto\",left:\"auto\"};function ze(t){var e,i=t.popper,n=t.popperRect,s=t.placement,o=t.variation,r=t.offsets,a=t.position,l=t.gpuAcceleration,c=t.adaptive,h=t.roundOffsets,d=t.isFixed,u=r.x,f=void 0===u?0:u,p=r.y,m=void 0===p?0:p,g=\"function\"==typeof h?h({x:f,y:m}):{x:f,y:m};f=g.x,m=g.y;var _=r.hasOwnProperty(\"x\"),b=r.hasOwnProperty(\"y\"),v=Qt,y=qt,w=window;if(c){var A=Ne(i),E=\"clientHeight\",T=\"clientWidth\";A===me(i)&&\"static\"!==Le(A=De(i)).position&&\"absolute\"===a&&(E=\"scrollHeight\",T=\"scrollWidth\"),(s===qt||(s===Qt||s===Kt)&&o===Gt)&&(y=Vt,m-=(d&&A===w&&w.visualViewport?w.visualViewport.height:A[E])-n.height,m*=l?1:-1),s!==Qt&&(s!==qt&&s!==Vt||o!==Gt)||(v=Kt,f-=(d&&A===w&&w.visualViewport?w.visualViewport.width:A[T])-n.width,f*=l?1:-1)}var C,O=Object.assign({position:a},c&&Be),x=!0===h?function(t,e){var i=t.x,n=t.y,s=e.devicePixelRatio||1;return{x:Ee(i*s)/s||0,y:Ee(n*s)/s||0}}({x:f,y:m},me(i)):{x:f,y:m};return f=x.x,m=x.y,l?Object.assign({},O,((C={})[y]=b?\"0\":\"\",C[v]=_?\"0\":\"\",C.transform=(w.devicePixelRatio||1)<=1?\"translate(\"+f+\"px, \"+m+\"px)\":\"translate3d(\"+f+\"px, \"+m+\"px, 0)\",C)):Object.assign({},O,((e={})[y]=b?m+\"px\":\"\",e[v]=_?f+\"px\":\"\",e.transform=\"\",e))}const Re={name:\"computeStyles\",enabled:!0,phase:\"beforeWrite\",fn:function(t){var e=t.state,i=t.options,n=i.gpuAcceleration,s=void 0===n||n,o=i.adaptive,r=void 0===o||o,a=i.roundOffsets,l=void 0===a||a,c={placement:ye(e.placement),variation:We(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:s,isFixed:\"fixed\"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,ze(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:r,roundOffsets:l})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,ze(Object.assign({},c,{offsets:e.modifiersData.arrow,position:\"absolute\",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{\"data-popper-placement\":e.placement})},data:{}};var qe={passive:!0};const Ve={name:\"eventListeners\",enabled:!0,phase:\"write\",fn:function(){},effect:function(t){var e=t.state,i=t.instance,n=t.options,s=n.scroll,o=void 0===s||s,r=n.resize,a=void 0===r||r,l=me(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&c.forEach((function(t){t.addEventListener(\"scroll\",i.update,qe)})),a&&l.addEventListener(\"resize\",i.update,qe),function(){o&&c.forEach((function(t){t.removeEventListener(\"scroll\",i.update,qe)})),a&&l.removeEventListener(\"resize\",i.update,qe)}},data:{}};var Ke={left:\"right\",right:\"left\",bottom:\"top\",top:\"bottom\"};function Qe(t){return t.replace(/left|right|bottom|top/g,(function(t){return Ke[t]}))}var Xe={start:\"end\",end:\"start\"};function Ye(t){return t.replace(/start|end/g,(function(t){return Xe[t]}))}function Ue(t){var e=me(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Ge(t){return Oe(De(t)).left+Ue(t).scrollLeft}function Je(t){var e=Le(t),i=e.overflow,n=e.overflowX,s=e.overflowY;return/auto|scroll|overlay|hidden/.test(i+s+n)}function Ze(t){return[\"html\",\"body\",\"#document\"].indexOf(pe(t))>=0?t.ownerDocument.body:_e(t)&&Je(t)?t:Ze($e(t))}function ti(t,e){var i;void 0===e&&(e=[]);var n=Ze(t),s=n===(null==(i=t.ownerDocument)?void 0:i.body),o=me(n),r=s?[o].concat(o.visualViewport||[],Je(n)?n:[]):n,a=e.concat(r);return s?a:a.concat(ti($e(r)))}function ei(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function ii(t,e,i){return e===Zt?ei(function(t,e){var i=me(t),n=De(t),s=i.visualViewport,o=n.clientWidth,r=n.clientHeight,a=0,l=0;if(s){o=s.width,r=s.height;var c=Ce();(c||!c&&\"fixed\"===e)&&(a=s.offsetLeft,l=s.offsetTop)}return{width:o,height:r,x:a+Ge(t),y:l}}(t,i)):ge(e)?function(t,e){var i=Oe(t,!1,\"fixed\"===e);return i.top=i.top+t.clientTop,i.left=i.left+t.clientLeft,i.bottom=i.top+t.clientHeight,i.right=i.left+t.clientWidth,i.width=t.clientWidth,i.height=t.clientHeight,i.x=i.left,i.y=i.top,i}(e,i):ei(function(t){var e,i=De(t),n=Ue(t),s=null==(e=t.ownerDocument)?void 0:e.body,o=we(i.scrollWidth,i.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),r=we(i.scrollHeight,i.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),a=-n.scrollLeft+Ge(t),l=-n.scrollTop;return\"rtl\"===Le(s||i).direction&&(a+=we(i.clientWidth,s?s.clientWidth:0)-o),{width:o,height:r,x:a,y:l}}(De(t)))}function ni(t){var e,i=t.reference,n=t.element,s=t.placement,o=s?ye(s):null,r=s?We(s):null,a=i.x+i.width/2-n.width/2,l=i.y+i.height/2-n.height/2;switch(o){case qt:e={x:a,y:i.y-n.height};break;case Vt:e={x:a,y:i.y+i.height};break;case Kt:e={x:i.x+i.width,y:l};break;case Qt:e={x:i.x-n.width,y:l};break;default:e={x:i.x,y:i.y}}var c=o?Pe(o):null;if(null!=c){var h=\"y\"===c?\"height\":\"width\";switch(r){case Ut:e[c]=e[c]-(i[h]/2-n[h]/2);break;case Gt:e[c]=e[c]+(i[h]/2-n[h]/2)}}return e}function si(t,e){void 0===e&&(e={});var i=e,n=i.placement,s=void 0===n?t.placement:n,o=i.strategy,r=void 0===o?t.strategy:o,a=i.boundary,l=void 0===a?Jt:a,c=i.rootBoundary,h=void 0===c?Zt:c,d=i.elementContext,u=void 0===d?te:d,f=i.altBoundary,p=void 0!==f&&f,m=i.padding,g=void 0===m?0:m,_=Me(\"number\"!=typeof g?g:Fe(g,Yt)),b=u===te?ee:te,v=t.rects.popper,y=t.elements[p?b:u],w=function(t,e,i,n){var s=\"clippingParents\"===e?function(t){var e=ti($e(t)),i=[\"absolute\",\"fixed\"].indexOf(Le(t).position)>=0&&_e(t)?Ne(t):t;return ge(i)?e.filter((function(t){return ge(t)&&ke(t,i)&&\"body\"!==pe(t)})):[]}(t):[].concat(e),o=[].concat(s,[i]),r=o[0],a=o.reduce((function(e,i){var s=ii(t,i,n);return e.top=we(s.top,e.top),e.right=Ae(s.right,e.right),e.bottom=Ae(s.bottom,e.bottom),e.left=we(s.left,e.left),e}),ii(t,r,n));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}(ge(y)?y:y.contextElement||De(t.elements.popper),l,h,r),A=Oe(t.elements.reference),E=ni({reference:A,element:v,placement:s}),T=ei(Object.assign({},v,E)),C=u===te?T:A,O={top:w.top-C.top+_.top,bottom:C.bottom-w.bottom+_.bottom,left:w.left-C.left+_.left,right:C.right-w.right+_.right},x=t.modifiersData.offset;if(u===te&&x){var k=x[s];Object.keys(O).forEach((function(t){var e=[Kt,Vt].indexOf(t)>=0?1:-1,i=[qt,Vt].indexOf(t)>=0?\"y\":\"x\";O[t]+=k[i]*e}))}return O}function oi(t,e){void 0===e&&(e={});var i=e,n=i.placement,s=i.boundary,o=i.rootBoundary,r=i.padding,a=i.flipVariations,l=i.allowedAutoPlacements,c=void 0===l?ne:l,h=We(n),d=h?a?ie:ie.filter((function(t){return We(t)===h})):Yt,u=d.filter((function(t){return c.indexOf(t)>=0}));0===u.length&&(u=d);var f=u.reduce((function(e,i){return e[i]=si(t,{placement:i,boundary:s,rootBoundary:o,padding:r})[ye(i)],e}),{});return Object.keys(f).sort((function(t,e){return f[t]-f[e]}))}const ri={name:\"flip\",enabled:!0,phase:\"main\",fn:function(t){var e=t.state,i=t.options,n=t.name;if(!e.modifiersData[n]._skip){for(var s=i.mainAxis,o=void 0===s||s,r=i.altAxis,a=void 0===r||r,l=i.fallbackPlacements,c=i.padding,h=i.boundary,d=i.rootBoundary,u=i.altBoundary,f=i.flipVariations,p=void 0===f||f,m=i.allowedAutoPlacements,g=e.options.placement,_=ye(g),b=l||(_!==g&&p?function(t){if(ye(t)===Xt)return[];var e=Qe(t);return[Ye(t),e,Ye(e)]}(g):[Qe(g)]),v=[g].concat(b).reduce((function(t,i){return t.concat(ye(i)===Xt?oi(e,{placement:i,boundary:h,rootBoundary:d,padding:c,flipVariations:p,allowedAutoPlacements:m}):i)}),[]),y=e.rects.reference,w=e.rects.popper,A=new Map,E=!0,T=v[0],C=0;C<v.length;C++){var O=v[C],x=ye(O),k=We(O)===Ut,L=[qt,Vt].indexOf(x)>=0,S=L?\"width\":\"height\",D=si(e,{placement:O,boundary:h,rootBoundary:d,altBoundary:u,padding:c}),$=L?k?Kt:Qt:k?Vt:qt;y[S]>w[S]&&($=Qe($));var I=Qe($),N=[];if(o&&N.push(D[x]<=0),a&&N.push(D[$]<=0,D[I]<=0),N.every((function(t){return t}))){T=O,E=!1;break}A.set(O,N)}if(E)for(var P=function(t){var e=v.find((function(e){var i=A.get(e);if(i)return i.slice(0,t).every((function(t){return t}))}));if(e)return T=e,\"break\"},j=p?3:1;j>0&&\"break\"!==P(j);j--);e.placement!==T&&(e.modifiersData[n]._skip=!0,e.placement=T,e.reset=!0)}},requiresIfExists:[\"offset\"],data:{_skip:!1}};function ai(t,e,i){return void 0===i&&(i={x:0,y:0}),{top:t.top-e.height-i.y,right:t.right-e.width+i.x,bottom:t.bottom-e.height+i.y,left:t.left-e.width-i.x}}function li(t){return[qt,Kt,Vt,Qt].some((function(e){return t[e]>=0}))}const ci={name:\"hide\",enabled:!0,phase:\"main\",requiresIfExists:[\"preventOverflow\"],fn:function(t){var e=t.state,i=t.name,n=e.rects.reference,s=e.rects.popper,o=e.modifiersData.preventOverflow,r=si(e,{elementContext:\"reference\"}),a=si(e,{altBoundary:!0}),l=ai(r,n),c=ai(a,s,o),h=li(l),d=li(c);e.modifiersData[i]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:h,hasPopperEscaped:d},e.attributes.popper=Object.assign({},e.attributes.popper,{\"data-popper-reference-hidden\":h,\"data-popper-escaped\":d})}},hi={name:\"offset\",enabled:!0,phase:\"main\",requires:[\"popperOffsets\"],fn:function(t){var e=t.state,i=t.options,n=t.name,s=i.offset,o=void 0===s?[0,0]:s,r=ne.reduce((function(t,i){return t[i]=function(t,e,i){var n=ye(t),s=[Qt,qt].indexOf(n)>=0?-1:1,o=\"function\"==typeof i?i(Object.assign({},e,{placement:t})):i,r=o[0],a=o[1];return r=r||0,a=(a||0)*s,[Qt,Kt].indexOf(n)>=0?{x:a,y:r}:{x:r,y:a}}(i,e.rects,o),t}),{}),a=r[e.placement],l=a.x,c=a.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=l,e.modifiersData.popperOffsets.y+=c),e.modifiersData[n]=r}},di={name:\"popperOffsets\",enabled:!0,phase:\"read\",fn:function(t){var e=t.state,i=t.name;e.modifiersData[i]=ni({reference:e.rects.reference,element:e.rects.popper,placement:e.placement})},data:{}},ui={name:\"preventOverflow\",enabled:!0,phase:\"main\",fn:function(t){var e=t.state,i=t.options,n=t.name,s=i.mainAxis,o=void 0===s||s,r=i.altAxis,a=void 0!==r&&r,l=i.boundary,c=i.rootBoundary,h=i.altBoundary,d=i.padding,u=i.tether,f=void 0===u||u,p=i.tetherOffset,m=void 0===p?0:p,g=si(e,{boundary:l,rootBoundary:c,padding:d,altBoundary:h}),_=ye(e.placement),b=We(e.placement),v=!b,y=Pe(_),w=\"x\"===y?\"y\":\"x\",A=e.modifiersData.popperOffsets,E=e.rects.reference,T=e.rects.popper,C=\"function\"==typeof m?m(Object.assign({},e.rects,{placement:e.placement})):m,O=\"number\"==typeof C?{mainAxis:C,altAxis:C}:Object.assign({mainAxis:0,altAxis:0},C),x=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,k={x:0,y:0};if(A){if(o){var L,S=\"y\"===y?qt:Qt,D=\"y\"===y?Vt:Kt,$=\"y\"===y?\"height\":\"width\",I=A[y],N=I+g[S],P=I-g[D],j=f?-T[$]/2:0,M=b===Ut?E[$]:T[$],F=b===Ut?-T[$]:-E[$],H=e.elements.arrow,W=f&&H?xe(H):{width:0,height:0},B=e.modifiersData[\"arrow#persistent\"]?e.modifiersData[\"arrow#persistent\"].padding:{top:0,right:0,bottom:0,left:0},z=B[S],R=B[D],q=je(0,E[$],W[$]),V=v?E[$]/2-j-q-z-O.mainAxis:M-q-z-O.mainAxis,K=v?-E[$]/2+j+q+R+O.mainAxis:F+q+R+O.mainAxis,Q=e.elements.arrow&&Ne(e.elements.arrow),X=Q?\"y\"===y?Q.clientTop||0:Q.clientLeft||0:0,Y=null!=(L=null==x?void 0:x[y])?L:0,U=I+K-Y,G=je(f?Ae(N,I+V-Y-X):N,I,f?we(P,U):P);A[y]=G,k[y]=G-I}if(a){var J,Z=\"x\"===y?qt:Qt,tt=\"x\"===y?Vt:Kt,et=A[w],it=\"y\"===w?\"height\":\"width\",nt=et+g[Z],st=et-g[tt],ot=-1!==[qt,Qt].indexOf(_),rt=null!=(J=null==x?void 0:x[w])?J:0,at=ot?nt:et-E[it]-T[it]-rt+O.altAxis,lt=ot?et+E[it]+T[it]-rt-O.altAxis:st,ct=f&&ot?function(t,e,i){var n=je(t,e,i);return n>i?i:n}(at,et,lt):je(f?at:nt,et,f?lt:st);A[w]=ct,k[w]=ct-et}e.modifiersData[n]=k}},requiresIfExists:[\"offset\"]};function fi(t,e,i){void 0===i&&(i=!1);var n,s,o=_e(e),r=_e(e)&&function(t){var e=t.getBoundingClientRect(),i=Ee(e.width)/t.offsetWidth||1,n=Ee(e.height)/t.offsetHeight||1;return 1!==i||1!==n}(e),a=De(e),l=Oe(t,r,i),c={scrollLeft:0,scrollTop:0},h={x:0,y:0};return(o||!o&&!i)&&((\"body\"!==pe(e)||Je(a))&&(c=(n=e)!==me(n)&&_e(n)?{scrollLeft:(s=n).scrollLeft,scrollTop:s.scrollTop}:Ue(n)),_e(e)?((h=Oe(e,!0)).x+=e.clientLeft,h.y+=e.clientTop):a&&(h.x=Ge(a))),{x:l.left+c.scrollLeft-h.x,y:l.top+c.scrollTop-h.y,width:l.width,height:l.height}}function pi(t){var e=new Map,i=new Set,n=[];function s(t){i.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!i.has(t)){var n=e.get(t);n&&s(n)}})),n.push(t)}return t.forEach((function(t){e.set(t.name,t)})),t.forEach((function(t){i.has(t.name)||s(t)})),n}var mi={placement:\"bottom\",modifiers:[],strategy:\"absolute\"};function gi(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return!e.some((function(t){return!(t&&\"function\"==typeof t.getBoundingClientRect)}))}function _i(t){void 0===t&&(t={});var e=t,i=e.defaultModifiers,n=void 0===i?[]:i,s=e.defaultOptions,o=void 0===s?mi:s;return function(t,e,i){void 0===i&&(i=o);var s,r,a={placement:\"bottom\",orderedModifiers:[],options:Object.assign({},mi,o),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},l=[],c=!1,h={state:a,setOptions:function(i){var s=\"function\"==typeof i?i(a.options):i;d(),a.options=Object.assign({},o,a.options,s),a.scrollParents={reference:ge(t)?ti(t):t.contextElement?ti(t.contextElement):[],popper:ti(e)};var r,c,u=function(t){var e=pi(t);return fe.reduce((function(t,i){return t.concat(e.filter((function(t){return t.phase===i})))}),[])}((r=[].concat(n,a.options.modifiers),c=r.reduce((function(t,e){var i=t[e.name];return t[e.name]=i?Object.assign({},i,e,{options:Object.assign({},i.options,e.options),data:Object.assign({},i.data,e.data)}):e,t}),{}),Object.keys(c).map((function(t){return c[t]}))));return a.orderedModifiers=u.filter((function(t){return t.enabled})),a.orderedModifiers.forEach((function(t){var e=t.name,i=t.options,n=void 0===i?{}:i,s=t.effect;if(\"function\"==typeof s){var o=s({state:a,name:e,instance:h,options:n});l.push(o||function(){})}})),h.update()},forceUpdate:function(){if(!c){var t=a.elements,e=t.reference,i=t.popper;if(gi(e,i)){a.rects={reference:fi(e,Ne(i),\"fixed\"===a.options.strategy),popper:xe(i)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(t){return a.modifiersData[t.name]=Object.assign({},t.data)}));for(var n=0;n<a.orderedModifiers.length;n++)if(!0!==a.reset){var s=a.orderedModifiers[n],o=s.fn,r=s.options,l=void 0===r?{}:r,d=s.name;\"function\"==typeof o&&(a=o({state:a,options:l,name:d,instance:h})||a)}else a.reset=!1,n=-1}}},update:(s=function(){return new Promise((function(t){h.forceUpdate(),t(a)}))},function(){return r||(r=new Promise((function(t){Promise.resolve().then((function(){r=void 0,t(s())}))}))),r}),destroy:function(){d(),c=!0}};if(!gi(t,e))return h;function d(){l.forEach((function(t){return t()})),l=[]}return h.setOptions(i).then((function(t){!c&&i.onFirstUpdate&&i.onFirstUpdate(t)})),h}}var bi=_i(),vi=_i({defaultModifiers:[Ve,di,Re,ve]}),yi=_i({defaultModifiers:[Ve,di,Re,ve,hi,ri,ui,He,ci]});const wi=Object.freeze(Object.defineProperty({__proto__:null,afterMain:ce,afterRead:re,afterWrite:ue,applyStyles:ve,arrow:He,auto:Xt,basePlacements:Yt,beforeMain:ae,beforeRead:se,beforeWrite:he,bottom:Vt,clippingParents:Jt,computeStyles:Re,createPopper:yi,createPopperBase:bi,createPopperLite:vi,detectOverflow:si,end:Gt,eventListeners:Ve,flip:ri,hide:ci,left:Qt,main:le,modifierPhases:fe,offset:hi,placements:ne,popper:te,popperGenerator:_i,popperOffsets:di,preventOverflow:ui,read:oe,reference:ee,right:Kt,start:Ut,top:qt,variationPlacements:ie,viewport:Zt,write:de},Symbol.toStringTag,{value:\"Module\"})),Ai=\"dropdown\",Ei=\".bs.dropdown\",Ti=\".data-api\",Ci=\"ArrowUp\",Oi=\"ArrowDown\",xi=`hide${Ei}`,ki=`hidden${Ei}`,Li=`show${Ei}`,Si=`shown${Ei}`,Di=`click${Ei}${Ti}`,$i=`keydown${Ei}${Ti}`,Ii=`keyup${Ei}${Ti}`,Ni=\"show\",Pi='[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)',ji=`${Pi}.${Ni}`,Mi=\".dropdown-menu\",Fi=p()?\"top-end\":\"top-start\",Hi=p()?\"top-start\":\"top-end\",Wi=p()?\"bottom-end\":\"bottom-start\",Bi=p()?\"bottom-start\":\"bottom-end\",zi=p()?\"left-start\":\"right-start\",Ri=p()?\"right-start\":\"left-start\",qi={autoClose:!0,boundary:\"clippingParents\",display:\"dynamic\",offset:[0,2],popperConfig:null,reference:\"toggle\"},Vi={autoClose:\"(boolean|string)\",boundary:\"(string|element)\",display:\"string\",offset:\"(array|string|function)\",popperConfig:\"(null|object|function)\",reference:\"(string|element|object)\"};class Ki extends W{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=z.next(this._element,Mi)[0]||z.prev(this._element,Mi)[0]||z.findOne(Mi,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return qi}static get DefaultType(){return Vi}static get NAME(){return Ai}toggle(){return this._isShown()?this.hide():this.show()}show(){if(l(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!N.trigger(this._element,Li,t).defaultPrevented){if(this._createPopper(),\"ontouchstart\"in document.documentElement&&!this._parent.closest(\".navbar-nav\"))for(const t of[].concat(...document.body.children))N.on(t,\"mouseover\",h);this._element.focus(),this._element.setAttribute(\"aria-expanded\",!0),this._menu.classList.add(Ni),this._element.classList.add(Ni),N.trigger(this._element,Si,t)}}hide(){if(l(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!N.trigger(this._element,xi,t).defaultPrevented){if(\"ontouchstart\"in document.documentElement)for(const t of[].concat(...document.body.children))N.off(t,\"mouseover\",h);this._popper&&this._popper.destroy(),this._menu.classList.remove(Ni),this._element.classList.remove(Ni),this._element.setAttribute(\"aria-expanded\",\"false\"),F.removeDataAttribute(this._menu,\"popper\"),N.trigger(this._element,ki,t)}}_getConfig(t){if(\"object\"==typeof(t=super._getConfig(t)).reference&&!o(t.reference)&&\"function\"!=typeof t.reference.getBoundingClientRect)throw new TypeError(`${Ai.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`);return t}_createPopper(){if(void 0===wi)throw new TypeError(\"Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)\");let t=this._element;\"parent\"===this._config.reference?t=this._parent:o(this._config.reference)?t=r(this._config.reference):\"object\"==typeof this._config.reference&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=yi(t,this._menu,e)}_isShown(){return this._menu.classList.contains(Ni)}_getPlacement(){const t=this._parent;if(t.classList.contains(\"dropend\"))return zi;if(t.classList.contains(\"dropstart\"))return Ri;if(t.classList.contains(\"dropup-center\"))return\"top\";if(t.classList.contains(\"dropdown-center\"))return\"bottom\";const e=\"end\"===getComputedStyle(this._menu).getPropertyValue(\"--bs-position\").trim();return t.classList.contains(\"dropup\")?e?Hi:Fi:e?Bi:Wi}_detectNavbar(){return null!==this._element.closest(\".navbar\")}_getOffset(){const{offset:t}=this._config;return\"string\"==typeof t?t.split(\",\").map((t=>Number.parseInt(t,10))):\"function\"==typeof t?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:\"preventOverflow\",options:{boundary:this._config.boundary}},{name:\"offset\",options:{offset:this._getOffset()}}]};return(this._inNavbar||\"static\"===this._config.display)&&(F.setDataAttribute(this._menu,\"popper\",\"static\"),t.modifiers=[{name:\"applyStyles\",enabled:!1}]),{...t,...g(this._config.popperConfig,[void 0,t])}}_selectMenuItem({key:t,target:e}){const i=z.find(\".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)\",this._menu).filter((t=>a(t)));i.length&&b(i,e,t===Oi,!i.includes(e)).focus()}static jQueryInterface(t){return this.each((function(){const e=Ki.getOrCreateInstance(this,t);if(\"string\"==typeof t){if(void 0===e[t])throw new TypeError(`No method named \"${t}\"`);e[t]()}}))}static clearMenus(t){if(2===t.button||\"keyup\"===t.type&&\"Tab\"!==t.key)return;const e=z.find(ji);for(const i of e){const e=Ki.getInstance(i);if(!e||!1===e._config.autoClose)continue;const n=t.composedPath(),s=n.includes(e._menu);if(n.includes(e._element)||\"inside\"===e._config.autoClose&&!s||\"outside\"===e._config.autoClose&&s)continue;if(e._menu.contains(t.target)&&(\"keyup\"===t.type&&\"Tab\"===t.key||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const o={relatedTarget:e._element};\"click\"===t.type&&(o.clickEvent=t),e._completeHide(o)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),i=\"Escape\"===t.key,n=[Ci,Oi].includes(t.key);if(!n&&!i)return;if(e&&!i)return;t.preventDefault();const s=this.matches(Pi)?this:z.prev(this,Pi)[0]||z.next(this,Pi)[0]||z.findOne(Pi,t.delegateTarget.parentNode),o=Ki.getOrCreateInstance(s);if(n)return t.stopPropagation(),o.show(),void o._selectMenuItem(t);o._isShown()&&(t.stopPropagation(),o.hide(),s.focus())}}N.on(document,$i,Pi,Ki.dataApiKeydownHandler),N.on(document,$i,Mi,Ki.dataApiKeydownHandler),N.on(document,Di,Ki.clearMenus),N.on(document,Ii,Ki.clearMenus),N.on(document,Di,Pi,(function(t){t.preventDefault(),Ki.getOrCreateInstance(this).toggle()})),m(Ki);const Qi=\"backdrop\",Xi=\"show\",Yi=`mousedown.bs.${Qi}`,Ui={className:\"modal-backdrop\",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:\"body\"},Gi={className:\"string\",clickCallback:\"(function|null)\",isAnimated:\"boolean\",isVisible:\"boolean\",rootElement:\"(element|string)\"};class Ji extends H{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return Ui}static get DefaultType(){return Gi}static get NAME(){return Qi}show(t){if(!this._config.isVisible)return void g(t);this._append();const e=this._getElement();this._config.isAnimated&&d(e),e.classList.add(Xi),this._emulateAnimation((()=>{g(t)}))}hide(t){this._config.isVisible?(this._getElement().classList.remove(Xi),this._emulateAnimation((()=>{this.dispose(),g(t)}))):g(t)}dispose(){this._isAppended&&(N.off(this._element,Yi),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement(\"div\");t.className=this._config.className,this._config.isAnimated&&t.classList.add(\"fade\"),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=r(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),N.on(t,Yi,(()=>{g(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(t){_(t,this._getElement(),this._config.isAnimated)}}const Zi=\".bs.focustrap\",tn=`focusin${Zi}`,en=`keydown.tab${Zi}`,nn=\"backward\",sn={autofocus:!0,trapElement:null},on={autofocus:\"boolean\",trapElement:\"element\"};class rn extends H{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return sn}static get DefaultType(){return on}static get NAME(){return\"focustrap\"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),N.off(document,Zi),N.on(document,tn,(t=>this._handleFocusin(t))),N.on(document,en,(t=>this._handleKeydown(t))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,N.off(document,Zi))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const i=z.focusableChildren(e);0===i.length?e.focus():this._lastTabNavDirection===nn?i[i.length-1].focus():i[0].focus()}_handleKeydown(t){\"Tab\"===t.key&&(this._lastTabNavDirection=t.shiftKey?nn:\"forward\")}}const an=\".fixed-top, .fixed-bottom, .is-fixed, .sticky-top\",ln=\".sticky-top\",cn=\"padding-right\",hn=\"margin-right\";class dn{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,cn,(e=>e+t)),this._setElementAttributes(an,cn,(e=>e+t)),this._setElementAttributes(ln,hn,(e=>e-t))}reset(){this._resetElementAttributes(this._element,\"overflow\"),this._resetElementAttributes(this._element,cn),this._resetElementAttributes(an,cn),this._resetElementAttributes(ln,hn)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,\"overflow\"),this._element.style.overflow=\"hidden\"}_setElementAttributes(t,e,i){const n=this.getWidth();this._applyManipulationCallback(t,(t=>{if(t!==this._element&&window.innerWidth>t.clientWidth+n)return;this._saveInitialAttribute(t,e);const s=window.getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,`${i(Number.parseFloat(s))}px`)}))}_saveInitialAttribute(t,e){const i=t.style.getPropertyValue(e);i&&F.setDataAttribute(t,e,i)}_resetElementAttributes(t,e){this._applyManipulationCallback(t,(t=>{const i=F.getDataAttribute(t,e);null!==i?(F.removeDataAttribute(t,e),t.style.setProperty(e,i)):t.style.removeProperty(e)}))}_applyManipulationCallback(t,e){if(o(t))e(t);else for(const i of z.find(t,this._element))e(i)}}const un=\".bs.modal\",fn=`hide${un}`,pn=`hidePrevented${un}`,mn=`hidden${un}`,gn=`show${un}`,_n=`shown${un}`,bn=`resize${un}`,vn=`click.dismiss${un}`,yn=`mousedown.dismiss${un}`,wn=`keydown.dismiss${un}`,An=`click${un}.data-api`,En=\"modal-open\",Tn=\"show\",Cn=\"modal-static\",On={backdrop:!0,focus:!0,keyboard:!0},xn={backdrop:\"(boolean|string)\",focus:\"boolean\",keyboard:\"boolean\"};class kn extends W{constructor(t,e){super(t,e),this._dialog=z.findOne(\".modal-dialog\",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new dn,this._addEventListeners()}static get Default(){return On}static get DefaultType(){return xn}static get NAME(){return\"modal\"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||N.trigger(this._element,gn,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(En),this._adjustDialog(),this._backdrop.show((()=>this._showElement(t))))}hide(){this._isShown&&!this._isTransitioning&&(N.trigger(this._element,fn).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Tn),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated())))}dispose(){N.off(window,un),N.off(this._dialog,un),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Ji({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new rn({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display=\"block\",this._element.removeAttribute(\"aria-hidden\"),this._element.setAttribute(\"aria-modal\",!0),this._element.setAttribute(\"role\",\"dialog\"),this._element.scrollTop=0;const e=z.findOne(\".modal-body\",this._dialog);e&&(e.scrollTop=0),d(this._element),this._element.classList.add(Tn),this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,N.trigger(this._element,_n,{relatedTarget:t})}),this._dialog,this._isAnimated())}_addEventListeners(){N.on(this._element,wn,(t=>{\"Escape\"===t.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),N.on(window,bn,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),N.on(this._element,yn,(t=>{N.one(this._element,vn,(e=>{this._element===t.target&&this._element===e.target&&(\"static\"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display=\"none\",this._element.setAttribute(\"aria-hidden\",!0),this._element.removeAttribute(\"aria-modal\"),this._element.removeAttribute(\"role\"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(En),this._resetAdjustments(),this._scrollBar.reset(),N.trigger(this._element,mn)}))}_isAnimated(){return this._element.classList.contains(\"fade\")}_triggerBackdropTransition(){if(N.trigger(this._element,pn).defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;\"hidden\"===e||this._element.classList.contains(Cn)||(t||(this._element.style.overflowY=\"hidden\"),this._element.classList.add(Cn),this._queueCallback((()=>{this._element.classList.remove(Cn),this._queueCallback((()=>{this._element.style.overflowY=e}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),i=e>0;if(i&&!t){const t=p()?\"paddingLeft\":\"paddingRight\";this._element.style[t]=`${e}px`}if(!i&&t){const t=p()?\"paddingRight\":\"paddingLeft\";this._element.style[t]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft=\"\",this._element.style.paddingRight=\"\"}static jQueryInterface(t,e){return this.each((function(){const i=kn.getOrCreateInstance(this,t);if(\"string\"==typeof t){if(void 0===i[t])throw new TypeError(`No method named \"${t}\"`);i[t](e)}}))}}N.on(document,An,'[data-bs-toggle=\"modal\"]',(function(t){const e=z.getElementFromSelector(this);[\"A\",\"AREA\"].includes(this.tagName)&&t.preventDefault(),N.one(e,gn,(t=>{t.defaultPrevented||N.one(e,mn,(()=>{a(this)&&this.focus()}))}));const i=z.findOne(\".modal.show\");i&&kn.getInstance(i).hide(),kn.getOrCreateInstance(e).toggle(this)})),R(kn),m(kn);const Ln=\".bs.offcanvas\",Sn=\".data-api\",Dn=`load${Ln}${Sn}`,$n=\"show\",In=\"showing\",Nn=\"hiding\",Pn=\".offcanvas.show\",jn=`show${Ln}`,Mn=`shown${Ln}`,Fn=`hide${Ln}`,Hn=`hidePrevented${Ln}`,Wn=`hidden${Ln}`,Bn=`resize${Ln}`,zn=`click${Ln}${Sn}`,Rn=`keydown.dismiss${Ln}`,qn={backdrop:!0,keyboard:!0,scroll:!1},Vn={backdrop:\"(boolean|string)\",keyboard:\"boolean\",scroll:\"boolean\"};class Kn extends W{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return qn}static get DefaultType(){return Vn}static get NAME(){return\"offcanvas\"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||N.trigger(this._element,jn,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new dn).hide(),this._element.setAttribute(\"aria-modal\",!0),this._element.setAttribute(\"role\",\"dialog\"),this._element.classList.add(In),this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add($n),this._element.classList.remove(In),N.trigger(this._element,Mn,{relatedTarget:t})}),this._element,!0))}hide(){this._isShown&&(N.trigger(this._element,Fn).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Nn),this._backdrop.hide(),this._queueCallback((()=>{this._element.classList.remove($n,Nn),this._element.removeAttribute(\"aria-modal\"),this._element.removeAttribute(\"role\"),this._config.scroll||(new dn).reset(),N.trigger(this._element,Wn)}),this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=Boolean(this._config.backdrop);return new Ji({className:\"offcanvas-backdrop\",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{\"static\"!==this._config.backdrop?this.hide():N.trigger(this._element,Hn)}:null})}_initializeFocusTrap(){return new rn({trapElement:this._element})}_addEventListeners(){N.on(this._element,Rn,(t=>{\"Escape\"===t.key&&(this._config.keyboard?this.hide():N.trigger(this._element,Hn))}))}static jQueryInterface(t){return this.each((function(){const e=Kn.getOrCreateInstance(this,t);if(\"string\"==typeof t){if(void 0===e[t]||t.startsWith(\"_\")||\"constructor\"===t)throw new TypeError(`No method named \"${t}\"`);e[t](this)}}))}}N.on(document,zn,'[data-bs-toggle=\"offcanvas\"]',(function(t){const e=z.getElementFromSelector(this);if([\"A\",\"AREA\"].includes(this.tagName)&&t.preventDefault(),l(this))return;N.one(e,Wn,(()=>{a(this)&&this.focus()}));const i=z.findOne(Pn);i&&i!==e&&Kn.getInstance(i).hide(),Kn.getOrCreateInstance(e).toggle(this)})),N.on(window,Dn,(()=>{for(const t of z.find(Pn))Kn.getOrCreateInstance(t).show()})),N.on(window,Bn,(()=>{for(const t of z.find(\"[aria-modal][class*=show][class*=offcanvas-]\"))\"fixed\"!==getComputedStyle(t).position&&Kn.getOrCreateInstance(t).hide()})),R(Kn),m(Kn);const Qn={\"*\":[\"class\",\"dir\",\"id\",\"lang\",\"role\",/^aria-[\\w-]*$/i],a:[\"target\",\"href\",\"title\",\"rel\"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:[\"src\",\"srcset\",\"alt\",\"title\",\"width\",\"height\"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Xn=new Set([\"background\",\"cite\",\"href\",\"itemtype\",\"longdesc\",\"poster\",\"src\",\"xlink:href\"]),Yn=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Un=(t,e)=>{const i=t.nodeName.toLowerCase();return e.includes(i)?!Xn.has(i)||Boolean(Yn.test(t.nodeValue)):e.filter((t=>t instanceof RegExp)).some((t=>t.test(i)))},Gn={allowList:Qn,content:{},extraClass:\"\",html:!1,sanitize:!0,sanitizeFn:null,template:\"<div></div>\"},Jn={allowList:\"object\",content:\"object\",extraClass:\"(string|function)\",html:\"boolean\",sanitize:\"boolean\",sanitizeFn:\"(null|function)\",template:\"string\"},Zn={entry:\"(string|element|function|null)\",selector:\"(string|element)\"};class ts extends H{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return Gn}static get DefaultType(){return Jn}static get NAME(){return\"TemplateFactory\"}getContent(){return Object.values(this._config.content).map((t=>this._resolvePossibleFunction(t))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement(\"div\");t.innerHTML=this._maybeSanitize(this._config.template);for(const[e,i]of Object.entries(this._config.content))this._setContent(t,i,e);const e=t.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&e.classList.add(...i.split(\" \")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,i]of Object.entries(t))super._typeCheckConfig({selector:e,entry:i},Zn)}_setContent(t,e,i){const n=z.findOne(i,t);n&&((e=this._resolvePossibleFunction(e))?o(e)?this._putElementInTemplate(r(e),n):this._config.html?n.innerHTML=this._maybeSanitize(e):n.textContent=e:n.remove())}_maybeSanitize(t){return this._config.sanitize?function(t,e,i){if(!t.length)return t;if(i&&\"function\"==typeof i)return i(t);const n=(new window.DOMParser).parseFromString(t,\"text/html\"),s=[].concat(...n.body.querySelectorAll(\"*\"));for(const t of s){const i=t.nodeName.toLowerCase();if(!Object.keys(e).includes(i)){t.remove();continue}const n=[].concat(...t.attributes),s=[].concat(e[\"*\"]||[],e[i]||[]);for(const e of n)Un(e,s)||t.removeAttribute(e.nodeName)}return n.body.innerHTML}(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return g(t,[void 0,this])}_putElementInTemplate(t,e){if(this._config.html)return e.innerHTML=\"\",void e.append(t);e.textContent=t.textContent}}const es=new Set([\"sanitize\",\"allowList\",\"sanitizeFn\"]),is=\"fade\",ns=\"show\",ss=\".tooltip-inner\",os=\".modal\",rs=\"hide.bs.modal\",as=\"hover\",ls=\"focus\",cs={AUTO:\"auto\",TOP:\"top\",RIGHT:p()?\"left\":\"right\",BOTTOM:\"bottom\",LEFT:p()?\"right\":\"left\"},hs={allowList:Qn,animation:!0,boundary:\"clippingParents\",container:!1,customClass:\"\",delay:0,fallbackPlacements:[\"top\",\"right\",\"bottom\",\"left\"],html:!1,offset:[0,6],placement:\"top\",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class=\"tooltip\" role=\"tooltip\"><div class=\"tooltip-arrow\"></div><div class=\"tooltip-inner\"></div></div>',title:\"\",trigger:\"hover focus\"},ds={allowList:\"object\",animation:\"boolean\",boundary:\"(string|element)\",container:\"(string|element|boolean)\",customClass:\"(string|function)\",delay:\"(number|object)\",fallbackPlacements:\"array\",html:\"boolean\",offset:\"(array|string|function)\",placement:\"(string|function)\",popperConfig:\"(null|object|function)\",sanitize:\"boolean\",sanitizeFn:\"(null|function)\",selector:\"(string|boolean)\",template:\"string\",title:\"(string|element|function)\",trigger:\"string\"};class us extends W{constructor(t,e){if(void 0===wi)throw new TypeError(\"Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)\");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return hs}static get DefaultType(){return ds}static get NAME(){return\"tooltip\"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),N.off(this._element.closest(os),rs,this._hideModalHandler),this._element.getAttribute(\"data-bs-original-title\")&&this._element.setAttribute(\"title\",this._element.getAttribute(\"data-bs-original-title\")),this._disposePopper(),super.dispose()}show(){if(\"none\"===this._element.style.display)throw new Error(\"Please use show on visible elements\");if(!this._isWithContent()||!this._isEnabled)return;const t=N.trigger(this._element,this.constructor.eventName(\"show\")),e=(c(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();const i=this._getTipElement();this._element.setAttribute(\"aria-describedby\",i.getAttribute(\"id\"));const{container:n}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(n.append(i),N.trigger(this._element,this.constructor.eventName(\"inserted\"))),this._popper=this._createPopper(i),i.classList.add(ns),\"ontouchstart\"in document.documentElement)for(const t of[].concat(...document.body.children))N.on(t,\"mouseover\",h);this._queueCallback((()=>{N.trigger(this._element,this.constructor.eventName(\"shown\")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(this._isShown()&&!N.trigger(this._element,this.constructor.eventName(\"hide\")).defaultPrevented){if(this._getTipElement().classList.remove(ns),\"ontouchstart\"in document.documentElement)for(const t of[].concat(...document.body.children))N.off(t,\"mouseover\",h);this._activeTrigger.click=!1,this._activeTrigger[ls]=!1,this._activeTrigger[as]=!1,this._isHovered=null,this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute(\"aria-describedby\"),N.trigger(this._element,this.constructor.eventName(\"hidden\")))}),this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(is,ns),e.classList.add(`bs-${this.constructor.NAME}-auto`);const i=(t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t})(this.constructor.NAME).toString();return e.setAttribute(\"id\",i),this._isAnimated()&&e.classList.add(is),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new ts({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[ss]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute(\"data-bs-original-title\")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(is)}_isShown(){return this.tip&&this.tip.classList.contains(ns)}_createPopper(t){const e=g(this._config.placement,[this,t,this._element]),i=cs[e.toUpperCase()];return yi(this._element,t,this._getPopperConfig(i))}_getOffset(){const{offset:t}=this._config;return\"string\"==typeof t?t.split(\",\").map((t=>Number.parseInt(t,10))):\"function\"==typeof t?e=>t(e,this._element):t}_resolvePossibleFunction(t){return g(t,[this._element,this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:\"flip\",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:\"offset\",options:{offset:this._getOffset()}},{name:\"preventOverflow\",options:{boundary:this._config.boundary}},{name:\"arrow\",options:{element:`.${this.constructor.NAME}-arrow`}},{name:\"preSetPlacement\",enabled:!0,phase:\"beforeMain\",fn:t=>{this._getTipElement().setAttribute(\"data-popper-placement\",t.state.placement)}}]};return{...e,...g(this._config.popperConfig,[void 0,e])}}_setListeners(){const t=this._config.trigger.split(\" \");for(const e of t)if(\"click\"===e)N.on(this._element,this.constructor.eventName(\"click\"),this._config.selector,(t=>{this._initializeOnDelegatedTarget(t).toggle()}));else if(\"manual\"!==e){const t=e===as?this.constructor.eventName(\"mouseenter\"):this.constructor.eventName(\"focusin\"),i=e===as?this.constructor.eventName(\"mouseleave\"):this.constructor.eventName(\"focusout\");N.on(this._element,t,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger[\"focusin\"===t.type?ls:as]=!0,e._enter()})),N.on(this._element,i,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger[\"focusout\"===t.type?ls:as]=e._element.contains(t.relatedTarget),e._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},N.on(this._element.closest(os),rs,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute(\"title\");t&&(this._element.getAttribute(\"aria-label\")||this._element.textContent.trim()||this._element.setAttribute(\"aria-label\",t),this._element.setAttribute(\"data-bs-original-title\",t),this._element.removeAttribute(\"title\"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=F.getDataAttributes(this._element);for(const t of Object.keys(e))es.has(t)&&delete e[t];return t={...e,...\"object\"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:r(t.container),\"number\"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),\"number\"==typeof t.title&&(t.title=t.title.toString()),\"number\"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,i]of Object.entries(this._config))this.constructor.Default[e]!==i&&(t[e]=i);return t.selector=!1,t.trigger=\"manual\",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each((function(){const e=us.getOrCreateInstance(this,t);if(\"string\"==typeof t){if(void 0===e[t])throw new TypeError(`No method named \"${t}\"`);e[t]()}}))}}m(us);const fs=\".popover-header\",ps=\".popover-body\",ms={...us.Default,content:\"\",offset:[0,8],placement:\"right\",template:'<div class=\"popover\" role=\"tooltip\"><div class=\"popover-arrow\"></div><h3 class=\"popover-header\"></h3><div class=\"popover-body\"></div></div>',trigger:\"click\"},gs={...us.DefaultType,content:\"(null|string|element|function)\"};class _s extends us{static get Default(){return ms}static get DefaultType(){return gs}static get NAME(){return\"popover\"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[fs]:this._getTitle(),[ps]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each((function(){const e=_s.getOrCreateInstance(this,t);if(\"string\"==typeof t){if(void 0===e[t])throw new TypeError(`No method named \"${t}\"`);e[t]()}}))}}m(_s);const bs=\".bs.scrollspy\",vs=`activate${bs}`,ys=`click${bs}`,ws=`load${bs}.data-api`,As=\"active\",Es=\"[href]\",Ts=\".nav-link\",Cs=`${Ts}, .nav-item > ${Ts}, .list-group-item`,Os={offset:null,rootMargin:\"0px 0px -25%\",smoothScroll:!1,target:null,threshold:[.1,.5,1]},xs={offset:\"(number|null)\",rootMargin:\"string\",smoothScroll:\"boolean\",target:\"element\",threshold:\"array\"};class ks extends W{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=\"visible\"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Os}static get DefaultType(){return xs}static get NAME(){return\"scrollspy\"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=r(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,\"string\"==typeof t.threshold&&(t.threshold=t.threshold.split(\",\").map((t=>Number.parseFloat(t)))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(N.off(this._config.target,ys),N.on(this._config.target,ys,Es,(t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const i=this._rootElement||window,n=e.offsetTop-this._element.offsetTop;if(i.scrollTo)return void i.scrollTo({top:n,behavior:\"smooth\"});i.scrollTop=n}})))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((t=>this._observerCallback(t)),t)}_observerCallback(t){const e=t=>this._targetLinks.get(`#${t.target.id}`),i=t=>{this._previousScrollData.visibleEntryTop=t.target.offsetTop,this._process(e(t))},n=(this._rootElement||document.documentElement).scrollTop,s=n>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=n;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const t=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(s&&t){if(i(o),!n)return}else s||t||i(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=z.find(Es,this._config.target);for(const e of t){if(!e.hash||l(e))continue;const t=z.findOne(decodeURI(e.hash),this._element);a(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(As),this._activateParents(t),N.trigger(this._element,vs,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains(\"dropdown-item\"))z.findOne(\".dropdown-toggle\",t.closest(\".dropdown\")).classList.add(As);else for(const e of z.parents(t,\".nav, .list-group\"))for(const t of z.prev(e,Cs))t.classList.add(As)}_clearActiveClass(t){t.classList.remove(As);const e=z.find(`${Es}.${As}`,t);for(const t of e)t.classList.remove(As)}static jQueryInterface(t){return this.each((function(){const e=ks.getOrCreateInstance(this,t);if(\"string\"==typeof t){if(void 0===e[t]||t.startsWith(\"_\")||\"constructor\"===t)throw new TypeError(`No method named \"${t}\"`);e[t]()}}))}}N.on(window,ws,(()=>{for(const t of z.find('[data-bs-spy=\"scroll\"]'))ks.getOrCreateInstance(t)})),m(ks);const Ls=\".bs.tab\",Ss=`hide${Ls}`,Ds=`hidden${Ls}`,$s=`show${Ls}`,Is=`shown${Ls}`,Ns=`click${Ls}`,Ps=`keydown${Ls}`,js=`load${Ls}`,Ms=\"ArrowLeft\",Fs=\"ArrowRight\",Hs=\"ArrowUp\",Ws=\"ArrowDown\",Bs=\"Home\",zs=\"End\",Rs=\"active\",qs=\"fade\",Vs=\"show\",Ks=\".dropdown-toggle\",Qs=`:not(${Ks})`,Xs='[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]',Ys=`.nav-link${Qs}, .list-group-item${Qs}, [role=\"tab\"]${Qs}, ${Xs}`,Us=`.${Rs}[data-bs-toggle=\"tab\"], .${Rs}[data-bs-toggle=\"pill\"], .${Rs}[data-bs-toggle=\"list\"]`;class Gs extends W{constructor(t){super(t),this._parent=this._element.closest('.list-group, .nav, [role=\"tablist\"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),N.on(this._element,Ps,(t=>this._keydown(t))))}static get NAME(){return\"tab\"}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),i=e?N.trigger(e,Ss,{relatedTarget:t}):null;N.trigger(t,$s,{relatedTarget:e}).defaultPrevented||i&&i.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){t&&(t.classList.add(Rs),this._activate(z.getElementFromSelector(t)),this._queueCallback((()=>{\"tab\"===t.getAttribute(\"role\")?(t.removeAttribute(\"tabindex\"),t.setAttribute(\"aria-selected\",!0),this._toggleDropDown(t,!0),N.trigger(t,Is,{relatedTarget:e})):t.classList.add(Vs)}),t,t.classList.contains(qs)))}_deactivate(t,e){t&&(t.classList.remove(Rs),t.blur(),this._deactivate(z.getElementFromSelector(t)),this._queueCallback((()=>{\"tab\"===t.getAttribute(\"role\")?(t.setAttribute(\"aria-selected\",!1),t.setAttribute(\"tabindex\",\"-1\"),this._toggleDropDown(t,!1),N.trigger(t,Ds,{relatedTarget:e})):t.classList.remove(Vs)}),t,t.classList.contains(qs)))}_keydown(t){if(![Ms,Fs,Hs,Ws,Bs,zs].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter((t=>!l(t)));let i;if([Bs,zs].includes(t.key))i=e[t.key===Bs?0:e.length-1];else{const n=[Fs,Ws].includes(t.key);i=b(e,t.target,n,!0)}i&&(i.focus({preventScroll:!0}),Gs.getOrCreateInstance(i).show())}_getChildren(){return z.find(Ys,this._parent)}_getActiveElem(){return this._getChildren().find((t=>this._elemIsActive(t)))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,\"role\",\"tablist\");for(const t of e)this._setInitialAttributesOnChild(t)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),i=this._getOuterElement(t);t.setAttribute(\"aria-selected\",e),i!==t&&this._setAttributeIfNotExists(i,\"role\",\"presentation\"),e||t.setAttribute(\"tabindex\",\"-1\"),this._setAttributeIfNotExists(t,\"role\",\"tab\"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=z.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,\"role\",\"tabpanel\"),t.id&&this._setAttributeIfNotExists(e,\"aria-labelledby\",`${t.id}`))}_toggleDropDown(t,e){const i=this._getOuterElement(t);if(!i.classList.contains(\"dropdown\"))return;const n=(t,n)=>{const s=z.findOne(t,i);s&&s.classList.toggle(n,e)};n(Ks,Rs),n(\".dropdown-menu\",Vs),i.setAttribute(\"aria-expanded\",e)}_setAttributeIfNotExists(t,e,i){t.hasAttribute(e)||t.setAttribute(e,i)}_elemIsActive(t){return t.classList.contains(Rs)}_getInnerElement(t){return t.matches(Ys)?t:z.findOne(Ys,t)}_getOuterElement(t){return t.closest(\".nav-item, .list-group-item\")||t}static jQueryInterface(t){return this.each((function(){const e=Gs.getOrCreateInstance(this);if(\"string\"==typeof t){if(void 0===e[t]||t.startsWith(\"_\")||\"constructor\"===t)throw new TypeError(`No method named \"${t}\"`);e[t]()}}))}}N.on(document,Ns,Xs,(function(t){[\"A\",\"AREA\"].includes(this.tagName)&&t.preventDefault(),l(this)||Gs.getOrCreateInstance(this).show()})),N.on(window,js,(()=>{for(const t of z.find(Us))Gs.getOrCreateInstance(t)})),m(Gs);const Js=\".bs.toast\",Zs=`mouseover${Js}`,to=`mouseout${Js}`,eo=`focusin${Js}`,io=`focusout${Js}`,no=`hide${Js}`,so=`hidden${Js}`,oo=`show${Js}`,ro=`shown${Js}`,ao=\"hide\",lo=\"show\",co=\"showing\",ho={animation:\"boolean\",autohide:\"boolean\",delay:\"number\"},uo={animation:!0,autohide:!0,delay:5e3};class fo extends W{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return uo}static get DefaultType(){return ho}static get NAME(){return\"toast\"}show(){N.trigger(this._element,oo).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add(\"fade\"),this._element.classList.remove(ao),d(this._element),this._element.classList.add(lo,co),this._queueCallback((()=>{this._element.classList.remove(co),N.trigger(this._element,ro),this._maybeScheduleHide()}),this._element,this._config.animation))}hide(){this.isShown()&&(N.trigger(this._element,no).defaultPrevented||(this._element.classList.add(co),this._queueCallback((()=>{this._element.classList.add(ao),this._element.classList.remove(co,lo),N.trigger(this._element,so)}),this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(lo),super.dispose()}isShown(){return this._element.classList.contains(lo)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(t,e){switch(t.type){case\"mouseover\":case\"mouseout\":this._hasMouseInteraction=e;break;case\"focusin\":case\"focusout\":this._hasKeyboardInteraction=e}if(e)return void this._clearTimeout();const i=t.relatedTarget;this._element===i||this._element.contains(i)||this._maybeScheduleHide()}_setListeners(){N.on(this._element,Zs,(t=>this._onInteraction(t,!0))),N.on(this._element,to,(t=>this._onInteraction(t,!1))),N.on(this._element,eo,(t=>this._onInteraction(t,!0))),N.on(this._element,io,(t=>this._onInteraction(t,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each((function(){const e=fo.getOrCreateInstance(this,t);if(\"string\"==typeof t){if(void 0===e[t])throw new TypeError(`No method named \"${t}\"`);e[t](this)}}))}}return R(fo),m(fo),{Alert:Q,Button:Y,Carousel:Lt,Collapse:Rt,Dropdown:Ki,Modal:kn,Offcanvas:Kn,Popover:_s,ScrollSpy:ks,Tab:Gs,Toast:fo,Tooltip:us}}));\n//# sourceMappingURL=bootstrap.bundle.min.js.map"], "mappings": "AAAA;AACA,CAAC,SAAS,GAAE,GAAE;AAAC;AAAa,cAAU,OAAO,UAAQ,YAAU,OAAO,OAAO,UAAQ,OAAO,UAAQ,EAAE,WAAS,EAAE,GAAE,IAAE,IAAE,SAASA,IAAE;AAAC,QAAG,CAACA,GAAE,SAAS,OAAM,IAAI,MAAM,0CAA0C;AAAE,WAAO,EAAEA,EAAC;AAAA,EAAC,IAAE,EAAE,CAAC;AAAC,EAAE,eAAa,OAAO,SAAO,SAAO,MAAK,SAAS,IAAG,GAAE;AAAC;AAAa,MAAI,KAAG,CAAC,GAAE,IAAE,OAAO,gBAAe,KAAG,GAAG,OAAM,IAAE,GAAG,OAAK,SAASA,IAAE;AAAC,WAAO,GAAG,KAAK,KAAKA,EAAC;AAAA,EAAC,IAAE,SAASA,IAAE;AAAC,WAAO,GAAG,OAAO,MAAM,CAAC,GAAEA,EAAC;AAAA,EAAC,GAAE,IAAE,GAAG,MAAK,KAAG,GAAG,SAAQ,IAAE,CAAC,GAAE,IAAE,EAAE,UAAS,KAAG,EAAE,gBAAe,IAAE,GAAG,UAAS,IAAE,EAAE,KAAK,MAAM,GAAE,KAAG,CAAC,GAAE,IAAE,SAASA,IAAE;AAAC,WAAM,cAAY,OAAOA,MAAG,YAAU,OAAOA,GAAE,YAAU,cAAY,OAAOA,GAAE;AAAA,EAAI,GAAE,IAAE,SAASA,IAAE;AAAC,WAAO,QAAMA,MAAGA,OAAIA,GAAE;AAAA,EAAM,GAAE,IAAE,GAAG,UAAS,IAAE,EAAC,MAAK,MAAG,KAAI,MAAG,OAAM,MAAG,UAAS,KAAE;AAAE,WAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,MAAGH,KAAEA,MAAG,GAAG,cAAc,QAAQ;AAAE,QAAGG,GAAE,OAAKL,IAAEC,GAAE,MAAIE,MAAK,EAAE,EAACC,KAAEH,GAAEE,EAAC,KAAGF,GAAE,gBAAcA,GAAE,aAAaE,EAAC,MAAIE,GAAE,aAAaF,IAAEC,EAAC;AAAE,IAAAF,GAAE,KAAK,YAAYG,EAAC,EAAE,WAAW,YAAYA,EAAC;AAAA,EAAC;AAAC,WAAS,EAAEL,IAAE;AAAC,WAAO,QAAMA,KAAEA,KAAE,KAAG,YAAU,OAAOA,MAAG,cAAY,OAAOA,KAAE,EAAE,EAAE,KAAKA,EAAC,CAAC,KAAG,WAAS,OAAOA;AAAA,EAAC;AAAC,MAAI,IAAE,SAAQ,IAAE,UAAS,KAAG,SAASA,IAAEC,IAAE;AAAC,WAAO,IAAI,GAAG,GAAG,KAAKD,IAAEC,EAAC;AAAA,EAAC;AAAE,WAAS,EAAED,IAAE;AAAC,QAAIC,KAAE,CAAC,CAACD,MAAG,YAAWA,MAAGA,GAAE,QAAOE,KAAE,EAAEF,EAAC;AAAE,WAAM,CAAC,EAAEA,EAAC,KAAG,CAAC,EAAEA,EAAC,MAAI,YAAUE,MAAG,MAAID,MAAG,YAAU,OAAOA,MAAG,IAAEA,MAAGA,KAAE,KAAKD;AAAA,EAAE;AAAC,WAAS,GAAGA,IAAEC,IAAE;AAAC,WAAOD,GAAE,YAAUA,GAAE,SAAS,YAAY,MAAIC,GAAE,YAAY;AAAA,EAAC;AAAC,KAAG,KAAG,GAAG,YAAU,EAAC,QAAO,GAAE,aAAY,IAAG,QAAO,GAAE,SAAQ,WAAU;AAAC,WAAO,GAAG,KAAK,IAAI;AAAA,EAAC,GAAE,KAAI,SAASD,IAAE;AAAC,WAAO,QAAMA,KAAE,GAAG,KAAK,IAAI,IAAEA,KAAE,IAAE,KAAKA,KAAE,KAAK,MAAM,IAAE,KAAKA,EAAC;AAAA,EAAC,GAAE,WAAU,SAASA,IAAE;AAAC,QAAIC,KAAE,GAAG,MAAM,KAAK,YAAY,GAAED,EAAC;AAAE,WAAOC,GAAE,aAAW,MAAKA;AAAA,EAAC,GAAE,MAAK,SAASD,IAAE;AAAC,WAAO,GAAG,KAAK,MAAKA,EAAC;AAAA,EAAC,GAAE,KAAI,SAASE,IAAE;AAAC,WAAO,KAAK,UAAU,GAAG,IAAI,MAAK,SAASF,IAAEC,IAAE;AAAC,aAAOC,GAAE,KAAKF,IAAEC,IAAED,EAAC;AAAA,IAAC,CAAC,CAAC;AAAA,EAAC,GAAE,OAAM,WAAU;AAAC,WAAO,KAAK,UAAU,GAAG,MAAM,MAAK,SAAS,CAAC;AAAA,EAAC,GAAE,OAAM,WAAU;AAAC,WAAO,KAAK,GAAG,CAAC;AAAA,EAAC,GAAE,MAAK,WAAU;AAAC,WAAO,KAAK,GAAG,EAAE;AAAA,EAAC,GAAE,MAAK,WAAU;AAAC,WAAO,KAAK,UAAU,GAAG,KAAK,MAAK,SAASA,IAAEC,IAAE;AAAC,cAAOA,KAAE,KAAG;AAAA,IAAC,CAAC,CAAC;AAAA,EAAC,GAAE,KAAI,WAAU;AAAC,WAAO,KAAK,UAAU,GAAG,KAAK,MAAK,SAASD,IAAEC,IAAE;AAAC,aAAOA,KAAE;AAAA,IAAC,CAAC,CAAC;AAAA,EAAC,GAAE,IAAG,SAASD,IAAE;AAAC,QAAIC,KAAE,KAAK,QAAOC,KAAE,CAACF,MAAGA,KAAE,IAAEC,KAAE;AAAG,WAAO,KAAK,UAAU,KAAGC,MAAGA,KAAED,KAAE,CAAC,KAAKC,EAAC,CAAC,IAAE,CAAC,CAAC;AAAA,EAAC,GAAE,KAAI,WAAU;AAAC,WAAO,KAAK,cAAY,KAAK,YAAY;AAAA,EAAC,GAAE,MAAK,GAAE,MAAK,GAAG,MAAK,QAAO,GAAG,OAAM,GAAE,GAAG,SAAO,GAAG,GAAG,SAAO,WAAU;AAAC,QAAIF,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,UAAU,CAAC,KAAG,CAAC,GAAEC,KAAE,GAAEC,KAAE,UAAU,QAAOC,KAAE;AAAG,SAAI,aAAW,OAAOH,OAAIG,KAAEH,IAAEA,KAAE,UAAUC,EAAC,KAAG,CAAC,GAAEA,OAAK,YAAU,OAAOD,MAAG,EAAEA,EAAC,MAAIA,KAAE,CAAC,IAAGC,OAAIC,OAAIF,KAAE,MAAKC,OAAKA,KAAEC,IAAED,KAAI,KAAG,SAAOP,KAAE,UAAUO,EAAC,GAAG,MAAIN,MAAKD,GAAE,CAAAG,KAAEH,GAAEC,EAAC,GAAE,gBAAcA,MAAGK,OAAIH,OAAIM,MAAGN,OAAI,GAAG,cAAcA,EAAC,MAAIC,KAAE,MAAM,QAAQD,EAAC,OAAKD,KAAEI,GAAEL,EAAC,GAAEI,KAAED,MAAG,CAAC,MAAM,QAAQF,EAAC,IAAE,CAAC,IAAEE,MAAG,GAAG,cAAcF,EAAC,IAAEA,KAAE,CAAC,GAAEE,KAAE,OAAGE,GAAEL,EAAC,IAAE,GAAG,OAAOQ,IAAEJ,IAAEF,EAAC,KAAG,WAASA,OAAIG,GAAEL,EAAC,IAAEE;AAAI,WAAOG;AAAA,EAAC,GAAE,GAAG,OAAO,EAAC,SAAQ,YAAU,IAAE,KAAK,OAAO,GAAG,QAAQ,OAAM,EAAE,GAAE,SAAQ,MAAG,OAAM,SAASN,IAAE;AAAC,UAAM,IAAI,MAAMA,EAAC;AAAA,EAAC,GAAE,MAAK,WAAU;AAAA,EAAC,GAAE,eAAc,SAASA,IAAE;AAAC,QAAIC,IAAEC;AAAE,WAAM,EAAE,CAACF,MAAG,sBAAoB,EAAE,KAAKA,EAAC,OAAK,EAAEC,KAAE,EAAED,EAAC,MAAI,cAAY,QAAOE,KAAE,GAAG,KAAKD,IAAE,aAAa,KAAGA,GAAE,gBAAc,EAAE,KAAKC,EAAC,MAAI;AAAA,EAAE,GAAE,eAAc,SAASF,IAAE;AAAC,QAAIC;AAAE,SAAIA,MAAKD,GAAE,QAAM;AAAG,WAAM;AAAA,EAAE,GAAE,YAAW,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAEF,IAAE,EAAC,OAAMC,MAAGA,GAAE,MAAK,GAAEC,EAAC;AAAA,EAAC,GAAE,MAAK,SAASF,IAAEC,IAAE;AAAC,QAAIC,IAAEC,KAAE;AAAE,QAAG,EAAEH,EAAC,GAAE;AAAC,WAAIE,KAAEF,GAAE,QAAOG,KAAED,IAAEC,KAAI,KAAG,UAAKF,GAAE,KAAKD,GAAEG,EAAC,GAAEA,IAAEH,GAAEG,EAAC,CAAC,EAAE;AAAA,IAAK,MAAM,MAAIA,MAAKH,GAAE,KAAG,UAAKC,GAAE,KAAKD,GAAEG,EAAC,GAAEA,IAAEH,GAAEG,EAAC,CAAC,EAAE;AAAM,WAAOH;AAAA,EAAC,GAAE,MAAK,SAASA,IAAE;AAAC,QAAIC,IAAEC,KAAE,IAAGC,KAAE,GAAEC,KAAEJ,GAAE;AAAS,QAAG,CAACI,GAAE,QAAMH,KAAED,GAAEG,IAAG,EAAE,CAAAD,MAAG,GAAG,KAAKD,EAAC;AAAE,WAAO,MAAIG,MAAG,OAAKA,KAAEJ,GAAE,cAAY,MAAII,KAAEJ,GAAE,gBAAgB,cAAY,MAAII,MAAG,MAAIA,KAAEJ,GAAE,YAAUE;AAAA,EAAC,GAAE,WAAU,SAASF,IAAEC,IAAE;AAAC,QAAIC,KAAED,MAAG,CAAC;AAAE,WAAO,QAAMD,OAAI,EAAE,OAAOA,EAAC,CAAC,IAAE,GAAG,MAAME,IAAE,YAAU,OAAOF,KAAE,CAACA,EAAC,IAAEA,EAAC,IAAE,EAAE,KAAKE,IAAEF,EAAC,IAAGE;AAAA,EAAC,GAAE,SAAQ,SAASF,IAAEC,IAAEC,IAAE;AAAC,WAAO,QAAMD,KAAE,KAAG,GAAG,KAAKA,IAAED,IAAEE,EAAC;AAAA,EAAC,GAAE,UAAS,SAASF,IAAE;AAAC,QAAIC,KAAED,MAAGA,GAAE,cAAaE,KAAEF,OAAIA,GAAE,iBAAeA,IAAG;AAAgB,WAAM,CAAC,EAAE,KAAKC,MAAGC,MAAGA,GAAE,YAAU,MAAM;AAAA,EAAC,GAAE,OAAM,SAASF,IAAEC,IAAE;AAAC,aAAQC,KAAE,CAACD,GAAE,QAAOE,KAAE,GAAEC,KAAEJ,GAAE,QAAOG,KAAED,IAAEC,KAAI,CAAAH,GAAEI,IAAG,IAAEH,GAAEE,EAAC;AAAE,WAAOH,GAAE,SAAOI,IAAEJ;AAAA,EAAC,GAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC,aAAQC,KAAE,CAAC,GAAEC,KAAE,GAAEC,KAAEL,GAAE,QAAOM,KAAE,CAACJ,IAAEE,KAAEC,IAAED,KAAI,EAACH,GAAED,GAAEI,EAAC,GAAEA,EAAC,MAAIE,MAAGH,GAAE,KAAKH,GAAEI,EAAC,CAAC;AAAE,WAAOD;AAAA,EAAC,GAAE,KAAI,SAASH,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAE,GAAEC,KAAE,CAAC;AAAE,QAAG,EAAEN,EAAC,EAAE,MAAIG,KAAEH,GAAE,QAAOK,KAAEF,IAAEE,KAAI,UAAOD,KAAEH,GAAED,GAAEK,EAAC,GAAEA,IAAEH,EAAC,MAAII,GAAE,KAAKF,EAAC;AAAA,QAAO,MAAIC,MAAKL,GAAE,UAAOI,KAAEH,GAAED,GAAEK,EAAC,GAAEA,IAAEH,EAAC,MAAII,GAAE,KAAKF,EAAC;AAAE,WAAO,EAAEE,EAAC;AAAA,EAAC,GAAE,MAAK,GAAE,SAAQ,GAAE,CAAC,GAAE,cAAY,OAAO,WAAS,GAAG,GAAG,OAAO,QAAQ,IAAE,GAAG,OAAO,QAAQ,IAAG,GAAG,KAAK,uEAAuE,MAAM,GAAG,GAAE,SAASN,IAAEC,IAAE;AAAC,MAAE,aAAWA,KAAE,GAAG,IAAEA,GAAE,YAAY;AAAA,EAAC,CAAC;AAAE,MAAI,KAAG,GAAG,KAAI,KAAG,GAAG,MAAK,KAAG,GAAG,QAAO,KAAG,uBAAsB,KAAG,IAAI,OAAO,MAAI,KAAG,gCAA8B,KAAG,MAAK,GAAG;AAAE,KAAG,WAAS,SAASD,IAAEC,IAAE;AAAC,QAAIC,KAAED,MAAGA,GAAE;AAAW,WAAOD,OAAIE,MAAG,EAAE,CAACA,MAAG,MAAIA,GAAE,YAAU,EAAEF,GAAE,WAASA,GAAE,SAASE,EAAC,IAAEF,GAAE,2BAAyB,KAAGA,GAAE,wBAAwBE,EAAC;AAAA,EAAG;AAAE,MAAI,IAAE;AAA+C,WAAS,EAAEF,IAAEC,IAAE;AAAC,WAAOA,KAAE,SAAOD,KAAE,WAASA,GAAE,MAAM,GAAE,EAAE,IAAE,OAAKA,GAAE,WAAWA,GAAE,SAAO,CAAC,EAAE,SAAS,EAAE,IAAE,MAAI,OAAKA;AAAA,EAAC;AAAC,KAAG,iBAAe,SAASA,IAAE;AAAC,YAAOA,KAAE,IAAI,QAAQ,GAAE,CAAC;AAAA,EAAC;AAAE,MAAI,KAAG,GAAE,KAAG;AAAE,GAAC,WAAU;AAAC,QAAIA,IAAEU,IAAEC,IAAEN,IAAEC,IAAEM,IAAET,IAAEU,IAAEC,IAAEV,IAAEW,KAAE,IAAGC,KAAE,GAAG,SAAQC,KAAE,GAAEf,KAAE,GAAEK,KAAEW,GAAE,GAAEC,KAAED,GAAE,GAAEV,KAAEU,GAAE,GAAEE,KAAEF,GAAE,GAAET,KAAE,SAAST,IAAEC,IAAE;AAAC,aAAOD,OAAIC,OAAIK,KAAE,OAAI;AAAA,IAAC,GAAEe,KAAE,8HAA6HpB,KAAE,4BAA0B,KAAG,2CAA0CqB,KAAE,QAAM,KAAG,OAAKrB,KAAE,SAAO,KAAG,kBAAgB,KAAG,0DAA2DA,KAAE,SAAO,KAAG,QAAOsB,KAAE,OAAKtB,KAAE,uFAAwFqB,KAAE,gBAAeE,KAAE,IAAI,OAAO,KAAG,KAAI,GAAG,GAAEC,KAAE,IAAI,OAAO,MAAI,KAAG,OAAK,KAAG,GAAG,GAAEC,KAAE,IAAI,OAAO,MAAI,KAAG,aAAW,KAAG,MAAI,KAAG,GAAG,GAAEC,KAAE,IAAI,OAAO,KAAG,IAAI,GAAEC,KAAE,IAAI,OAAOL,EAAC,GAAEM,KAAE,IAAI,OAAO,MAAI5B,KAAE,GAAG,GAAE6B,KAAE,EAAC,IAAG,IAAI,OAAO,QAAM7B,KAAE,GAAG,GAAE,OAAM,IAAI,OAAO,UAAQA,KAAE,GAAG,GAAE,KAAI,IAAI,OAAO,OAAKA,KAAE,OAAO,GAAE,MAAK,IAAI,OAAO,MAAIqB,EAAC,GAAE,QAAO,IAAI,OAAO,MAAIC,EAAC,GAAE,OAAM,IAAI,OAAO,2DAAyD,KAAG,iCAA+B,KAAG,gBAAc,KAAG,eAAa,KAAG,UAAS,GAAG,GAAE,MAAK,IAAI,OAAO,SAAOF,KAAE,MAAK,GAAG,GAAE,cAAa,IAAI,OAAO,MAAI,KAAG,qDAAmD,KAAG,qBAAmB,KAAG,oBAAmB,GAAG,EAAC,GAAEU,KAAE,uCAAsCC,KAAE,UAASC,KAAE,oCAAmCC,KAAE,QAAOC,KAAE,IAAI,OAAO,yBAAuB,KAAG,wBAAuB,GAAG,GAAEC,KAAE,SAASpC,IAAEC,IAAE;AAAC,UAAIC,KAAE,OAAKF,GAAE,MAAM,CAAC,IAAE;AAAM,aAAOC,OAAIC,KAAE,IAAE,OAAO,aAAaA,KAAE,KAAK,IAAE,OAAO,aAAaA,MAAG,KAAG,OAAM,OAAKA,KAAE,KAAK;AAAA,IAAE,GAAEmC,KAAE,WAAU;AAAC,MAAAC,GAAE;AAAA,IAAC,GAAEC,KAAEC,GAAE,SAASxC,IAAE;AAAC,aAAM,SAAKA,GAAE,YAAU,GAAGA,IAAE,UAAU;AAAA,IAAC,GAAE,EAAC,KAAI,cAAa,MAAK,SAAQ,CAAC;AAAE,QAAG;AAAC,MAAAe,GAAE,MAAM,KAAG,GAAG,KAAK,GAAG,UAAU,GAAE,GAAG,UAAU,GAAE,GAAG,GAAG,WAAW,MAAM,EAAE;AAAA,IAAQ,SAAOf,IAAE;AAAC,MAAAe,KAAE,EAAC,OAAM,SAASf,IAAEC,IAAE;AAAC,WAAG,MAAMD,IAAE,GAAG,KAAKC,EAAC,CAAC;AAAA,MAAC,GAAE,MAAK,SAASD,IAAE;AAAC,WAAG,MAAMA,IAAE,GAAG,KAAK,WAAU,CAAC,CAAC;AAAA,MAAC,EAAC;AAAA,IAAC;AAAC,aAASyC,GAAExC,IAAED,IAAEE,IAAEC,IAAE;AAAC,UAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEU,IAAEE,KAAErB,MAAGA,GAAE,eAAcsB,KAAEtB,KAAEA,GAAE,WAAS;AAAE,UAAGE,KAAEA,MAAG,CAAC,GAAE,YAAU,OAAOD,MAAG,CAACA,MAAG,MAAIqB,MAAG,MAAIA,MAAG,OAAKA,GAAE,QAAOpB;AAAE,UAAG,CAACC,OAAImC,GAAEtC,EAAC,GAAEA,KAAEA,MAAGY,IAAEC,KAAG;AAAC,YAAG,OAAKS,OAAId,KAAEyB,GAAE,KAAKhC,EAAC,GAAG,KAAGG,KAAEI,GAAE,CAAC,GAAE;AAAC,cAAG,MAAIc,IAAE;AAAC,gBAAG,EAAEhB,KAAEN,GAAE,eAAeI,EAAC,GAAG,QAAOF;AAAE,gBAAGI,GAAE,OAAKF,GAAE,QAAOW,GAAE,KAAKb,IAAEI,EAAC,GAAEJ;AAAA,UAAC,WAASmB,OAAIf,KAAEe,GAAE,eAAejB,EAAC,MAAIqC,GAAE,SAASzC,IAAEM,EAAC,KAAGA,GAAE,OAAKF,GAAE,QAAOW,GAAE,KAAKb,IAAEI,EAAC,GAAEJ;AAAA,QAAC,OAAK;AAAC,cAAGM,GAAE,CAAC,EAAE,QAAOO,GAAE,MAAMb,IAAEF,GAAE,qBAAqBC,EAAC,CAAC,GAAEC;AAAE,eAAIE,KAAEI,GAAE,CAAC,MAAIR,GAAE,uBAAuB,QAAOe,GAAE,MAAMb,IAAEF,GAAE,uBAAuBI,EAAC,CAAC,GAAEF;AAAA,QAAC;AAAC,YAAG,EAAEkB,GAAEnB,KAAE,GAAG,KAAGa,MAAGA,GAAE,KAAKb,EAAC,IAAG;AAAC,cAAGkB,KAAElB,IAAEoB,KAAErB,IAAE,MAAIsB,OAAIK,GAAE,KAAK1B,EAAC,KAAGyB,GAAE,KAAKzB,EAAC,IAAG;AAAC,aAACoB,KAAEa,GAAE,KAAKjC,EAAC,KAAGyC,GAAE1C,GAAE,UAAU,KAAGA,OAAIA,MAAG,GAAG,WAASO,KAAEP,GAAE,aAAa,IAAI,KAAGO,KAAE,GAAG,eAAeA,EAAC,IAAEP,GAAE,aAAa,MAAKO,KAAES,EAAC,IAAGX,MAAGI,KAAEkC,GAAE1C,EAAC,GAAG;AAAO,mBAAMI,KAAI,CAAAI,GAAEJ,EAAC,KAAGE,KAAE,MAAIA,KAAE,YAAU,MAAIqC,GAAEnC,GAAEJ,EAAC,CAAC;AAAE,YAAAc,KAAEV,GAAE,KAAK,GAAG;AAAA,UAAC;AAAC,cAAG;AAAC,mBAAOM,GAAE,MAAMb,IAAEmB,GAAE,iBAAiBF,EAAC,CAAC,GAAEjB;AAAA,UAAC,SAAOF,IAAE;AAAC,YAAAoB,GAAEnB,IAAE,IAAE;AAAA,UAAC,UAAC;AAAQ,YAAAM,OAAIS,MAAGhB,GAAE,gBAAgB,IAAI;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAC,aAAO6C,IAAG5C,GAAE,QAAQ,IAAG,IAAI,GAAED,IAAEE,IAAEC,EAAC;AAAA,IAAC;AAAC,aAASe,KAAG;AAAC,UAAIf,KAAE,CAAC;AAAE,aAAO,SAASH,GAAEC,IAAEC,IAAE;AAAC,eAAOC,GAAE,KAAKF,KAAE,GAAG,IAAES,GAAE,eAAa,OAAOV,GAAEG,GAAE,MAAM,CAAC,GAAEH,GAAEC,KAAE,GAAG,IAAEC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS4C,GAAE9C,IAAE;AAAC,aAAOA,GAAEgB,EAAC,IAAE,MAAGhB;AAAA,IAAC;AAAC,aAAS+C,GAAE/C,IAAE;AAAC,UAAIC,KAAEW,GAAE,cAAc,UAAU;AAAE,UAAG;AAAC,eAAM,CAAC,CAACZ,GAAEC,EAAC;AAAA,MAAC,SAAOD,IAAE;AAAC,eAAM;AAAA,MAAE,UAAC;AAAQ,QAAAC,GAAE,cAAYA,GAAE,WAAW,YAAYA,EAAC,GAAEA,KAAE;AAAA,MAAI;AAAA,IAAC;AAAC,aAAS+C,GAAE/C,IAAE;AAAC,aAAO,SAASD,IAAE;AAAC,eAAO,GAAGA,IAAE,OAAO,KAAGA,GAAE,SAAOC;AAAA,MAAC;AAAA,IAAC;AAAC,aAASgD,GAAEhD,IAAE;AAAC,aAAO,SAASD,IAAE;AAAC,gBAAO,GAAGA,IAAE,OAAO,KAAG,GAAGA,IAAE,QAAQ,MAAIA,GAAE,SAAOC;AAAA,MAAC;AAAA,IAAC;AAAC,aAASiD,GAAEjD,IAAE;AAAC,aAAO,SAASD,IAAE;AAAC,eAAM,UAASA,KAAEA,GAAE,cAAY,UAAKA,GAAE,WAAS,WAAUA,KAAE,WAAUA,GAAE,aAAWA,GAAE,WAAW,aAAWC,KAAED,GAAE,aAAWC,KAAED,GAAE,eAAaC,MAAGD,GAAE,eAAa,CAACC,MAAGsC,GAAEvC,EAAC,MAAIC,KAAED,GAAE,aAAWC,KAAE,WAAUD,MAAGA,GAAE,aAAWC;AAAA,MAAC;AAAA,IAAC;AAAC,aAASkD,GAAE7C,IAAE;AAAC,aAAOwC,GAAE,SAASzC,IAAE;AAAC,eAAOA,KAAE,CAACA,IAAEyC,GAAE,SAAS9C,IAAEC,IAAE;AAAC,cAAIC,IAAEC,KAAEG,GAAE,CAAC,GAAEN,GAAE,QAAOK,EAAC,GAAED,KAAED,GAAE;AAAO,iBAAMC,KAAI,CAAAJ,GAAEE,KAAEC,GAAEC,EAAC,CAAC,MAAIJ,GAAEE,EAAC,IAAE,EAAED,GAAEC,EAAC,IAAEF,GAAEE,EAAC;AAAA,QAAG,CAAC;AAAA,MAAC,CAAC;AAAA,IAAC;AAAC,aAASwC,GAAE1C,IAAE;AAAC,aAAOA,MAAG,eAAa,OAAOA,GAAE,wBAAsBA;AAAA,IAAC;AAAC,aAASsC,GAAEtC,IAAE;AAAC,UAAIC,IAAEC,KAAEF,KAAEA,GAAE,iBAAeA,KAAE;AAAG,aAAOE,MAAGU,MAAG,MAAIV,GAAE,YAAUA,GAAE,oBAAkBC,MAAGS,KAAEV,IAAG,iBAAgBW,KAAE,CAAC,GAAG,SAASD,EAAC,GAAER,KAAED,GAAE,WAASA,GAAE,yBAAuBA,GAAE,mBAAkBA,GAAE,qBAAmB,MAAIS,OAAIX,KAAEW,GAAE,gBAAcX,GAAE,QAAMA,MAAGA,GAAE,iBAAiB,UAASoC,EAAC,GAAE,GAAG,UAAQU,GAAE,SAAS/C,IAAE;AAAC,eAAOG,GAAE,YAAYH,EAAC,EAAE,KAAG,GAAG,SAAQ,CAACY,GAAE,qBAAmB,CAACA,GAAE,kBAAkB,GAAG,OAAO,EAAE;AAAA,MAAM,CAAC,GAAE,GAAG,oBAAkBmC,GAAE,SAAS/C,IAAE;AAAC,eAAOI,GAAE,KAAKJ,IAAE,GAAG;AAAA,MAAC,CAAC,GAAE,GAAG,QAAM+C,GAAE,WAAU;AAAC,eAAOnC,GAAE,iBAAiB,QAAQ;AAAA,MAAC,CAAC,GAAE,GAAG,SAAOmC,GAAE,WAAU;AAAC,YAAG;AAAC,iBAAOnC,GAAE,cAAc,iBAAiB,GAAE;AAAA,QAAE,SAAOZ,IAAE;AAAC,iBAAM;AAAA,QAAE;AAAA,MAAC,CAAC,GAAE,GAAG,WAASU,GAAE,OAAO,KAAG,SAASV,IAAE;AAAC,YAAIC,KAAED,GAAE,QAAQmC,IAAEC,EAAC;AAAE,eAAO,SAASpC,IAAE;AAAC,iBAAOA,GAAE,aAAa,IAAI,MAAIC;AAAA,QAAC;AAAA,MAAC,GAAES,GAAE,KAAK,KAAG,SAASV,IAAEC,IAAE;AAAC,YAAG,eAAa,OAAOA,GAAE,kBAAgBY,IAAE;AAAC,cAAIX,KAAED,GAAE,eAAeD,EAAC;AAAE,iBAAOE,KAAE,CAACA,EAAC,IAAE,CAAC;AAAA,QAAC;AAAA,MAAC,MAAIQ,GAAE,OAAO,KAAG,SAASV,IAAE;AAAC,YAAIE,KAAEF,GAAE,QAAQmC,IAAEC,EAAC;AAAE,eAAO,SAASpC,IAAE;AAAC,cAAIC,KAAE,eAAa,OAAOD,GAAE,oBAAkBA,GAAE,iBAAiB,IAAI;AAAE,iBAAOC,MAAGA,GAAE,UAAQC;AAAA,QAAC;AAAA,MAAC,GAAEQ,GAAE,KAAK,KAAG,SAASV,IAAEC,IAAE;AAAC,YAAG,eAAa,OAAOA,GAAE,kBAAgBY,IAAE;AAAC,cAAIX,IAAEC,IAAEC,IAAEC,KAAEJ,GAAE,eAAeD,EAAC;AAAE,cAAGK,IAAE;AAAC,iBAAIH,KAAEG,GAAE,iBAAiB,IAAI,MAAIH,GAAE,UAAQF,GAAE,QAAM,CAACK,EAAC;AAAE,YAAAD,KAAEH,GAAE,kBAAkBD,EAAC,GAAEG,KAAE;AAAE,mBAAME,KAAED,GAAED,IAAG,EAAE,MAAID,KAAEG,GAAE,iBAAiB,IAAI,MAAIH,GAAE,UAAQF,GAAE,QAAM,CAACK,EAAC;AAAA,UAAC;AAAC,iBAAM,CAAC;AAAA,QAAC;AAAA,MAAC,IAAGK,GAAE,KAAK,MAAI,SAASV,IAAEC,IAAE;AAAC,eAAM,eAAa,OAAOA,GAAE,uBAAqBA,GAAE,qBAAqBD,EAAC,IAAEC,GAAE,iBAAiBD,EAAC;AAAA,MAAC,GAAEU,GAAE,KAAK,QAAM,SAASV,IAAEC,IAAE;AAAC,YAAG,eAAa,OAAOA,GAAE,0BAAwBY,GAAE,QAAOZ,GAAE,uBAAuBD,EAAC;AAAA,MAAC,GAAEc,KAAE,CAAC,GAAEiC,GAAE,SAAS/C,IAAE;AAAC,YAAIC;AAAE,QAAAE,GAAE,YAAYH,EAAC,EAAE,YAAU,YAAUgB,KAAE,mDAAiDA,KAAE,qEAAoEhB,GAAE,iBAAiB,YAAY,EAAE,UAAQc,GAAE,KAAK,QAAM,KAAG,eAAaO,KAAE,GAAG,GAAErB,GAAE,iBAAiB,UAAQgB,KAAE,IAAI,EAAE,UAAQF,GAAE,KAAK,IAAI,GAAEd,GAAE,iBAAiB,OAAKgB,KAAE,IAAI,EAAE,UAAQF,GAAE,KAAK,UAAU,GAAEd,GAAE,iBAAiB,UAAU,EAAE,UAAQc,GAAE,KAAK,UAAU,IAAGb,KAAEW,GAAE,cAAc,OAAO,GAAG,aAAa,QAAO,QAAQ,GAAEZ,GAAE,YAAYC,EAAC,EAAE,aAAa,QAAO,GAAG,GAAEE,GAAE,YAAYH,EAAC,EAAE,WAAS,MAAG,MAAIA,GAAE,iBAAiB,WAAW,EAAE,UAAQc,GAAE,KAAK,YAAW,WAAW,IAAGb,KAAEW,GAAE,cAAc,OAAO,GAAG,aAAa,QAAO,EAAE,GAAEZ,GAAE,YAAYC,EAAC,GAAED,GAAE,iBAAiB,WAAW,EAAE,UAAQc,GAAE,KAAK,QAAM,KAAG,UAAQ,KAAG,OAAK,KAAG,YAAc;AAAA,MAAC,CAAC,GAAE,GAAG,UAAQA,GAAE,KAAK,MAAM,GAAEA,KAAEA,GAAE,UAAQ,IAAI,OAAOA,GAAE,KAAK,GAAG,CAAC,GAAEL,KAAE,SAAST,IAAEC,IAAE;AAAC,YAAGD,OAAIC,GAAE,QAAOK,KAAE,MAAG;AAAE,YAAIJ,KAAE,CAACF,GAAE,0BAAwB,CAACC,GAAE;AAAwB,eAAOC,OAAI,KAAGA,MAAGF,GAAE,iBAAeA,QAAKC,GAAE,iBAAeA,MAAGD,GAAE,wBAAwBC,EAAC,IAAE,MAAI,CAAC,GAAG,gBAAcA,GAAE,wBAAwBD,EAAC,MAAIE,KAAEF,OAAIY,MAAGZ,GAAE,iBAAe,MAAIyC,GAAE,SAAS,IAAGzC,EAAC,IAAE,KAAGC,OAAIW,MAAGX,GAAE,iBAAe,MAAIwC,GAAE,SAAS,IAAGxC,EAAC,IAAE,IAAEI,KAAE,GAAG,KAAKA,IAAEL,EAAC,IAAE,GAAG,KAAKK,IAAEJ,EAAC,IAAE,IAAE,IAAEC,KAAE,KAAG;AAAA,MAAE,IAAGU;AAAA,IAAC;AAAC,SAAIZ,MAAKyC,GAAE,UAAQ,SAASzC,IAAEC,IAAE;AAAC,aAAOwC,GAAEzC,IAAE,MAAK,MAAKC,EAAC;AAAA,IAAC,GAAEwC,GAAE,kBAAgB,SAASzC,IAAEC,IAAE;AAAC,UAAGqC,GAAEtC,EAAC,GAAEa,MAAG,CAACO,GAAEnB,KAAE,GAAG,MAAI,CAACa,MAAG,CAACA,GAAE,KAAKb,EAAC,GAAG,KAAG;AAAC,YAAIC,KAAEE,GAAE,KAAKJ,IAAEC,EAAC;AAAE,YAAGC,MAAG,GAAG,qBAAmBF,GAAE,YAAU,OAAKA,GAAE,SAAS,SAAS,QAAOE;AAAA,MAAC,SAAOF,IAAE;AAAC,QAAAoB,GAAEnB,IAAE,IAAE;AAAA,MAAC;AAAC,aAAO,IAAEwC,GAAExC,IAAEW,IAAE,MAAK,CAACZ,EAAC,CAAC,EAAE;AAAA,IAAM,GAAEyC,GAAE,WAAS,SAASzC,IAAEC,IAAE;AAAC,cAAOD,GAAE,iBAAeA,OAAIY,MAAG0B,GAAEtC,EAAC,GAAE,GAAG,SAASA,IAAEC,EAAC;AAAA,IAAC,GAAEwC,GAAE,OAAK,SAASzC,IAAEC,IAAE;AAAC,OAACD,GAAE,iBAAeA,OAAIY,MAAG0B,GAAEtC,EAAC;AAAE,UAAIE,KAAEQ,GAAE,WAAWT,GAAE,YAAY,CAAC,GAAEE,KAAED,MAAG,GAAG,KAAKQ,GAAE,YAAWT,GAAE,YAAY,CAAC,IAAEC,GAAEF,IAAEC,IAAE,CAACY,EAAC,IAAE;AAAO,aAAO,WAASV,KAAEA,KAAEH,GAAE,aAAaC,EAAC;AAAA,IAAC,GAAEwC,GAAE,QAAM,SAASzC,IAAE;AAAC,YAAM,IAAI,MAAM,4CAA0CA,EAAC;AAAA,IAAC,GAAE,GAAG,aAAW,SAASA,IAAE;AAAC,UAAIC,IAAEC,KAAE,CAAC,GAAEC,KAAE,GAAEC,KAAE;AAAE,UAAGE,KAAE,CAAC,GAAG,YAAWD,KAAE,CAAC,GAAG,cAAY,GAAG,KAAKL,IAAE,CAAC,GAAE,GAAG,KAAKA,IAAES,EAAC,GAAEH,IAAE;AAAC,eAAML,KAAED,GAAEI,IAAG,EAAE,CAAAH,OAAID,GAAEI,EAAC,MAAID,KAAED,GAAE,KAAKE,EAAC;AAAG,eAAMD,KAAI,IAAG,KAAKH,IAAEE,GAAEC,EAAC,GAAE,CAAC;AAAA,MAAC;AAAC,aAAOE,KAAE,MAAKL;AAAA,IAAC,GAAE,GAAG,GAAG,aAAW,WAAU;AAAC,aAAO,KAAK,UAAU,GAAG,WAAW,GAAG,MAAM,IAAI,CAAC,CAAC;AAAA,IAAC,IAAGU,KAAE,GAAG,OAAK,EAAC,aAAY,IAAG,cAAaoC,IAAE,OAAMhB,IAAE,YAAW,CAAC,GAAE,MAAK,CAAC,GAAE,UAAS,EAAC,KAAI,EAAC,KAAI,cAAa,OAAM,KAAE,GAAE,KAAI,EAAC,KAAI,aAAY,GAAE,KAAI,EAAC,KAAI,mBAAkB,OAAM,KAAE,GAAE,KAAI,EAAC,KAAI,kBAAiB,EAAC,GAAE,WAAU,EAAC,MAAK,SAAS9B,IAAE;AAAC,aAAOA,GAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,QAAQmC,IAAEC,EAAC,GAAEpC,GAAE,CAAC,KAAGA,GAAE,CAAC,KAAGA,GAAE,CAAC,KAAGA,GAAE,CAAC,KAAG,IAAI,QAAQmC,IAAEC,EAAC,GAAE,SAAOpC,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAE,MAAIA,GAAE,CAAC,IAAE,MAAKA,GAAE,MAAM,GAAE,CAAC;AAAA,IAAC,GAAE,OAAM,SAASA,IAAE;AAAC,aAAOA,GAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,YAAY,GAAE,UAAQA,GAAE,CAAC,EAAE,MAAM,GAAE,CAAC,KAAGA,GAAE,CAAC,KAAGyC,GAAE,MAAMzC,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,EAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAGA,GAAE,CAAC,KAAG,KAAG,KAAG,WAASA,GAAE,CAAC,KAAG,UAAQA,GAAE,CAAC,KAAIA,GAAE,CAAC,IAAE,EAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAG,UAAQA,GAAE,CAAC,MAAIA,GAAE,CAAC,KAAGyC,GAAE,MAAMzC,GAAE,CAAC,CAAC,GAAEA;AAAA,IAAC,GAAE,QAAO,SAASA,IAAE;AAAC,UAAIC,IAAEC,KAAE,CAACF,GAAE,CAAC,KAAGA,GAAE,CAAC;AAAE,aAAO8B,GAAE,MAAM,KAAK9B,GAAE,CAAC,CAAC,IAAE,QAAMA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAGA,GAAE,CAAC,KAAG,KAAGE,MAAG0B,GAAE,KAAK1B,EAAC,MAAID,KAAE0C,GAAEzC,IAAE,IAAE,OAAKD,KAAEC,GAAE,QAAQ,KAAIA,GAAE,SAAOD,EAAC,IAAEC,GAAE,YAAUF,GAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,MAAM,GAAEC,EAAC,GAAED,GAAE,CAAC,IAAEE,GAAE,MAAM,GAAED,EAAC,IAAGD,GAAE,MAAM,GAAE,CAAC;AAAA,IAAE,EAAC,GAAE,QAAO,EAAC,KAAI,SAASA,IAAE;AAAC,UAAIC,KAAED,GAAE,QAAQmC,IAAEC,EAAC,EAAE,YAAY;AAAE,aAAM,QAAMpC,KAAE,WAAU;AAAC,eAAM;AAAA,MAAE,IAAE,SAASA,IAAE;AAAC,eAAO,GAAGA,IAAEC,EAAC;AAAA,MAAC;AAAA,IAAC,GAAE,OAAM,SAASD,IAAE;AAAC,UAAIC,KAAEM,GAAEP,KAAE,GAAG;AAAE,aAAOC,OAAIA,KAAE,IAAI,OAAO,QAAM,KAAG,MAAID,KAAE,MAAI,KAAG,KAAK,MAAIO,GAAEP,IAAE,SAASA,IAAE;AAAC,eAAOC,GAAE,KAAK,YAAU,OAAOD,GAAE,aAAWA,GAAE,aAAW,eAAa,OAAOA,GAAE,gBAAcA,GAAE,aAAa,OAAO,KAAG,EAAE;AAAA,MAAC,CAAC;AAAA,IAAC,GAAE,MAAK,SAASE,IAAEC,IAAEC,IAAE;AAAC,aAAO,SAASJ,IAAE;AAAC,YAAIC,KAAEwC,GAAE,KAAKzC,IAAEE,EAAC;AAAE,eAAO,QAAMD,KAAE,SAAOE,KAAE,CAACA,OAAIF,MAAG,IAAG,QAAME,KAAEF,OAAIG,KAAE,SAAOD,KAAEF,OAAIG,KAAE,SAAOD,KAAEC,MAAG,MAAIH,GAAE,QAAQG,EAAC,IAAE,SAAOD,KAAEC,MAAG,KAAGH,GAAE,QAAQG,EAAC,IAAE,SAAOD,KAAEC,MAAGH,GAAE,MAAM,CAACG,GAAE,MAAM,MAAIA,KAAE,SAAOD,KAAE,MAAI,MAAIF,GAAE,QAAQuB,IAAE,GAAG,IAAE,KAAK,QAAQpB,EAAC,IAAE,SAAOD,OAAIF,OAAIG,MAAGH,GAAE,MAAM,GAAEG,GAAE,SAAO,CAAC,MAAIA,KAAE;AAAA,MAAK;AAAA,IAAC,GAAE,OAAM,SAASU,IAAEd,IAAEC,IAAEmB,IAAEG,IAAE;AAAC,UAAIC,KAAE,UAAQV,GAAE,MAAM,GAAE,CAAC,GAAEW,KAAE,WAASX,GAAE,MAAM,EAAE,GAAEY,KAAE,cAAY1B;AAAE,aAAO,MAAIoB,MAAG,MAAIG,KAAE,SAASvB,IAAE;AAAC,eAAM,CAAC,CAACA,GAAE;AAAA,MAAU,IAAE,SAASA,IAAEC,IAAEC,IAAE;AAAC,YAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAEgB,OAAIC,KAAE,gBAAc,mBAAkBhB,KAAET,GAAE,YAAWmB,KAAEO,MAAG1B,GAAE,SAAS,YAAY,GAAEqB,KAAE,CAACnB,MAAG,CAACwB,IAAEJ,KAAE;AAAG,YAAGb,IAAE;AAAC,cAAGe,IAAE;AAAC,mBAAMhB,IAAE;AAAC,cAAAH,KAAEL;AAAE,qBAAMK,KAAEA,GAAEG,EAAC,EAAE,KAAGkB,KAAE,GAAGrB,IAAEc,EAAC,IAAE,MAAId,GAAE,SAAS,QAAM;AAAG,cAAAE,KAAEC,KAAE,WAASM,MAAG,CAACP,MAAG;AAAA,YAAa;AAAC,mBAAM;AAAA,UAAE;AAAC,cAAGA,KAAE,CAACkB,KAAEhB,GAAE,aAAWA,GAAE,SAAS,GAAEgB,MAAGJ,IAAE;AAAC,YAAAC,MAAGhB,MAAGH,MAAGC,KAAEK,GAAEO,EAAC,MAAIP,GAAEO,EAAC,IAAE,CAAC,IAAIF,EAAC,KAAG,CAAC,GAAG,CAAC,MAAIG,MAAGd,GAAE,CAAC,MAAIA,GAAE,CAAC,GAAEE,KAAEC,MAAGG,GAAE,WAAWH,EAAC;AAAE,mBAAMD,KAAE,EAAEC,MAAGD,MAAGA,GAAEG,EAAC,MAAIc,KAAEhB,KAAE,MAAIC,GAAE,IAAI,EAAE,KAAG,MAAIF,GAAE,YAAU,EAAEiB,MAAGjB,OAAIL,IAAE;AAAC,cAAAI,GAAEU,EAAC,IAAE,CAACG,IAAEX,IAAEgB,EAAC;AAAE;AAAA,YAAK;AAAA,UAAC,WAASD,OAAIC,KAAEhB,MAAGH,MAAGC,KAAEJ,GAAEgB,EAAC,MAAIhB,GAAEgB,EAAC,IAAE,CAAC,IAAIF,EAAC,KAAG,CAAC,GAAG,CAAC,MAAIG,MAAGd,GAAE,CAAC,IAAG,UAAKmB;AAAE,mBAAMjB,KAAE,EAAEC,MAAGD,MAAGA,GAAEG,EAAC,MAAIc,KAAEhB,KAAE,MAAIC,GAAE,IAAI,EAAE,MAAImB,KAAE,GAAGrB,IAAEc,EAAC,IAAE,MAAId,GAAE,aAAW,EAAEiB,OAAID,QAAKjB,KAAEC,GAAEW,EAAC,MAAIX,GAAEW,EAAC,IAAE,CAAC,IAAIF,EAAC,IAAE,CAACG,IAAEK,EAAC,IAAGjB,OAAIL,IAAG;AAAA;AAAM,kBAAOsB,MAAGC,QAAKH,MAAGE,KAAEF,MAAG,KAAG,KAAGE,KAAEF;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,GAAE,QAAO,SAASpB,IAAEK,IAAE;AAAC,UAAIJ,IAAEK,KAAEI,GAAE,QAAQV,EAAC,KAAGU,GAAE,WAAWV,GAAE,YAAY,CAAC,KAAGyC,GAAE,MAAM,yBAAuBzC,EAAC;AAAE,aAAOM,GAAEU,EAAC,IAAEV,GAAED,EAAC,IAAE,IAAEC,GAAE,UAAQL,KAAE,CAACD,IAAEA,IAAE,IAAGK,EAAC,GAAEK,GAAE,WAAW,eAAeV,GAAE,YAAY,CAAC,IAAE8C,GAAE,SAAS9C,IAAEC,IAAE;AAAC,YAAIC,IAAEC,KAAEG,GAAEN,IAAEK,EAAC,GAAED,KAAED,GAAE;AAAO,eAAMC,KAAI,CAAAJ,GAAEE,KAAE,GAAG,KAAKF,IAAEG,GAAEC,EAAC,CAAC,CAAC,IAAE,EAAEH,GAAEC,EAAC,IAAEC,GAAEC,EAAC;AAAA,MAAE,CAAC,IAAE,SAASJ,IAAE;AAAC,eAAOM,GAAEN,IAAE,GAAEC,EAAC;AAAA,MAAC,KAAGK;AAAA,IAAC,EAAC,GAAE,SAAQ,EAAC,KAAIwC,GAAE,SAAS9C,IAAE;AAAC,UAAIG,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEG,KAAE6C,IAAGpD,GAAE,QAAQ,IAAG,IAAI,CAAC;AAAE,aAAOO,GAAES,EAAC,IAAE8B,GAAE,SAAS9C,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAIC,IAAEC,KAAEE,GAAEP,IAAE,MAAKG,IAAE,CAAC,CAAC,GAAEG,KAAEN,GAAE;AAAO,eAAMM,KAAI,EAACF,KAAEC,GAAEC,EAAC,OAAKN,GAAEM,EAAC,IAAE,EAAEL,GAAEK,EAAC,IAAEF;AAAA,MAAG,CAAC,IAAE,SAASJ,IAAEC,IAAEC,IAAE;AAAC,eAAOC,GAAE,CAAC,IAAEH,IAAEO,GAAEJ,IAAE,MAAKD,IAAEE,EAAC,GAAED,GAAE,CAAC,IAAE,MAAK,CAACC,GAAE,IAAI;AAAA,MAAC;AAAA,IAAC,CAAC,GAAE,KAAI0C,GAAE,SAAS7C,IAAE;AAAC,aAAO,SAASD,IAAE;AAAC,eAAO,IAAEyC,GAAExC,IAAED,EAAC,EAAE;AAAA,MAAM;AAAA,IAAC,CAAC,GAAE,UAAS8C,GAAE,SAAS7C,IAAE;AAAC,aAAOA,KAAEA,GAAE,QAAQkC,IAAEC,EAAC,GAAE,SAASpC,IAAE;AAAC,eAAM,MAAIA,GAAE,eAAa,GAAG,KAAKA,EAAC,GAAG,QAAQC,EAAC;AAAA,MAAC;AAAA,IAAC,CAAC,GAAE,MAAK6C,GAAE,SAAS5C,IAAE;AAAC,aAAO2B,GAAE,KAAK3B,MAAG,EAAE,KAAGuC,GAAE,MAAM,uBAAqBvC,EAAC,GAAEA,KAAEA,GAAE,QAAQiC,IAAEC,EAAC,EAAE,YAAY,GAAE,SAASpC,IAAE;AAAC,YAAIC;AAAE,WAAE;AAAC,cAAGA,KAAEY,KAAEb,GAAE,OAAKA,GAAE,aAAa,UAAU,KAAGA,GAAE,aAAa,MAAM,EAAE,SAAOC,KAAEA,GAAE,YAAY,OAAKC,MAAG,MAAID,GAAE,QAAQC,KAAE,GAAG;AAAA,QAAC,UAAQF,KAAEA,GAAE,eAAa,MAAIA,GAAE;AAAU,eAAM;AAAA,MAAE;AAAA,IAAC,CAAC,GAAE,QAAO,SAASA,IAAE;AAAC,UAAIC,KAAE,GAAG,YAAU,GAAG,SAAS;AAAK,aAAOA,MAAGA,GAAE,MAAM,CAAC,MAAID,GAAE;AAAA,IAAE,GAAE,MAAK,SAASA,IAAE;AAAC,aAAOA,OAAIG;AAAA,IAAC,GAAE,OAAM,SAASH,IAAE;AAAC,aAAOA,OAAI,WAAU;AAAC,YAAG;AAAC,iBAAOY,GAAE;AAAA,QAAa,SAAOZ,IAAE;AAAA,QAAC;AAAA,MAAC,EAAE,KAAGY,GAAE,SAAS,KAAG,CAAC,EAAEZ,GAAE,QAAMA,GAAE,QAAM,CAACA,GAAE;AAAA,IAAS,GAAE,SAAQkD,GAAE,KAAE,GAAE,UAASA,GAAE,IAAE,GAAE,SAAQ,SAASlD,IAAE;AAAC,aAAO,GAAGA,IAAE,OAAO,KAAG,CAAC,CAACA,GAAE,WAAS,GAAGA,IAAE,QAAQ,KAAG,CAAC,CAACA,GAAE;AAAA,IAAQ,GAAE,UAAS,SAASA,IAAE;AAAC,aAAOA,GAAE,cAAYA,GAAE,WAAW,eAAc,SAAKA,GAAE;AAAA,IAAQ,GAAE,OAAM,SAASA,IAAE;AAAC,WAAIA,KAAEA,GAAE,YAAWA,IAAEA,KAAEA,GAAE,YAAY,KAAGA,GAAE,WAAS,EAAE,QAAM;AAAG,aAAM;AAAA,IAAE,GAAE,QAAO,SAASA,IAAE;AAAC,aAAM,CAACU,GAAE,QAAQ,MAAMV,EAAC;AAAA,IAAC,GAAE,QAAO,SAASA,IAAE;AAAC,aAAOgC,GAAE,KAAKhC,GAAE,QAAQ;AAAA,IAAC,GAAE,OAAM,SAASA,IAAE;AAAC,aAAO+B,GAAE,KAAK/B,GAAE,QAAQ;AAAA,IAAC,GAAE,QAAO,SAASA,IAAE;AAAC,aAAO,GAAGA,IAAE,OAAO,KAAG,aAAWA,GAAE,QAAM,GAAGA,IAAE,QAAQ;AAAA,IAAC,GAAE,MAAK,SAASA,IAAE;AAAC,UAAIC;AAAE,aAAO,GAAGD,IAAE,OAAO,KAAG,WAASA,GAAE,SAAO,SAAOC,KAAED,GAAE,aAAa,MAAM,MAAI,WAASC,GAAE,YAAY;AAAA,IAAE,GAAE,OAAMkD,GAAE,WAAU;AAAC,aAAM,CAAC,CAAC;AAAA,IAAC,CAAC,GAAE,MAAKA,GAAE,SAASnD,IAAEC,IAAE;AAAC,aAAM,CAACA,KAAE,CAAC;AAAA,IAAC,CAAC,GAAE,IAAGkD,GAAE,SAASnD,IAAEC,IAAEC,IAAE;AAAC,aAAM,CAACA,KAAE,IAAEA,KAAED,KAAEC,EAAC;AAAA,IAAC,CAAC,GAAE,MAAKiD,GAAE,SAASnD,IAAEC,IAAE;AAAC,eAAQC,KAAE,GAAEA,KAAED,IAAEC,MAAG,EAAE,CAAAF,GAAE,KAAKE,EAAC;AAAE,aAAOF;AAAA,IAAC,CAAC,GAAE,KAAImD,GAAE,SAASnD,IAAEC,IAAE;AAAC,eAAQC,KAAE,GAAEA,KAAED,IAAEC,MAAG,EAAE,CAAAF,GAAE,KAAKE,EAAC;AAAE,aAAOF;AAAA,IAAC,CAAC,GAAE,IAAGmD,GAAE,SAASnD,IAAEC,IAAEC,IAAE;AAAC,UAAIC;AAAE,WAAIA,KAAED,KAAE,IAAEA,KAAED,KAAEA,KAAEC,KAAED,KAAEC,IAAE,KAAG,EAAEC,KAAG,CAAAH,GAAE,KAAKG,EAAC;AAAE,aAAOH;AAAA,IAAC,CAAC,GAAE,IAAGmD,GAAE,SAASnD,IAAEC,IAAEC,IAAE;AAAC,eAAQC,KAAED,KAAE,IAAEA,KAAED,KAAEC,IAAE,EAAEC,KAAEF,KAAG,CAAAD,GAAE,KAAKG,EAAC;AAAE,aAAOH;AAAA,IAAC,CAAC,EAAC,EAAC,GAAG,QAAQ,MAAIU,GAAE,QAAQ,IAAG,EAAC,OAAM,MAAG,UAAS,MAAG,MAAK,MAAG,UAAS,MAAG,OAAM,KAAE,EAAE,CAAAA,GAAE,QAAQV,EAAC,IAAEgD,GAAEhD,EAAC;AAAE,SAAIA,MAAI,EAAC,QAAO,MAAG,OAAM,KAAE,EAAE,CAAAU,GAAE,QAAQV,EAAC,IAAEiD,GAAEjD,EAAC;AAAE,aAASqD,KAAG;AAAA,IAAC;AAAC,aAASV,GAAE3C,IAAEC,IAAE;AAAC,UAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAEU,GAAEnB,KAAE,GAAG;AAAE,UAAGS,GAAE,QAAOR,KAAE,IAAEQ,GAAE,MAAM,CAAC;AAAE,MAAAH,KAAEN,IAAEO,KAAE,CAAC,GAAEC,KAAEE,GAAE;AAAU,aAAMJ,IAAE;AAAC,aAAID,MAAKH,MAAG,EAAEC,KAAEsB,GAAE,KAAKnB,EAAC,OAAKH,OAAIG,KAAEA,GAAE,MAAMH,GAAE,CAAC,EAAE,MAAM,KAAGG,KAAGC,GAAE,KAAKH,KAAE,CAAC,CAAC,IAAGF,KAAE,QAAIC,KAAEuB,GAAE,KAAKpB,EAAC,OAAKJ,KAAEC,GAAE,MAAM,GAAEC,GAAE,KAAK,EAAC,OAAMF,IAAE,MAAKC,GAAE,CAAC,EAAE,QAAQ,IAAG,GAAG,EAAC,CAAC,GAAEG,KAAEA,GAAE,MAAMJ,GAAE,MAAM,IAAGQ,GAAE,OAAO,GAAEP,KAAE2B,GAAEzB,EAAC,EAAE,KAAKC,EAAC,MAAIE,GAAEH,EAAC,KAAG,EAAEF,KAAEK,GAAEH,EAAC,EAAEF,EAAC,OAAKD,KAAEC,GAAE,MAAM,GAAEC,GAAE,KAAK,EAAC,OAAMF,IAAE,MAAKG,IAAE,SAAQF,GAAC,CAAC,GAAEG,KAAEA,GAAE,MAAMJ,GAAE,MAAM;AAAG,YAAG,CAACA,GAAE;AAAA,MAAK;AAAC,aAAOD,KAAEK,GAAE,SAAOA,KAAEmC,GAAE,MAAMzC,EAAC,IAAEmB,GAAEnB,IAAEO,EAAC,EAAE,MAAM,CAAC;AAAA,IAAC;AAAC,aAASqC,GAAE5C,IAAE;AAAC,eAAQC,KAAE,GAAEC,KAAEF,GAAE,QAAOG,KAAE,IAAGF,KAAEC,IAAED,KAAI,CAAAE,MAAGH,GAAEC,EAAC,EAAE;AAAM,aAAOE;AAAA,IAAC;AAAC,aAASqC,GAAElC,IAAEN,IAAEC,IAAE;AAAC,UAAIM,KAAEP,GAAE,KAAIQ,KAAER,GAAE,MAAKS,KAAED,MAAGD,IAAEY,KAAElB,MAAG,iBAAeQ,IAAEY,KAAEnB;AAAI,aAAOF,GAAE,QAAM,SAASA,IAAEC,IAAEC,IAAE;AAAC,eAAMF,KAAEA,GAAEO,EAAC,EAAE,KAAG,MAAIP,GAAE,YAAUmB,GAAE,QAAOb,GAAEN,IAAEC,IAAEC,EAAC;AAAE,eAAM;AAAA,MAAE,IAAE,SAASF,IAAEC,IAAEC,IAAE;AAAC,YAAIC,IAAEC,IAAEC,KAAE,CAACY,IAAEI,EAAC;AAAE,YAAGnB,IAAE;AAAC,iBAAMF,KAAEA,GAAEO,EAAC,EAAE,MAAI,MAAIP,GAAE,YAAUmB,OAAIb,GAAEN,IAAEC,IAAEC,EAAC,EAAE,QAAM;AAAA,QAAE,MAAM,QAAMF,KAAEA,GAAEO,EAAC,EAAE,KAAG,MAAIP,GAAE,YAAUmB,GAAE,KAAGf,KAAEJ,GAAEgB,EAAC,MAAIhB,GAAEgB,EAAC,IAAE,CAAC,IAAGR,MAAG,GAAGR,IAAEQ,EAAC,EAAE,CAAAR,KAAEA,GAAEO,EAAC,KAAGP;AAAA,aAAM;AAAC,eAAIG,KAAEC,GAAEK,EAAC,MAAIN,GAAE,CAAC,MAAIc,MAAGd,GAAE,CAAC,MAAIkB,GAAE,QAAOhB,GAAE,CAAC,IAAEF,GAAE,CAAC;AAAE,eAAIC,GAAEK,EAAC,IAAEJ,IAAG,CAAC,IAAEC,GAAEN,IAAEC,IAAEC,EAAC,EAAE,QAAM;AAAA,QAAE;AAAC,eAAM;AAAA,MAAE;AAAA,IAAC;AAAC,aAASoD,GAAElD,IAAE;AAAC,aAAO,IAAEA,GAAE,SAAO,SAASJ,IAAEC,IAAEC,IAAE;AAAC,YAAIC,KAAEC,GAAE;AAAO,eAAMD,KAAI,KAAG,CAACC,GAAED,EAAC,EAAEH,IAAEC,IAAEC,EAAC,EAAE,QAAM;AAAG,eAAM;AAAA,MAAE,IAAEE,GAAE,CAAC;AAAA,IAAC;AAAC,aAASmD,GAAEvD,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAQC,IAAEC,KAAE,CAAC,GAAEC,KAAE,GAAEC,KAAER,GAAE,QAAOS,KAAE,QAAMR,IAAEM,KAAEC,IAAED,KAAI,EAACF,KAAEL,GAAEO,EAAC,OAAKL,MAAG,CAACA,GAAEG,IAAEF,IAAEC,EAAC,MAAIE,GAAE,KAAKD,EAAC,GAAEI,MAAGR,GAAE,KAAKM,EAAC;AAAI,aAAOD;AAAA,IAAC;AAAC,aAASkD,IAAG1C,IAAEM,IAAEG,IAAEC,IAAEC,IAAEzB,IAAE;AAAC,aAAOwB,MAAG,CAACA,GAAER,EAAC,MAAIQ,KAAEgC,IAAGhC,EAAC,IAAGC,MAAG,CAACA,GAAET,EAAC,MAAIS,KAAE+B,IAAG/B,IAAEzB,EAAC,IAAG8C,GAAE,SAAS9C,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAIC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEU,KAAElB,GAAE,QAAOoB,KAAErB,MAAG,SAASA,IAAEC,IAAEC,IAAE;AAAC,mBAAQC,KAAE,GAAEC,KAAEH,GAAE,QAAOE,KAAEC,IAAED,KAAI,CAAAsC,GAAEzC,IAAEC,GAAEE,EAAC,GAAED,EAAC;AAAE,iBAAOA;AAAA,QAAC,EAAEkB,MAAG,KAAIlB,GAAE,WAAS,CAACA,EAAC,IAAEA,IAAE,CAAC,CAAC,GAAEoB,KAAE,CAACR,MAAG,CAACd,MAAGoB,KAAEC,KAAEkC,GAAElC,IAAEb,IAAEM,IAAEZ,IAAEC,EAAC;AAAE,YAAGoB,KAAEA,GAAED,IAAEf,KAAEkB,OAAIzB,KAAEc,KAAEK,MAAGK,MAAG,CAAC,IAAEvB,IAAEC,IAAEC,EAAC,IAAEI,KAAEe,IAAEE,IAAE;AAAC,UAAApB,KAAEmD,GAAEhD,IAAEE,EAAC,GAAEe,GAAEpB,IAAE,CAAC,GAAEF,IAAEC,EAAC,GAAEE,KAAED,GAAE;AAAO,iBAAMC,KAAI,EAACC,KAAEF,GAAEC,EAAC,OAAKE,GAAEE,GAAEJ,EAAC,CAAC,IAAE,EAAEiB,GAAEb,GAAEJ,EAAC,CAAC,IAAEC;AAAA,QAAG;AAAC,YAAGN,IAAE;AAAC,cAAGyB,MAAGX,IAAE;AAAC,gBAAGW,IAAE;AAAC,cAAArB,KAAE,CAAC,GAAEC,KAAEE,GAAE;AAAO,qBAAMF,KAAI,EAACC,KAAEC,GAAEF,EAAC,MAAID,GAAE,KAAKkB,GAAEjB,EAAC,IAAEC,EAAC;AAAE,cAAAmB,GAAE,MAAKlB,KAAE,CAAC,GAAEH,IAAED,EAAC;AAAA,YAAC;AAAC,YAAAE,KAAEE,GAAE;AAAO,mBAAMF,KAAI,EAACC,KAAEC,GAAEF,EAAC,MAAI,MAAID,KAAEqB,KAAE,GAAG,KAAKzB,IAAEM,EAAC,IAAEE,GAAEH,EAAC,OAAKL,GAAEI,EAAC,IAAE,EAAEH,GAAEG,EAAC,IAAEE;AAAA,UAAG;AAAA,QAAC,MAAM,CAAAC,KAAEgD,GAAEhD,OAAIN,KAAEM,GAAE,OAAOY,IAAEZ,GAAE,MAAM,IAAEA,EAAC,GAAEkB,KAAEA,GAAE,MAAKxB,IAAEM,IAAEJ,EAAC,IAAEY,GAAE,MAAMd,IAAEM,EAAC;AAAA,MAAC,CAAC;AAAA,IAAC;AAAC,aAASkD,IAAGzD,IAAE;AAAC,eAAQI,IAAEH,IAAEC,IAAEC,KAAEH,GAAE,QAAOK,KAAEK,GAAE,SAASV,GAAE,CAAC,EAAE,IAAI,GAAEM,KAAED,MAAGK,GAAE,SAAS,GAAG,GAAEH,KAAEF,KAAE,IAAE,GAAEG,KAAEgC,GAAE,SAASxC,IAAE;AAAC,eAAOA,OAAII;AAAA,MAAC,GAAEE,IAAE,IAAE,GAAEG,KAAE+B,GAAE,SAASxC,IAAE;AAAC,eAAM,KAAG,GAAG,KAAKI,IAAEJ,EAAC;AAAA,MAAC,GAAEM,IAAE,IAAE,GAAEa,KAAE,CAAC,SAASnB,IAAEC,IAAEC,IAAE;AAAC,YAAIC,KAAE,CAACE,OAAIH,MAAGD,MAAGU,SAAMP,KAAEH,IAAG,WAASO,GAAER,IAAEC,IAAEC,EAAC,IAAEO,GAAET,IAAEC,IAAEC,EAAC;AAAG,eAAOE,KAAE,MAAKD;AAAA,MAAC,CAAC,GAAEI,KAAEJ,IAAEI,KAAI,KAAGN,KAAES,GAAE,SAASV,GAAEO,EAAC,EAAE,IAAI,EAAE,CAAAY,KAAE,CAACqB,GAAEc,GAAEnC,EAAC,GAAElB,EAAC,CAAC;AAAA,WAAM;AAAC,aAAIA,KAAES,GAAE,OAAOV,GAAEO,EAAC,EAAE,IAAI,EAAE,MAAM,MAAKP,GAAEO,EAAC,EAAE,OAAO,GAAGS,EAAC,GAAE;AAAC,eAAId,KAAE,EAAEK,IAAEL,KAAEC,IAAED,KAAI,KAAGQ,GAAE,SAASV,GAAEE,EAAC,EAAE,IAAI,EAAE;AAAM,iBAAOsD,IAAG,IAAEjD,MAAG+C,GAAEnC,EAAC,GAAE,IAAEZ,MAAGqC,GAAE5C,GAAE,MAAM,GAAEO,KAAE,CAAC,EAAE,OAAO,EAAC,OAAM,QAAMP,GAAEO,KAAE,CAAC,EAAE,OAAK,MAAI,GAAE,CAAC,CAAC,EAAE,QAAQ,IAAG,IAAI,GAAEN,IAAEM,KAAEL,MAAGuD,IAAGzD,GAAE,MAAMO,IAAEL,EAAC,CAAC,GAAEA,KAAEC,MAAGsD,IAAGzD,KAAEA,GAAE,MAAME,EAAC,CAAC,GAAEA,KAAEC,MAAGyC,GAAE5C,EAAC,CAAC;AAAA,QAAC;AAAC,QAAAmB,GAAE,KAAKlB,EAAC;AAAA,MAAC;AAAC,aAAOqD,GAAEnC,EAAC;AAAA,IAAC;AAAC,aAASiC,IAAGpD,IAAEC,IAAE;AAAC,UAAIC,IAAEsB,IAAEC,IAAEC,IAAEC,IAAExB,IAAEC,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEC,KAAEE,GAAER,KAAE,GAAG;AAAE,UAAG,CAACM,IAAE;AAAC,QAAAL,OAAIA,KAAE0C,GAAE3C,EAAC,IAAGE,KAAED,GAAE;AAAO,eAAMC,KAAI,EAACI,KAAEmD,IAAGxD,GAAEC,EAAC,CAAC,GAAGc,EAAC,IAAEZ,GAAE,KAAKE,EAAC,IAAED,GAAE,KAAKC,EAAC;AAAE,SAACA,KAAEE,GAAER,KAAGwB,KAAEnB,IAAEqB,KAAE,KAAGD,KAAErB,IAAG,QAAOuB,KAAE,IAAEH,GAAE,QAAOrB,KAAE,SAASH,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC,IAAEC,IAAEC,IAAEC,KAAE,GAAEC,KAAE,KAAIU,KAAEnB,MAAG,CAAC,GAAEqB,KAAE,CAAC,GAAEC,KAAEX,IAAEG,KAAEd,MAAG2B,MAAGjB,GAAE,KAAK,IAAI,KAAIN,EAAC,GAAEgB,KAAEH,MAAG,QAAMK,KAAE,IAAE,KAAK,OAAO,KAAG,KAAGC,KAAET,GAAE;AAAO,eAAIV,OAAIO,KAAEV,MAAGW,MAAGX,MAAGG,KAAGK,OAAIc,MAAG,SAAOlB,KAAES,GAAEL,EAAC,IAAGA,MAAI;AAAC,gBAAGkB,MAAGtB,IAAE;AAAC,cAAAC,KAAE,GAAEL,MAAGI,GAAE,iBAAeO,OAAI0B,GAAEjC,EAAC,GAAEH,KAAE,CAACW;AAAG,qBAAMN,KAAEiB,GAAElB,IAAG,EAAE,KAAGC,GAAEF,IAAEJ,MAAGW,IAAEV,EAAC,GAAE;AAAC,gBAAAa,GAAE,KAAKZ,IAAEE,EAAC;AAAE;AAAA,cAAK;AAAC,cAAAD,OAAIa,KAAEG;AAAA,YAAE;AAAC,YAAAM,QAAKrB,KAAE,CAACE,MAAGF,OAAIG,MAAIR,MAAGmB,GAAE,KAAKd,EAAC;AAAA,UAAE;AAAC,cAAGG,MAAGC,IAAEiB,MAAGjB,OAAID,IAAE;AAAC,YAAAF,KAAE;AAAE,mBAAMC,KAAEkB,GAAEnB,IAAG,EAAE,CAAAC,GAAEY,IAAEE,IAAEpB,IAAEC,EAAC;AAAE,gBAAGF,IAAE;AAAC,kBAAG,IAAEQ,GAAE,QAAMC,KAAI,CAAAU,GAAEV,EAAC,KAAGY,GAAEZ,EAAC,MAAIY,GAAEZ,EAAC,IAAE,GAAG,KAAKN,EAAC;AAAG,cAAAkB,KAAEkC,GAAElC,EAAC;AAAA,YAAC;AAAC,YAAAN,GAAE,MAAMZ,IAAEkB,EAAC,GAAEjB,MAAG,CAACJ,MAAG,IAAEqB,GAAE,UAAQ,IAAEb,KAAEiB,GAAE,UAAQ,GAAG,WAAWtB,EAAC;AAAA,UAAC;AAAC,iBAAOC,OAAIa,KAAEG,IAAET,KAAEW,KAAGH;AAAA,QAAC,GAAEO,KAAEoB,GAAE3C,EAAC,IAAEA,GAAE,GAAG,WAASH;AAAA,MAAC;AAAC,aAAOM;AAAA,IAAC;AAAC,aAASuC,IAAG7C,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,cAAY,OAAOT,MAAGA,IAAEmB,KAAE,CAAChB,MAAGwC,GAAE3C,KAAES,GAAE,YAAUT,EAAC;AAAE,UAAGE,KAAEA,MAAG,CAAC,GAAE,MAAIiB,GAAE,QAAO;AAAC,YAAG,KAAGd,KAAEc,GAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,MAAM,CAAC,GAAG,UAAQ,UAAQb,KAAED,GAAE,CAAC,GAAG,QAAM,MAAIJ,GAAE,YAAUY,MAAGH,GAAE,SAASL,GAAE,CAAC,EAAE,IAAI,GAAE;AAAC,cAAG,EAAEJ,MAAGS,GAAE,KAAK,GAAGJ,GAAE,QAAQ,CAAC,EAAE,QAAQ6B,IAAEC,EAAC,GAAEnC,EAAC,KAAG,CAAC,GAAG,CAAC,GAAG,QAAOC;AAAE,UAAAO,OAAIR,KAAEA,GAAE,aAAYD,KAAEA,GAAE,MAAMK,GAAE,MAAM,EAAE,MAAM,MAAM;AAAA,QAAC;AAAC,QAAAD,KAAE0B,GAAE,aAAa,KAAK9B,EAAC,IAAE,IAAEK,GAAE;AAAO,eAAMD,MAAI;AAAC,cAAGE,KAAED,GAAED,EAAC,GAAEM,GAAE,SAASH,KAAED,GAAE,IAAI,EAAE;AAAM,eAAIE,KAAEE,GAAE,KAAKH,EAAC,OAAKJ,KAAEK,GAAEF,GAAE,QAAQ,CAAC,EAAE,QAAQ6B,IAAEC,EAAC,GAAEF,GAAE,KAAK7B,GAAE,CAAC,EAAE,IAAI,KAAGqC,GAAEzC,GAAE,UAAU,KAAGA,EAAC,IAAG;AAAC,gBAAGI,GAAE,OAAOD,IAAE,CAAC,GAAE,EAAEJ,KAAEG,GAAE,UAAQyC,GAAEvC,EAAC,GAAG,QAAOU,GAAE,MAAMb,IAAEC,EAAC,GAAED;AAAE;AAAA,UAAK;AAAA,QAAC;AAAA,MAAC;AAAC,cAAOO,MAAG2C,IAAGpD,IAAEmB,EAAC,GAAGhB,IAAEF,IAAE,CAACY,IAAEX,IAAE,CAACD,MAAGiC,GAAE,KAAKlC,EAAC,KAAG0C,GAAEzC,GAAE,UAAU,KAAGA,EAAC,GAAEC;AAAA,IAAC;AAAC,IAAAmD,GAAE,YAAU3C,GAAE,UAAQA,GAAE,SAAQA,GAAE,aAAW,IAAI2C,MAAE,GAAG,aAAWrC,GAAE,MAAM,EAAE,EAAE,KAAKP,EAAC,EAAE,KAAK,EAAE,MAAIO,IAAEsB,GAAE,GAAE,GAAG,eAAaS,GAAE,SAAS/C,IAAE;AAAC,aAAO,IAAEA,GAAE,wBAAwBY,GAAE,cAAc,UAAU,CAAC;AAAA,IAAC,CAAC,GAAE,GAAG,OAAK6B,IAAE,GAAG,KAAK,GAAG,IAAE,GAAG,KAAK,SAAQ,GAAG,SAAO,GAAG,YAAWA,GAAE,UAAQW,KAAGX,GAAE,SAAOI,KAAGJ,GAAE,cAAYH,IAAEG,GAAE,WAASE,IAAEF,GAAE,SAAO,GAAG,gBAAeA,GAAE,UAAQ,GAAG,MAAKA,GAAE,QAAM,GAAG,UAASA,GAAE,YAAU,GAAG,MAAKA,GAAE,UAAQ,GAAG,SAAQA,GAAE,aAAW,GAAG;AAAA,EAAU,EAAE;AAAE,MAAI,IAAE,SAASzC,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAE,CAAC,GAAEC,KAAE,WAASF;AAAE,YAAOF,KAAEA,GAAEC,EAAC,MAAI,MAAID,GAAE,SAAS,KAAG,MAAIA,GAAE,UAAS;AAAC,UAAGI,MAAG,GAAGJ,EAAC,EAAE,GAAGE,EAAC,EAAE;AAAM,MAAAC,GAAE,KAAKH,EAAC;AAAA,IAAC;AAAC,WAAOG;AAAA,EAAC,GAAE,IAAE,SAASH,IAAEC,IAAE;AAAC,aAAQC,KAAE,CAAC,GAAEF,IAAEA,KAAEA,GAAE,YAAY,OAAIA,GAAE,YAAUA,OAAIC,MAAGC,GAAE,KAAKF,EAAC;AAAE,WAAOE;AAAA,EAAC,GAAE,IAAE,GAAG,KAAK,MAAM,cAAa,IAAE;AAAkE,WAAS,EAAEF,IAAEE,IAAEC,IAAE;AAAC,WAAO,EAAED,EAAC,IAAE,GAAG,KAAKF,IAAE,SAASA,IAAEC,IAAE;AAAC,aAAM,CAAC,CAACC,GAAE,KAAKF,IAAEC,IAAED,EAAC,MAAIG;AAAA,IAAC,CAAC,IAAED,GAAE,WAAS,GAAG,KAAKF,IAAE,SAASA,IAAE;AAAC,aAAOA,OAAIE,OAAIC;AAAA,IAAC,CAAC,IAAE,YAAU,OAAOD,KAAE,GAAG,KAAKF,IAAE,SAASA,IAAE;AAAC,aAAM,KAAG,GAAG,KAAKE,IAAEF,EAAC,MAAIG;AAAA,IAAC,CAAC,IAAE,GAAG,OAAOD,IAAEF,IAAEG,EAAC;AAAA,EAAC;AAAC,KAAG,SAAO,SAASH,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAEF,GAAE,CAAC;AAAE,WAAOC,OAAIF,KAAE,UAAQA,KAAE,MAAK,MAAIC,GAAE,UAAQ,MAAIE,GAAE,WAAS,GAAG,KAAK,gBAAgBA,IAAEH,EAAC,IAAE,CAACG,EAAC,IAAE,CAAC,IAAE,GAAG,KAAK,QAAQH,IAAE,GAAG,KAAKC,IAAE,SAASD,IAAE;AAAC,aAAO,MAAIA,GAAE;AAAA,IAAQ,CAAC,CAAC;AAAA,EAAC,GAAE,GAAG,GAAG,OAAO,EAAC,MAAK,SAASA,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAE,KAAK,QAAOC,KAAE;AAAK,QAAG,YAAU,OAAOJ,GAAE,QAAO,KAAK,UAAU,GAAGA,EAAC,EAAE,OAAO,WAAU;AAAC,WAAIC,KAAE,GAAEA,KAAEE,IAAEF,KAAI,KAAG,GAAG,SAASG,GAAEH,EAAC,GAAE,IAAI,EAAE,QAAM;AAAA,IAAE,CAAC,CAAC;AAAE,SAAIC,KAAE,KAAK,UAAU,CAAC,CAAC,GAAED,KAAE,GAAEA,KAAEE,IAAEF,KAAI,IAAG,KAAKD,IAAEI,GAAEH,EAAC,GAAEC,EAAC;AAAE,WAAO,IAAEC,KAAE,GAAG,WAAWD,EAAC,IAAEA;AAAA,EAAC,GAAE,QAAO,SAASF,IAAE;AAAC,WAAO,KAAK,UAAU,EAAE,MAAKA,MAAG,CAAC,GAAE,KAAE,CAAC;AAAA,EAAC,GAAE,KAAI,SAASA,IAAE;AAAC,WAAO,KAAK,UAAU,EAAE,MAAKA,MAAG,CAAC,GAAE,IAAE,CAAC;AAAA,EAAC,GAAE,IAAG,SAASA,IAAE;AAAC,WAAM,CAAC,CAAC,EAAE,MAAK,YAAU,OAAOA,MAAG,EAAE,KAAKA,EAAC,IAAE,GAAGA,EAAC,IAAEA,MAAG,CAAC,GAAE,KAAE,EAAE;AAAA,EAAM,EAAC,CAAC;AAAE,MAAI,GAAE,IAAE;AAAsC,GAAC,GAAG,GAAG,OAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC;AAAE,QAAG,CAACJ,GAAE,QAAO;AAAK,QAAGE,KAAEA,MAAG,GAAE,YAAU,OAAOF,IAAE;AAAC,UAAG,EAAEG,KAAE,QAAMH,GAAE,CAAC,KAAG,QAAMA,GAAEA,GAAE,SAAO,CAAC,KAAG,KAAGA,GAAE,SAAO,CAAC,MAAKA,IAAE,IAAI,IAAE,EAAE,KAAKA,EAAC,MAAI,CAACG,GAAE,CAAC,KAAGF,GAAE,QAAM,CAACA,MAAGA,GAAE,UAAQA,MAAGC,IAAG,KAAKF,EAAC,IAAE,KAAK,YAAYC,EAAC,EAAE,KAAKD,EAAC;AAAE,UAAGG,GAAE,CAAC,GAAE;AAAC,YAAGF,KAAEA,cAAa,KAAGA,GAAE,CAAC,IAAEA,IAAE,GAAG,MAAM,MAAK,GAAG,UAAUE,GAAE,CAAC,GAAEF,MAAGA,GAAE,WAASA,GAAE,iBAAeA,KAAE,GAAE,IAAE,CAAC,GAAE,EAAE,KAAKE,GAAE,CAAC,CAAC,KAAG,GAAG,cAAcF,EAAC,EAAE,MAAIE,MAAKF,GAAE,GAAE,KAAKE,EAAC,CAAC,IAAE,KAAKA,EAAC,EAAEF,GAAEE,EAAC,CAAC,IAAE,KAAK,KAAKA,IAAEF,GAAEE,EAAC,CAAC;AAAE,eAAO;AAAA,MAAI;AAAC,cAAOC,KAAE,EAAE,eAAeD,GAAE,CAAC,CAAC,OAAK,KAAK,CAAC,IAAEC,IAAE,KAAK,SAAO,IAAG;AAAA,IAAI;AAAC,WAAOJ,GAAE,YAAU,KAAK,CAAC,IAAEA,IAAE,KAAK,SAAO,GAAE,QAAM,EAAEA,EAAC,IAAE,WAASE,GAAE,QAAMA,GAAE,MAAMF,EAAC,IAAEA,GAAE,EAAE,IAAE,GAAG,UAAUA,IAAE,IAAI;AAAA,EAAC,GAAG,YAAU,GAAG,IAAG,IAAE,GAAG,CAAC;AAAE,MAAI,IAAE,kCAAiC,IAAE,EAAC,UAAS,MAAG,UAAS,MAAG,MAAK,MAAG,MAAK,KAAE;AAAE,WAAS,EAAEA,IAAEC,IAAE;AAAC,YAAOD,KAAEA,GAAEC,EAAC,MAAI,MAAID,GAAE,SAAS;AAAC,WAAOA;AAAA,EAAC;AAAC,KAAG,GAAG,OAAO,EAAC,KAAI,SAASA,IAAE;AAAC,QAAIC,KAAE,GAAGD,IAAE,IAAI,GAAEE,KAAED,GAAE;AAAO,WAAO,KAAK,OAAO,WAAU;AAAC,eAAQD,KAAE,GAAEA,KAAEE,IAAEF,KAAI,KAAG,GAAG,SAAS,MAAKC,GAAED,EAAC,CAAC,EAAE,QAAM;AAAA,IAAE,CAAC;AAAA,EAAC,GAAE,SAAQ,SAASA,IAAEC,IAAE;AAAC,QAAIC,IAAEC,KAAE,GAAEC,KAAE,KAAK,QAAOC,KAAE,CAAC,GAAEC,KAAE,YAAU,OAAON,MAAG,GAAGA,EAAC;AAAE,QAAG,CAAC,EAAE,KAAKA,EAAC;AAAE,aAAKG,KAAEC,IAAED,KAAI,MAAID,KAAE,KAAKC,EAAC,GAAED,MAAGA,OAAID,IAAEC,KAAEA,GAAE,WAAW,KAAGA,GAAE,WAAS,OAAKI,KAAE,KAAGA,GAAE,MAAMJ,EAAC,IAAE,MAAIA,GAAE,YAAU,GAAG,KAAK,gBAAgBA,IAAEF,EAAC,IAAG;AAAC,QAAAK,GAAE,KAAKH,EAAC;AAAE;AAAA,MAAK;AAAA;AAAC,WAAO,KAAK,UAAU,IAAEG,GAAE,SAAO,GAAG,WAAWA,EAAC,IAAEA,EAAC;AAAA,EAAC,GAAE,OAAM,SAASL,IAAE;AAAC,WAAOA,KAAE,YAAU,OAAOA,KAAE,GAAG,KAAK,GAAGA,EAAC,GAAE,KAAK,CAAC,CAAC,IAAE,GAAG,KAAK,MAAKA,GAAE,SAAOA,GAAE,CAAC,IAAEA,EAAC,IAAE,KAAK,CAAC,KAAG,KAAK,CAAC,EAAE,aAAW,KAAK,MAAM,EAAE,QAAQ,EAAE,SAAO;AAAA,EAAE,GAAE,KAAI,SAASA,IAAEC,IAAE;AAAC,WAAO,KAAK,UAAU,GAAG,WAAW,GAAG,MAAM,KAAK,IAAI,GAAE,GAAGD,IAAEC,EAAC,CAAC,CAAC,CAAC;AAAA,EAAC,GAAE,SAAQ,SAASD,IAAE;AAAC,WAAO,KAAK,IAAI,QAAMA,KAAE,KAAK,aAAW,KAAK,WAAW,OAAOA,EAAC,CAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,KAAK,EAAC,QAAO,SAASA,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAW,WAAOC,MAAG,OAAKA,GAAE,WAASA,KAAE;AAAA,EAAI,GAAE,SAAQ,SAASD,IAAE;AAAC,WAAO,EAAEA,IAAE,YAAY;AAAA,EAAC,GAAE,cAAa,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAO,EAAEF,IAAE,cAAaE,EAAC;AAAA,EAAC,GAAE,MAAK,SAASF,IAAE;AAAC,WAAO,EAAEA,IAAE,aAAa;AAAA,EAAC,GAAE,MAAK,SAASA,IAAE;AAAC,WAAO,EAAEA,IAAE,iBAAiB;AAAA,EAAC,GAAE,SAAQ,SAASA,IAAE;AAAC,WAAO,EAAEA,IAAE,aAAa;AAAA,EAAC,GAAE,SAAQ,SAASA,IAAE;AAAC,WAAO,EAAEA,IAAE,iBAAiB;AAAA,EAAC,GAAE,WAAU,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAO,EAAEF,IAAE,eAAcE,EAAC;AAAA,EAAC,GAAE,WAAU,SAASF,IAAEC,IAAEC,IAAE;AAAC,WAAO,EAAEF,IAAE,mBAAkBE,EAAC;AAAA,EAAC,GAAE,UAAS,SAASF,IAAE;AAAC,WAAO,GAAGA,GAAE,cAAY,CAAC,GAAG,YAAWA,EAAC;AAAA,EAAC,GAAE,UAAS,SAASA,IAAE;AAAC,WAAO,EAAEA,GAAE,UAAU;AAAA,EAAC,GAAE,UAAS,SAASA,IAAE;AAAC,WAAO,QAAMA,GAAE,mBAAiB,EAAEA,GAAE,eAAe,IAAEA,GAAE,mBAAiB,GAAGA,IAAE,UAAU,MAAIA,KAAEA,GAAE,WAASA,KAAG,GAAG,MAAM,CAAC,GAAEA,GAAE,UAAU;AAAA,EAAE,EAAC,GAAE,SAASG,IAAEC,IAAE;AAAC,OAAG,GAAGD,EAAC,IAAE,SAASH,IAAEC,IAAE;AAAC,UAAIC,KAAE,GAAG,IAAI,MAAKE,IAAEJ,EAAC;AAAE,aAAM,YAAUG,GAAE,MAAM,EAAE,MAAIF,KAAED,KAAGC,MAAG,YAAU,OAAOA,OAAIC,KAAE,GAAG,OAAOD,IAAEC,EAAC,IAAG,IAAE,KAAK,WAAS,EAAEC,EAAC,KAAG,GAAG,WAAWD,EAAC,GAAE,EAAE,KAAKC,EAAC,KAAGD,GAAE,QAAQ,IAAG,KAAK,UAAUA,EAAC;AAAA,IAAC;AAAA,EAAC,CAAC;AAAE,MAAI,IAAE;AAAoB,WAAS,EAAEF,IAAE;AAAC,WAAOA;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,UAAMA;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAIC;AAAE,QAAG;AAAC,MAAAJ,MAAG,EAAEI,KAAEJ,GAAE,OAAO,IAAEI,GAAE,KAAKJ,EAAC,EAAE,KAAKC,EAAC,EAAE,KAAKC,EAAC,IAAEF,MAAG,EAAEI,KAAEJ,GAAE,IAAI,IAAEI,GAAE,KAAKJ,IAAEC,IAAEC,EAAC,IAAED,GAAE,MAAM,QAAO,CAACD,EAAC,EAAE,MAAMG,EAAC,CAAC;AAAA,IAAC,SAAOH,IAAE;AAAC,MAAAE,GAAE,MAAM,QAAO,CAACF,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,KAAG,YAAU,SAASG,IAAE;AAAC,QAAIH,IAAEE;AAAE,IAAAC,KAAE,YAAU,OAAOA,MAAGH,KAAEG,IAAED,KAAE,CAAC,GAAE,GAAG,KAAKF,GAAE,MAAM,CAAC,KAAG,CAAC,GAAE,SAASA,IAAEC,IAAE;AAAC,MAAAC,GAAED,EAAC,IAAE;AAAA,IAAE,CAAC,GAAEC,MAAG,GAAG,OAAO,CAAC,GAAEC,EAAC;AAAE,QAAIC,IAAEH,IAAEI,IAAEC,IAAEC,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEC,KAAE,IAAGU,KAAE,WAAU;AAAC,WAAIb,KAAEA,MAAGH,GAAE,MAAKE,KAAED,KAAE,MAAGI,GAAE,QAAOC,KAAE,IAAG;AAAC,QAAAR,KAAEO,GAAE,MAAM;AAAE,eAAM,EAAEC,KAAEF,GAAE,OAAO,WAAKA,GAAEE,EAAC,EAAE,MAAMR,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,KAAGE,GAAE,gBAAcM,KAAEF,GAAE,QAAON,KAAE;AAAA,MAAG;AAAC,MAAAE,GAAE,WAASF,KAAE,QAAIG,KAAE,OAAGE,OAAIC,KAAEN,KAAE,CAAC,IAAE;AAAA,IAAG,GAAEoB,KAAE,EAAC,KAAI,WAAU;AAAC,aAAOd,OAAIN,MAAG,CAACG,OAAIK,KAAEF,GAAE,SAAO,GAAEC,GAAE,KAAKP,EAAC,IAAG,SAASC,GAAEF,IAAE;AAAC,WAAG,KAAKA,IAAE,SAASA,IAAEC,IAAE;AAAC,YAAEA,EAAC,IAAEE,GAAE,UAAQkB,GAAE,IAAIpB,EAAC,KAAGM,GAAE,KAAKN,EAAC,IAAEA,MAAGA,GAAE,UAAQ,aAAW,EAAEA,EAAC,KAAGC,GAAED,EAAC;AAAA,QAAC,CAAC;AAAA,MAAC,EAAE,SAAS,GAAEA,MAAG,CAACG,MAAGe,GAAE,IAAG;AAAA,IAAI,GAAE,QAAO,WAAU;AAAC,aAAO,GAAG,KAAK,WAAU,SAASnB,IAAEC,IAAE;AAAC,YAAIC;AAAE,eAAM,MAAIA,KAAE,GAAG,QAAQD,IAAEM,IAAEL,EAAC,GAAG,CAAAK,GAAE,OAAOL,IAAE,CAAC,GAAEA,MAAGO,MAAGA;AAAA,MAAG,CAAC,GAAE;AAAA,IAAI,GAAE,KAAI,SAAST,IAAE;AAAC,aAAOA,KAAE,KAAG,GAAG,QAAQA,IAAEO,EAAC,IAAE,IAAEA,GAAE;AAAA,IAAM,GAAE,OAAM,WAAU;AAAC,aAAOA,OAAIA,KAAE,CAAC,IAAG;AAAA,IAAI,GAAE,SAAQ,WAAU;AAAC,aAAOD,KAAEE,KAAE,CAAC,GAAED,KAAEN,KAAE,IAAG;AAAA,IAAI,GAAE,UAAS,WAAU;AAAC,aAAM,CAACM;AAAA,IAAC,GAAE,MAAK,WAAU;AAAC,aAAOD,KAAEE,KAAE,CAAC,GAAEP,MAAGG,OAAIG,KAAEN,KAAE,KAAI;AAAA,IAAI,GAAE,QAAO,WAAU;AAAC,aAAM,CAAC,CAACK;AAAA,IAAC,GAAE,UAAS,SAASN,IAAEC,IAAE;AAAC,aAAOK,OAAIL,KAAE,CAACD,KAAGC,KAAEA,MAAG,CAAC,GAAG,QAAMA,GAAE,MAAM,IAAEA,EAAC,GAAEO,GAAE,KAAKP,EAAC,GAAEG,MAAGe,GAAE,IAAG;AAAA,IAAI,GAAE,MAAK,WAAU;AAAC,aAAOE,GAAE,SAAS,MAAK,SAAS,GAAE;AAAA,IAAI,GAAE,OAAM,WAAU;AAAC,aAAM,CAAC,CAAChB;AAAA,IAAC,EAAC;AAAE,WAAOgB;AAAA,EAAC,GAAE,GAAG,OAAO,EAAC,UAAS,SAASrB,IAAE;AAAC,QAAIK,KAAE,CAAC,CAAC,UAAS,YAAW,GAAG,UAAU,QAAQ,GAAE,GAAG,UAAU,QAAQ,GAAE,CAAC,GAAE,CAAC,WAAU,QAAO,GAAG,UAAU,aAAa,GAAE,GAAG,UAAU,aAAa,GAAE,GAAE,UAAU,GAAE,CAAC,UAAS,QAAO,GAAG,UAAU,aAAa,GAAE,GAAG,UAAU,aAAa,GAAE,GAAE,UAAU,CAAC,GAAED,KAAE,WAAUE,KAAE,EAAC,OAAM,WAAU;AAAC,aAAOF;AAAA,IAAC,GAAE,QAAO,WAAU;AAAC,aAAOG,GAAE,KAAK,SAAS,EAAE,KAAK,SAAS,GAAE;AAAA,IAAI,GAAE,SAAQ,SAASP,IAAE;AAAC,aAAOM,GAAE,KAAK,MAAKN,EAAC;AAAA,IAAC,GAAE,MAAK,WAAU;AAAC,UAAII,KAAE;AAAU,aAAO,GAAG,SAAS,SAASD,IAAE;AAAC,WAAG,KAAKE,IAAE,SAASL,IAAEC,IAAE;AAAC,cAAIC,KAAE,EAAEE,GAAEH,GAAE,CAAC,CAAC,CAAC,KAAGG,GAAEH,GAAE,CAAC,CAAC;AAAE,UAAAM,GAAEN,GAAE,CAAC,CAAC,EAAE,WAAU;AAAC,gBAAID,KAAEE,MAAGA,GAAE,MAAM,MAAK,SAAS;AAAE,YAAAF,MAAG,EAAEA,GAAE,OAAO,IAAEA,GAAE,QAAQ,EAAE,SAASG,GAAE,MAAM,EAAE,KAAKA,GAAE,OAAO,EAAE,KAAKA,GAAE,MAAM,IAAEA,GAAEF,GAAE,CAAC,IAAE,MAAM,EAAE,MAAKC,KAAE,CAACF,EAAC,IAAE,SAAS;AAAA,UAAC,CAAC;AAAA,QAAC,CAAC,GAAEI,KAAE;AAAA,MAAI,CAAC,EAAE,QAAQ;AAAA,IAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC,UAAIK,KAAE;AAAE,eAASC,GAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAO,WAAU;AAAC,cAAIL,KAAE,MAAKC,KAAE,WAAUH,KAAE,WAAU;AAAC,gBAAIA,IAAEC;AAAE,gBAAG,EAAEG,KAAEI,KAAG;AAAC,mBAAIR,KAAEM,GAAE,MAAMJ,IAAEC,EAAC,OAAKE,GAAE,QAAQ,EAAE,OAAM,IAAI,UAAU,0BAA0B;AAAE,cAAAJ,KAAED,OAAI,YAAU,OAAOA,MAAG,cAAY,OAAOA,OAAIA,GAAE,MAAK,EAAEC,EAAC,IAAEM,KAAEN,GAAE,KAAKD,IAAES,GAAED,IAAEH,IAAE,GAAEE,EAAC,GAAEE,GAAED,IAAEH,IAAE,GAAEE,EAAC,CAAC,KAAGC,MAAIP,GAAE,KAAKD,IAAES,GAAED,IAAEH,IAAE,GAAEE,EAAC,GAAEE,GAAED,IAAEH,IAAE,GAAEE,EAAC,GAAEE,GAAED,IAAEH,IAAE,GAAEA,GAAE,UAAU,CAAC,MAAIC,OAAI,MAAIJ,KAAE,QAAOC,KAAE,CAACH,EAAC,KAAIO,MAAGF,GAAE,aAAaH,IAAEC,EAAC;AAAA,YAAE;AAAA,UAAC,GAAEF,KAAEM,KAAEP,KAAE,WAAU;AAAC,gBAAG;AAAC,cAAAA,GAAE;AAAA,YAAC,SAAOA,IAAE;AAAC,iBAAG,SAAS,iBAAe,GAAG,SAAS,cAAcA,IAAEC,GAAE,KAAK,GAAEO,MAAGJ,KAAE,MAAIE,OAAI,MAAIJ,KAAE,QAAOC,KAAE,CAACH,EAAC,IAAGK,GAAE,WAAWH,IAAEC,EAAC;AAAA,YAAE;AAAA,UAAC;AAAE,UAAAC,KAAEH,GAAE,KAAG,GAAG,SAAS,eAAaA,GAAE,QAAM,GAAG,SAAS,aAAa,IAAE,GAAG,SAAS,iBAAeA,GAAE,QAAM,GAAG,SAAS,aAAa,IAAG,GAAG,WAAWA,EAAC;AAAA,QAAE;AAAA,MAAC;AAAC,aAAO,GAAG,SAAS,SAASD,IAAE;AAAC,QAAAK,GAAE,CAAC,EAAE,CAAC,EAAE,IAAII,GAAE,GAAET,IAAE,EAAEG,EAAC,IAAEA,KAAE,GAAEH,GAAE,UAAU,CAAC,GAAEK,GAAE,CAAC,EAAE,CAAC,EAAE,IAAII,GAAE,GAAET,IAAE,EAAEC,EAAC,IAAEA,KAAE,CAAC,CAAC,GAAEI,GAAE,CAAC,EAAE,CAAC,EAAE,IAAII,GAAE,GAAET,IAAE,EAAEE,EAAC,IAAEA,KAAE,CAAC,CAAC;AAAA,MAAC,CAAC,EAAE,QAAQ;AAAA,IAAC,GAAE,SAAQ,SAASF,IAAE;AAAC,aAAO,QAAMA,KAAE,GAAG,OAAOA,IAAEM,EAAC,IAAEA;AAAA,IAAC,EAAC,GAAEC,KAAE,CAAC;AAAE,WAAO,GAAG,KAAKF,IAAE,SAASL,IAAEC,IAAE;AAAC,UAAIC,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,MAAAK,GAAEL,GAAE,CAAC,CAAC,IAAEC,GAAE,KAAIC,MAAGD,GAAE,IAAI,WAAU;AAAC,QAAAE,KAAED;AAAA,MAAC,GAAEE,GAAE,IAAEL,EAAC,EAAE,CAAC,EAAE,SAAQK,GAAE,IAAEL,EAAC,EAAE,CAAC,EAAE,SAAQK,GAAE,CAAC,EAAE,CAAC,EAAE,MAAKA,GAAE,CAAC,EAAE,CAAC,EAAE,IAAI,GAAEH,GAAE,IAAID,GAAE,CAAC,EAAE,IAAI,GAAEM,GAAEN,GAAE,CAAC,CAAC,IAAE,WAAU;AAAC,eAAOM,GAAEN,GAAE,CAAC,IAAE,MAAM,EAAE,SAAOM,KAAE,SAAO,MAAK,SAAS,GAAE;AAAA,MAAI,GAAEA,GAAEN,GAAE,CAAC,IAAE,MAAM,IAAEC,GAAE;AAAA,IAAQ,CAAC,GAAEI,GAAE,QAAQC,EAAC,GAAEP,MAAGA,GAAE,KAAKO,IAAEA,EAAC,GAAEA;AAAA,EAAC,GAAE,MAAK,SAASP,IAAE;AAAC,QAAIE,KAAE,UAAU,QAAOD,KAAEC,IAAEC,KAAE,MAAMF,EAAC,GAAEG,KAAE,GAAG,KAAK,SAAS,GAAEC,KAAE,GAAG,SAAS,GAAEC,KAAE,SAASL,IAAE;AAAC,aAAO,SAASD,IAAE;AAAC,QAAAG,GAAEF,EAAC,IAAE,MAAKG,GAAEH,EAAC,IAAE,IAAE,UAAU,SAAO,GAAG,KAAK,SAAS,IAAED,IAAE,EAAEE,MAAGG,GAAE,YAAYF,IAAEC,EAAC;AAAA,MAAC;AAAA,IAAC;AAAE,QAAGF,MAAG,MAAI,EAAEF,IAAEK,GAAE,KAAKC,GAAEL,EAAC,CAAC,EAAE,SAAQI,GAAE,QAAO,CAACH,EAAC,GAAE,cAAYG,GAAE,MAAM,KAAG,EAAED,GAAEH,EAAC,KAAGG,GAAEH,EAAC,EAAE,IAAI,GAAG,QAAOI,GAAE,KAAK;AAAE,WAAMJ,KAAI,GAAEG,GAAEH,EAAC,GAAEK,GAAEL,EAAC,GAAEI,GAAE,MAAM;AAAE,WAAOA,GAAE,QAAQ;AAAA,EAAC,EAAC,CAAC;AAAE,MAAI,IAAE;AAAyD,KAAG,SAAS,gBAAc,SAASL,IAAEC,IAAE;AAAC,OAAG,WAAS,GAAG,QAAQ,QAAMD,MAAG,EAAE,KAAKA,GAAE,IAAI,KAAG,GAAG,QAAQ,KAAK,gCAA8BA,GAAE,SAAQA,GAAE,OAAMC,EAAC;AAAA,EAAC,GAAE,GAAG,iBAAe,SAASD,IAAE;AAAC,OAAG,WAAW,WAAU;AAAC,YAAMA;AAAA,IAAC,CAAC;AAAA,EAAC;AAAE,MAAI,IAAE,GAAG,SAAS;AAAE,WAAS,IAAG;AAAC,MAAE,oBAAoB,oBAAmB,CAAC,GAAE,GAAG,oBAAoB,QAAO,CAAC,GAAE,GAAG,MAAM;AAAA,EAAC;AAAC,KAAG,GAAG,QAAM,SAASA,IAAE;AAAC,WAAO,EAAE,KAAKA,EAAC,EAAE,OAAO,EAAE,SAASA,IAAE;AAAC,SAAG,eAAeA,EAAC;AAAA,IAAC,CAAC,GAAE;AAAA,EAAI,GAAE,GAAG,OAAO,EAAC,SAAQ,OAAG,WAAU,GAAE,OAAM,SAASA,IAAE;AAAC,KAAC,SAAKA,KAAE,EAAE,GAAG,YAAU,GAAG,aAAW,GAAG,UAAQ,UAAMA,MAAG,IAAE,EAAE,GAAG,aAAW,EAAE,YAAY,GAAE,CAAC,EAAE,CAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,MAAM,OAAK,EAAE,MAAK,eAAa,EAAE,cAAY,cAAY,EAAE,cAAY,CAAC,EAAE,gBAAgB,WAAS,GAAG,WAAW,GAAG,KAAK,KAAG,EAAE,iBAAiB,oBAAmB,CAAC,GAAE,GAAG,iBAAiB,QAAO,CAAC;AAAG,MAAI,IAAE,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAE,GAAEC,KAAER,GAAE,QAAOS,KAAE,QAAMP;AAAE,QAAG,aAAW,EAAEA,EAAC,EAAE,MAAIK,MAAKH,KAAE,MAAGF,GAAE,GAAEF,IAAEC,IAAEM,IAAEL,GAAEK,EAAC,GAAE,MAAGF,IAAEC,EAAC;AAAA,aAAU,WAASH,OAAIC,KAAE,MAAG,EAAED,EAAC,MAAIG,KAAE,OAAIG,OAAIH,MAAGL,GAAE,KAAKD,IAAEG,EAAC,GAAEF,KAAE,SAAOQ,KAAER,IAAEA,KAAE,SAASD,IAAEC,IAAEC,IAAE;AAAC,aAAOO,GAAE,KAAK,GAAGT,EAAC,GAAEE,EAAC;AAAA,IAAC,KAAID,IAAG,QAAKM,KAAEC,IAAED,KAAI,CAAAN,GAAED,GAAEO,EAAC,GAAEL,IAAEI,KAAEH,KAAEA,GAAE,KAAKH,GAAEO,EAAC,GAAEA,IAAEN,GAAED,GAAEO,EAAC,GAAEL,EAAC,CAAC,CAAC;AAAE,WAAOE,KAAEJ,KAAES,KAAER,GAAE,KAAKD,EAAC,IAAEQ,KAAEP,GAAED,GAAE,CAAC,GAAEE,EAAC,IAAEG;AAAA,EAAC,GAAE,IAAE,SAAQ,IAAE;AAAY,WAAS,EAAEL,IAAEC,IAAE;AAAC,WAAOA,GAAE,YAAY;AAAA,EAAC;AAAC,WAAS,EAAED,IAAE;AAAC,WAAOA,GAAE,QAAQ,GAAE,KAAK,EAAE,QAAQ,GAAE,CAAC;AAAA,EAAC;AAAC,MAAI,IAAE,SAASA,IAAE;AAAC,WAAO,MAAIA,GAAE,YAAU,MAAIA,GAAE,YAAU,CAAC,CAACA,GAAE;AAAA,EAAQ;AAAE,WAAS,IAAG;AAAC,SAAK,UAAQ,GAAG,UAAQ,EAAE;AAAA,EAAK;AAAC,IAAE,MAAI,GAAE,EAAE,YAAU,EAAC,OAAM,SAASA,IAAE;AAAC,QAAIC,KAAED,GAAE,KAAK,OAAO;AAAE,WAAOC,OAAIA,KAAE,CAAC,GAAE,EAAED,EAAC,MAAIA,GAAE,WAASA,GAAE,KAAK,OAAO,IAAEC,KAAE,OAAO,eAAeD,IAAE,KAAK,SAAQ,EAAC,OAAMC,IAAE,cAAa,KAAE,CAAC,KAAIA;AAAA,EAAC,GAAE,KAAI,SAASD,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,KAAE,KAAK,MAAMJ,EAAC;AAAE,QAAG,YAAU,OAAOC,GAAE,CAAAG,GAAE,EAAEH,EAAC,CAAC,IAAEC;AAAA,QAAO,MAAIC,MAAKF,GAAE,CAAAG,GAAE,EAAED,EAAC,CAAC,IAAEF,GAAEE,EAAC;AAAE,WAAOC;AAAA,EAAC,GAAE,KAAI,SAASJ,IAAEC,IAAE;AAAC,WAAO,WAASA,KAAE,KAAK,MAAMD,EAAC,IAAEA,GAAE,KAAK,OAAO,KAAGA,GAAE,KAAK,OAAO,EAAE,EAAEC,EAAC,CAAC;AAAA,EAAC,GAAE,QAAO,SAASD,IAAEC,IAAEC,IAAE;AAAC,WAAO,WAASD,MAAGA,MAAG,YAAU,OAAOA,MAAG,WAASC,KAAE,KAAK,IAAIF,IAAEC,EAAC,KAAG,KAAK,IAAID,IAAEC,IAAEC,EAAC,GAAE,WAASA,KAAEA,KAAED;AAAA,EAAE,GAAE,QAAO,SAASD,IAAEC,IAAE;AAAC,QAAIC,IAAEC,KAAEH,GAAE,KAAK,OAAO;AAAE,QAAG,WAASG,IAAE;AAAC,UAAG,WAASF,IAAE;AAAC,QAAAC,MAAGD,KAAE,MAAM,QAAQA,EAAC,IAAEA,GAAE,IAAI,CAAC,KAAGA,KAAE,EAAEA,EAAC,MAAKE,KAAE,CAACF,EAAC,IAAEA,GAAE,MAAM,CAAC,KAAG,CAAC,GAAG;AAAO,eAAMC,KAAI,QAAOC,GAAEF,GAAEC,EAAC,CAAC;AAAA,MAAC;AAAC,OAAC,WAASD,MAAG,GAAG,cAAcE,EAAC,OAAKH,GAAE,WAASA,GAAE,KAAK,OAAO,IAAE,SAAO,OAAOA,GAAE,KAAK,OAAO;AAAA,IAAE;AAAA,EAAC,GAAE,SAAQ,SAASA,IAAE;AAAC,QAAIC,KAAED,GAAE,KAAK,OAAO;AAAE,WAAO,WAASC,MAAG,CAAC,GAAG,cAAcA,EAAC;AAAA,EAAC,EAAC;AAAE,MAAI,IAAE,IAAI,KAAE,IAAE,IAAI,KAAE,IAAE,iCAAgC,IAAE;AAAS,WAAS,EAAED,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC;AAAE,QAAG,WAASF,MAAG,MAAIF,GAAE,SAAS,KAAGG,KAAE,UAAQF,GAAE,QAAQ,GAAE,KAAK,EAAE,YAAY,GAAE,YAAU,QAAOC,KAAEF,GAAE,aAAaG,EAAC,IAAG;AAAC,UAAG;AAAC,QAAAD,KAAE,YAAUE,KAAEF,OAAI,YAAUE,OAAI,WAASA,KAAE,OAAKA,OAAI,CAACA,KAAE,KAAG,CAACA,KAAE,EAAE,KAAKA,EAAC,IAAE,KAAK,MAAMA,EAAC,IAAEA;AAAA,MAAE,SAAOJ,IAAE;AAAA,MAAC;AAAC,QAAE,IAAIA,IAAEC,IAAEC,EAAC;AAAA,IAAC,MAAM,CAAAA,KAAE;AAAO,WAAOA;AAAA,EAAC;AAAC,KAAG,OAAO,EAAC,SAAQ,SAASF,IAAE;AAAC,WAAO,EAAE,QAAQA,EAAC,KAAG,EAAE,QAAQA,EAAC;AAAA,EAAC,GAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAO,EAAE,OAAOF,IAAEC,IAAEC,EAAC;AAAA,EAAC,GAAE,YAAW,SAASF,IAAEC,IAAE;AAAC,MAAE,OAAOD,IAAEC,EAAC;AAAA,EAAC,GAAE,OAAM,SAASD,IAAEC,IAAEC,IAAE;AAAC,WAAO,EAAE,OAAOF,IAAEC,IAAEC,EAAC;AAAA,EAAC,GAAE,aAAY,SAASF,IAAEC,IAAE;AAAC,MAAE,OAAOD,IAAEC,EAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,GAAG,OAAO,EAAC,MAAK,SAASC,IAAEF,IAAE;AAAC,QAAIC,IAAEE,IAAEC,IAAEC,KAAE,KAAK,CAAC,GAAEC,KAAED,MAAGA,GAAE;AAAW,QAAG,WAASH,IAAE;AAAC,UAAG,KAAK,WAASE,KAAE,EAAE,IAAIC,EAAC,GAAE,MAAIA,GAAE,YAAU,CAAC,EAAE,IAAIA,IAAE,cAAc,IAAG;AAAC,QAAAJ,KAAEK,GAAE;AAAO,eAAML,KAAI,CAAAK,GAAEL,EAAC,KAAG,OAAKE,KAAEG,GAAEL,EAAC,EAAE,MAAM,QAAQ,OAAO,MAAIE,KAAE,EAAEA,GAAE,MAAM,CAAC,CAAC,GAAE,EAAEE,IAAEF,IAAEC,GAAED,EAAC,CAAC;AAAG,UAAE,IAAIE,IAAE,gBAAe,IAAE;AAAA,MAAC;AAAC,aAAOD;AAAA,IAAC;AAAC,WAAM,YAAU,OAAOF,KAAE,KAAK,KAAK,WAAU;AAAC,QAAE,IAAI,MAAKA,EAAC;AAAA,IAAC,CAAC,IAAE,EAAE,MAAK,SAASF,IAAE;AAAC,UAAIC;AAAE,UAAGI,MAAG,WAASL,GAAE,QAAO,YAAUC,KAAE,EAAE,IAAII,IAAEH,EAAC,KAAGD,KAAE,YAAUA,KAAE,EAAEI,IAAEH,EAAC,KAAGD,KAAE;AAAO,WAAK,KAAK,WAAU;AAAC,UAAE,IAAI,MAAKC,IAAEF,EAAC;AAAA,MAAC,CAAC;AAAA,IAAC,GAAE,MAAKA,IAAE,IAAE,UAAU,QAAO,MAAK,IAAE;AAAA,EAAC,GAAE,YAAW,SAASA,IAAE;AAAC,WAAO,KAAK,KAAK,WAAU;AAAC,QAAE,OAAO,MAAKA,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,OAAO,EAAC,OAAM,SAASA,IAAEC,IAAEC,IAAE;AAAC,QAAIC;AAAE,QAAGH,GAAE,QAAOC,MAAGA,MAAG,QAAM,SAAQE,KAAE,EAAE,IAAIH,IAAEC,EAAC,GAAEC,OAAI,CAACC,MAAG,MAAM,QAAQD,EAAC,IAAEC,KAAE,EAAE,OAAOH,IAAEC,IAAE,GAAG,UAAUC,EAAC,CAAC,IAAEC,GAAE,KAAKD,EAAC,IAAGC,MAAG,CAAC;AAAA,EAAC,GAAE,SAAQ,SAASH,IAAEC,IAAE;AAAC,IAAAA,KAAEA,MAAG;AAAK,QAAIC,KAAE,GAAG,MAAMF,IAAEC,EAAC,GAAEE,KAAED,GAAE,QAAOE,KAAEF,GAAE,MAAM,GAAEG,KAAE,GAAG,YAAYL,IAAEC,EAAC;AAAE,qBAAeG,OAAIA,KAAEF,GAAE,MAAM,GAAEC,OAAKC,OAAI,SAAOH,MAAGC,GAAE,QAAQ,YAAY,GAAE,OAAOG,GAAE,MAAKD,GAAE,KAAKJ,IAAE,WAAU;AAAC,SAAG,QAAQA,IAAEC,EAAC;AAAA,IAAC,GAAEI,EAAC,IAAG,CAACF,MAAGE,MAAGA,GAAE,MAAM,KAAK;AAAA,EAAC,GAAE,aAAY,SAASL,IAAEC,IAAE;AAAC,QAAIC,KAAED,KAAE;AAAa,WAAO,EAAE,IAAID,IAAEE,EAAC,KAAG,EAAE,OAAOF,IAAEE,IAAE,EAAC,OAAM,GAAG,UAAU,aAAa,EAAE,IAAI,WAAU;AAAC,QAAE,OAAOF,IAAE,CAACC,KAAE,SAAQC,EAAC,CAAC;AAAA,IAAC,CAAC,EAAC,CAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,GAAG,OAAO,EAAC,OAAM,SAASD,IAAEC,IAAE;AAAC,QAAIF,KAAE;AAAE,WAAM,YAAU,OAAOC,OAAIC,KAAED,IAAEA,KAAE,MAAKD,OAAK,UAAU,SAAOA,KAAE,GAAG,MAAM,KAAK,CAAC,GAAEC,EAAC,IAAE,WAASC,KAAE,OAAK,KAAK,KAAK,WAAU;AAAC,UAAIF,KAAE,GAAG,MAAM,MAAKC,IAAEC,EAAC;AAAE,SAAG,YAAY,MAAKD,EAAC,GAAE,SAAOA,MAAG,iBAAeD,GAAE,CAAC,KAAG,GAAG,QAAQ,MAAKC,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,SAAQ,SAASD,IAAE;AAAC,WAAO,KAAK,KAAK,WAAU;AAAC,SAAG,QAAQ,MAAKA,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,YAAW,SAASA,IAAE;AAAC,WAAO,KAAK,MAAMA,MAAG,MAAK,CAAC,CAAC;AAAA,EAAC,GAAE,SAAQ,SAASA,IAAEC,IAAE;AAAC,QAAIC,IAAEC,KAAE,GAAEC,KAAE,GAAG,SAAS,GAAEC,KAAE,MAAKC,KAAE,KAAK,QAAOC,KAAE,WAAU;AAAC,QAAEJ,MAAGC,GAAE,YAAYC,IAAE,CAACA,EAAC,CAAC;AAAA,IAAC;AAAE,gBAAU,OAAOL,OAAIC,KAAED,IAAEA,KAAE,SAAQA,KAAEA,MAAG;AAAK,WAAMM,KAAI,EAACJ,KAAE,EAAE,IAAIG,GAAEC,EAAC,GAAEN,KAAE,YAAY,MAAIE,GAAE,UAAQC,MAAID,GAAE,MAAM,IAAIK,EAAC;AAAG,WAAOA,GAAE,GAAEH,GAAE,QAAQH,EAAC;AAAA,EAAC,EAAC,CAAC;AAAE,MAAI,IAAE,sCAAsC,QAAO,IAAE,IAAI,OAAO,mBAAiB,IAAE,eAAc,GAAG,GAAE,IAAE,CAAC,OAAM,SAAQ,UAAS,MAAM,GAAE,IAAE,EAAE,iBAAgB,IAAE,SAASD,IAAE;AAAC,WAAO,GAAG,SAASA,GAAE,eAAcA,EAAC;AAAA,EAAC,GAAE,IAAE,EAAC,UAAS,KAAE;AAAE,IAAE,gBAAc,IAAE,SAASA,IAAE;AAAC,WAAO,GAAG,SAASA,GAAE,eAAcA,EAAC,KAAGA,GAAE,YAAY,CAAC,MAAIA,GAAE;AAAA,EAAa;AAAG,MAAI,KAAG,SAASA,IAAEC,IAAE;AAAC,WAAM,YAAUD,KAAEC,MAAGD,IAAG,MAAM,WAAS,OAAKA,GAAE,MAAM,WAAS,EAAEA,EAAC,KAAG,WAAS,GAAG,IAAIA,IAAE,SAAS;AAAA,EAAC;AAAE,WAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAE,IAAGC,KAAEJ,KAAE,WAAU;AAAC,aAAOA,GAAE,IAAI;AAAA,IAAC,IAAE,WAAU;AAAC,aAAO,GAAG,IAAIH,IAAEC,IAAE,EAAE;AAAA,IAAC,GAAEO,KAAED,GAAE,GAAEE,KAAEP,MAAGA,GAAE,CAAC,MAAI,GAAG,UAAUD,EAAC,IAAE,KAAG,OAAMkB,KAAEnB,GAAE,aAAW,GAAG,UAAUC,EAAC,KAAG,SAAOQ,MAAG,CAACD,OAAI,EAAE,KAAK,GAAG,IAAIR,IAAEC,EAAC,CAAC;AAAE,QAAGkB,MAAGA,GAAE,CAAC,MAAIV,IAAE;AAAC,MAAAD,MAAG,GAAEC,KAAEA,MAAGU,GAAE,CAAC,GAAEA,KAAE,CAACX,MAAG;AAAE,aAAMF,KAAI,IAAG,MAAMN,IAAEC,IAAEkB,KAAEV,EAAC,IAAG,IAAEJ,OAAI,KAAGA,KAAEE,GAAE,IAAEC,MAAG,SAAM,MAAIF,KAAE,IAAGa,MAAGd;AAAE,MAAAc,MAAG,GAAE,GAAG,MAAMnB,IAAEC,IAAEkB,KAAEV,EAAC,GAAEP,KAAEA,MAAG,CAAC;AAAA,IAAC;AAAC,WAAOA,OAAIiB,KAAE,CAACA,MAAG,CAACX,MAAG,GAAEJ,KAAEF,GAAE,CAAC,IAAEiB,MAAGjB,GAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,IAAE,CAACA,GAAE,CAAC,GAAEC,OAAIA,GAAE,OAAKM,IAAEN,GAAE,QAAMgB,IAAEhB,GAAE,MAAIC,MAAIA;AAAA,EAAC;AAAC,MAAI,KAAG,CAAC;AAAE,WAAS,GAAGJ,IAAEC,IAAE;AAAC,aAAQC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,CAAC,GAAEU,KAAE,GAAEE,KAAErB,GAAE,QAAOmB,KAAEE,IAAEF,KAAI,EAAChB,KAAEH,GAAEmB,EAAC,GAAG,UAAQjB,KAAEC,GAAE,MAAM,SAAQF,MAAG,WAASC,OAAIO,GAAEU,EAAC,IAAE,EAAE,IAAIhB,IAAE,SAAS,KAAG,MAAKM,GAAEU,EAAC,MAAIhB,GAAE,MAAM,UAAQ,MAAK,OAAKA,GAAE,MAAM,WAAS,GAAGA,EAAC,MAAIM,GAAEU,EAAC,KAAGX,KAAEF,KAAED,KAAE,QAAOC,MAAGF,KAAED,IAAG,eAAcI,KAAEH,GAAE,WAAUI,KAAE,GAAGD,EAAC,OAAKF,KAAEC,GAAE,KAAK,YAAYA,GAAE,cAAcC,EAAC,CAAC,GAAEC,KAAE,GAAG,IAAIH,IAAE,SAAS,GAAEA,GAAE,WAAW,YAAYA,EAAC,GAAE,WAASG,OAAIA,KAAE,UAAS,GAAGD,EAAC,IAAEC,SAAM,WAASN,OAAIO,GAAEU,EAAC,IAAE,QAAO,EAAE,IAAIhB,IAAE,WAAUD,EAAC;AAAI,SAAIiB,KAAE,GAAEA,KAAEE,IAAEF,KAAI,SAAMV,GAAEU,EAAC,MAAInB,GAAEmB,EAAC,EAAE,MAAM,UAAQV,GAAEU,EAAC;AAAG,WAAOnB;AAAA,EAAC;AAAC,KAAG,GAAG,OAAO,EAAC,MAAK,WAAU;AAAC,WAAO,GAAG,MAAK,IAAE;AAAA,EAAC,GAAE,MAAK,WAAU;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC,GAAE,QAAO,SAASA,IAAE;AAAC,WAAM,aAAW,OAAOA,KAAEA,KAAE,KAAK,KAAK,IAAE,KAAK,KAAK,IAAE,KAAK,KAAK,WAAU;AAAC,SAAG,IAAI,IAAE,GAAG,IAAI,EAAE,KAAK,IAAE,GAAG,IAAI,EAAE,KAAK;AAAA,IAAC,CAAC;AAAA,EAAC,EAAC,CAAC;AAAE,MAAI,IAAG,IAAG,KAAG,yBAAwB,KAAG,kCAAiC,KAAG;AAAqC,OAAG,EAAE,uBAAuB,EAAE,YAAY,EAAE,cAAc,KAAK,CAAC,IAAG,KAAG,EAAE,cAAc,OAAO,GAAG,aAAa,QAAO,OAAO,GAAE,GAAG,aAAa,WAAU,SAAS,GAAE,GAAG,aAAa,QAAO,GAAG,GAAE,GAAG,YAAY,EAAE,GAAE,GAAG,aAAW,GAAG,UAAU,IAAE,EAAE,UAAU,IAAE,EAAE,UAAU,SAAQ,GAAG,YAAU,0BAAyB,GAAG,iBAAe,CAAC,CAAC,GAAG,UAAU,IAAE,EAAE,UAAU,cAAa,GAAG,YAAU,qBAAoB,GAAG,SAAO,CAAC,CAAC,GAAG;AAAU,MAAI,KAAG,EAAC,OAAM,CAAC,GAAE,WAAU,UAAU,GAAE,KAAI,CAAC,GAAE,qBAAoB,qBAAqB,GAAE,IAAG,CAAC,GAAE,kBAAiB,kBAAkB,GAAE,IAAG,CAAC,GAAE,sBAAqB,uBAAuB,GAAE,UAAS,CAAC,GAAE,IAAG,EAAE,EAAC;AAAE,WAAS,GAAGA,IAAEC,IAAE;AAAC,QAAIC;AAAE,WAAOA,KAAE,eAAa,OAAOF,GAAE,uBAAqBA,GAAE,qBAAqBC,MAAG,GAAG,IAAE,eAAa,OAAOD,GAAE,mBAAiBA,GAAE,iBAAiBC,MAAG,GAAG,IAAE,CAAC,GAAE,WAASA,MAAGA,MAAG,GAAGD,IAAEC,EAAC,IAAE,GAAG,MAAM,CAACD,EAAC,GAAEE,EAAC,IAAEA;AAAA,EAAC;AAAC,WAAS,GAAGF,IAAEC,IAAE;AAAC,aAAQC,KAAE,GAAEC,KAAEH,GAAE,QAAOE,KAAEC,IAAED,KAAI,GAAE,IAAIF,GAAEE,EAAC,GAAE,cAAa,CAACD,MAAG,EAAE,IAAIA,GAAEC,EAAC,GAAE,YAAY,CAAC;AAAA,EAAC;AAAC,KAAG,QAAM,GAAG,QAAM,GAAG,WAAS,GAAG,UAAQ,GAAG,OAAM,GAAG,KAAG,GAAG,IAAG,GAAG,WAAS,GAAG,WAAS,GAAG,SAAO,CAAC,GAAE,gCAA+B,WAAW;AAAG,MAAI,KAAG;AAAY,WAAS,GAAGF,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,aAAQC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEU,IAAEE,KAAEpB,GAAE,uBAAuB,GAAEqB,KAAE,CAAC,GAAER,KAAE,GAAEM,KAAEpB,GAAE,QAAOc,KAAEM,IAAEN,KAAI,MAAIT,KAAEL,GAAEc,EAAC,MAAI,MAAIT,GAAE,KAAG,aAAW,EAAEA,EAAC,EAAE,IAAG,MAAMiB,IAAEjB,GAAE,WAAS,CAACA,EAAC,IAAEA,EAAC;AAAA,aAAU,GAAG,KAAKA,EAAC,GAAE;AAAC,MAAAC,KAAEA,MAAGe,GAAE,YAAYpB,GAAE,cAAc,KAAK,CAAC,GAAEM,MAAG,GAAG,KAAKF,EAAC,KAAG,CAAC,IAAG,EAAE,GAAG,CAAC,EAAE,YAAY,GAAEG,KAAE,GAAGD,EAAC,KAAG,GAAG,UAASD,GAAE,YAAUE,GAAE,CAAC,IAAE,GAAG,cAAcH,EAAC,IAAEG,GAAE,CAAC,GAAEW,KAAEX,GAAE,CAAC;AAAE,aAAMW,KAAI,CAAAb,KAAEA,GAAE;AAAU,SAAG,MAAMgB,IAAEhB,GAAE,UAAU,IAAGA,KAAEe,GAAE,YAAY,cAAY;AAAA,IAAE,MAAM,CAAAC,GAAE,KAAKrB,GAAE,eAAeI,EAAC,CAAC;AAAE,IAAAgB,GAAE,cAAY,IAAGP,KAAE;AAAE,WAAMT,KAAEiB,GAAER,IAAG,EAAE,KAAGX,MAAG,KAAG,GAAG,QAAQE,IAAEF,EAAC,EAAE,CAAAC,MAAGA,GAAE,KAAKC,EAAC;AAAA,aAAUI,KAAE,EAAEJ,EAAC,GAAEC,KAAE,GAAGe,GAAE,YAAYhB,EAAC,GAAE,QAAQ,GAAEI,MAAG,GAAGH,EAAC,GAAEJ,IAAE;AAAC,MAAAiB,KAAE;AAAE,aAAMd,KAAEC,GAAEa,IAAG,EAAE,IAAG,KAAKd,GAAE,QAAM,EAAE,KAAGH,GAAE,KAAKG,EAAC;AAAA,IAAC;AAAC,WAAOgB;AAAA,EAAC;AAAC,MAAI,KAAG;AAAsB,WAAS,KAAI;AAAC,WAAM;AAAA,EAAE;AAAC,WAAS,KAAI;AAAC,WAAM;AAAA,EAAE;AAAC,WAAS,GAAGrB,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC;AAAE,QAAG,YAAU,OAAON,IAAE;AAAC,WAAIM,MAAI,YAAU,OAAOL,OAAIC,KAAEA,MAAGD,IAAEA,KAAE,SAAQD,GAAE,IAAGD,IAAEO,IAAEL,IAAEC,IAAEF,GAAEM,EAAC,GAAEF,EAAC;AAAE,aAAOL;AAAA,IAAC;AAAC,QAAG,QAAMG,MAAG,QAAMC,MAAGA,KAAEF,IAAEC,KAAED,KAAE,UAAQ,QAAME,OAAI,YAAU,OAAOF,MAAGE,KAAED,IAAEA,KAAE,WAASC,KAAED,IAAEA,KAAED,IAAEA,KAAE,UAAS,UAAKE,GAAE,CAAAA,KAAE;AAAA,aAAW,CAACA,GAAE,QAAOJ;AAAE,WAAO,MAAIK,OAAIC,KAAEF,KAAGA,KAAE,SAASJ,IAAE;AAAC,aAAO,GAAG,EAAE,IAAIA,EAAC,GAAEM,GAAE,MAAM,MAAK,SAAS;AAAA,IAAC,GAAG,OAAKA,GAAE,SAAOA,GAAE,OAAK,GAAG,UAASN,GAAE,KAAK,WAAU;AAAC,SAAG,MAAM,IAAI,MAAKC,IAAEG,IAAED,IAAED,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC;AAAC,WAAS,GAAGF,IAAEG,IAAEF,IAAE;AAAC,IAAAA,MAAG,EAAE,IAAID,IAAEG,IAAE,KAAE,GAAE,GAAG,MAAM,IAAIH,IAAEG,IAAE,EAAC,WAAU,OAAG,SAAQ,SAASH,IAAE;AAAC,UAAIC,IAAEC,KAAE,EAAE,IAAI,MAAKC,EAAC;AAAE,UAAG,IAAEH,GAAE,aAAW,KAAKG,EAAC,GAAE;AAAC,YAAGD,GAAE,EAAC,GAAG,MAAM,QAAQC,EAAC,KAAG,CAAC,GAAG,gBAAcH,GAAE,gBAAgB;AAAA,iBAAUE,KAAE,GAAG,KAAK,SAAS,GAAE,EAAE,IAAI,MAAKC,IAAED,EAAC,GAAE,KAAKC,EAAC,EAAE,GAAEF,KAAE,EAAE,IAAI,MAAKE,EAAC,GAAE,EAAE,IAAI,MAAKA,IAAE,KAAE,GAAED,OAAID,GAAE,QAAOD,GAAE,yBAAyB,GAAEA,GAAE,eAAe,GAAEC;AAAA,MAAC,MAAM,CAAAC,OAAI,EAAE,IAAI,MAAKC,IAAE,GAAG,MAAM,QAAQD,GAAE,CAAC,GAAEA,GAAE,MAAM,CAAC,GAAE,IAAI,CAAC,GAAEF,GAAE,gBAAgB,GAAEA,GAAE,gCAA8B;AAAA,IAAG,EAAC,CAAC,KAAG,WAAS,EAAE,IAAIA,IAAEG,EAAC,KAAG,GAAG,MAAM,IAAIH,IAAEG,IAAE,EAAE;AAAA,EAAC;AAAC,KAAG,QAAM,EAAC,QAAO,CAAC,GAAE,KAAI,SAASF,IAAED,IAAEE,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEU,IAAEE,IAAEC,IAAER,IAAEM,IAAEG,IAAEC,KAAE,EAAE,IAAIvB,EAAC;AAAE,QAAG,EAAEA,EAAC,GAAE;AAAC,MAAAC,GAAE,YAAUA,MAAGG,KAAEH,IAAG,SAAQE,KAAEC,GAAE,WAAUD,MAAG,GAAG,KAAK,gBAAgB,GAAEA,EAAC,GAAEF,GAAE,SAAOA,GAAE,OAAK,GAAG,UAASM,KAAEgB,GAAE,YAAUhB,KAAEgB,GAAE,SAAO,uBAAO,OAAO,IAAI,KAAIlB,KAAEkB,GAAE,YAAUlB,KAAEkB,GAAE,SAAO,SAASxB,IAAE;AAAC,eAAM,eAAa,OAAO,MAAI,GAAG,MAAM,cAAYA,GAAE,OAAK,GAAG,MAAM,SAAS,MAAMC,IAAE,SAAS,IAAE;AAAA,MAAM,IAAGQ,MAAGT,MAAGA,MAAG,IAAI,MAAM,CAAC,KAAG,CAAC,EAAE,GAAG;AAAO,aAAMS,KAAI,CAAAK,KAAES,MAAGhB,KAAE,GAAG,KAAKP,GAAES,EAAC,CAAC,KAAG,CAAC,GAAG,CAAC,GAAEW,MAAGb,GAAE,CAAC,KAAG,IAAI,MAAM,GAAG,EAAE,KAAK,GAAEO,OAAIO,KAAE,GAAG,MAAM,QAAQP,EAAC,KAAG,CAAC,GAAEA,MAAGV,KAAEiB,GAAE,eAAaA,GAAE,aAAWP,IAAEO,KAAE,GAAG,MAAM,QAAQP,EAAC,KAAG,CAAC,GAAEK,KAAE,GAAG,OAAO,EAAC,MAAKL,IAAE,UAASS,IAAE,MAAKpB,IAAE,SAAQD,IAAE,MAAKA,GAAE,MAAK,UAASE,IAAE,cAAaA,MAAG,GAAG,KAAK,MAAM,aAAa,KAAKA,EAAC,GAAE,WAAUgB,GAAE,KAAK,GAAG,EAAC,GAAEf,EAAC,IAAGiB,KAAEd,GAAEM,EAAC,QAAMQ,KAAEd,GAAEM,EAAC,IAAE,CAAC,GAAG,gBAAc,GAAEO,GAAE,SAAO,UAAKA,GAAE,MAAM,KAAKpB,IAAEE,IAAEiB,IAAEd,EAAC,KAAGL,GAAE,oBAAkBA,GAAE,iBAAiBa,IAAER,EAAC,IAAGe,GAAE,QAAMA,GAAE,IAAI,KAAKpB,IAAEkB,EAAC,GAAEA,GAAE,QAAQ,SAAOA,GAAE,QAAQ,OAAKjB,GAAE,QAAOE,KAAEkB,GAAE,OAAOA,GAAE,iBAAgB,GAAEH,EAAC,IAAEG,GAAE,KAAKH,EAAC,GAAE,GAAG,MAAM,OAAOL,EAAC,IAAE;AAAA,IAAG;AAAA,EAAC,GAAE,QAAO,SAASd,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEU,IAAEE,IAAEC,IAAER,IAAEM,IAAEG,IAAEC,KAAE,EAAE,QAAQxB,EAAC,KAAG,EAAE,IAAIA,EAAC;AAAE,QAAGwB,OAAIhB,KAAEgB,GAAE,SAAQ;AAAC,MAAAf,MAAGR,MAAGA,MAAG,IAAI,MAAM,CAAC,KAAG,CAAC,EAAE,GAAG;AAAO,aAAMQ,KAAI,KAAGK,KAAES,MAAGhB,KAAE,GAAG,KAAKN,GAAEQ,EAAC,CAAC,KAAG,CAAC,GAAG,CAAC,GAAEW,MAAGb,GAAE,CAAC,KAAG,IAAI,MAAM,GAAG,EAAE,KAAK,GAAEO,IAAE;AAAC,QAAAO,KAAE,GAAG,MAAM,QAAQP,EAAC,KAAG,CAAC,GAAEQ,KAAEd,GAAEM,MAAGX,KAAEkB,GAAE,eAAaA,GAAE,aAAWP,EAAC,KAAG,CAAC,GAAEP,KAAEA,GAAE,CAAC,KAAG,IAAI,OAAO,YAAUa,GAAE,KAAK,eAAe,IAAE,SAAS,GAAEd,KAAED,KAAEiB,GAAE;AAAO,eAAMjB,KAAI,CAAAc,KAAEG,GAAEjB,EAAC,GAAE,CAACD,MAAGmB,OAAIJ,GAAE,YAAUjB,MAAGA,GAAE,SAAOiB,GAAE,QAAMZ,MAAG,CAACA,GAAE,KAAKY,GAAE,SAAS,KAAGhB,MAAGA,OAAIgB,GAAE,aAAW,SAAOhB,MAAG,CAACgB,GAAE,cAAYG,GAAE,OAAOjB,IAAE,CAAC,GAAEc,GAAE,YAAUG,GAAE,iBAAgBD,GAAE,UAAQA,GAAE,OAAO,KAAKrB,IAAEmB,EAAC;AAAG,QAAAb,MAAG,CAACgB,GAAE,WAASD,GAAE,YAAU,UAAKA,GAAE,SAAS,KAAKrB,IAAEoB,IAAEI,GAAE,MAAM,KAAG,GAAG,YAAYxB,IAAEc,IAAEU,GAAE,MAAM,GAAE,OAAOhB,GAAEM,EAAC;AAAA,MAAE,MAAM,MAAIA,MAAKN,GAAE,IAAG,MAAM,OAAOR,IAAEc,KAAEb,GAAEQ,EAAC,GAAEP,IAAEC,IAAE,IAAE;AAAE,SAAG,cAAcK,EAAC,KAAG,EAAE,OAAOR,IAAE,eAAe;AAAA,IAAC;AAAA,EAAC,GAAE,UAAS,SAASA,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,IAAI,MAAM,UAAU,MAAM,GAAEC,KAAE,GAAG,MAAM,IAAIR,EAAC,GAAES,MAAG,EAAE,IAAI,MAAK,QAAQ,KAAG,uBAAO,OAAO,IAAI,GAAGD,GAAE,IAAI,KAAG,CAAC,GAAEW,KAAE,GAAG,MAAM,QAAQX,GAAE,IAAI,KAAG,CAAC;AAAE,SAAID,GAAE,CAAC,IAAEC,IAAEP,KAAE,GAAEA,KAAE,UAAU,QAAOA,KAAI,CAAAM,GAAEN,EAAC,IAAE,UAAUA,EAAC;AAAE,QAAGO,GAAE,iBAAe,MAAK,CAACW,GAAE,eAAa,UAAKA,GAAE,YAAY,KAAK,MAAKX,EAAC,GAAE;AAAC,MAAAF,KAAE,GAAG,MAAM,SAAS,KAAK,MAAKE,IAAEC,EAAC,GAAER,KAAE;AAAE,cAAOG,KAAEE,GAAEL,IAAG,MAAI,CAACO,GAAE,qBAAqB,GAAE;AAAC,QAAAA,GAAE,gBAAcJ,GAAE,MAAKF,KAAE;AAAE,gBAAOG,KAAED,GAAE,SAASF,IAAG,MAAI,CAACM,GAAE,8BAA8B,EAAE,CAAAA,GAAE,cAAY,UAAKH,GAAE,aAAW,CAACG,GAAE,WAAW,KAAKH,GAAE,SAAS,MAAIG,GAAE,YAAUH,IAAEG,GAAE,OAAKH,GAAE,MAAK,YAAUF,OAAI,GAAG,MAAM,QAAQE,GAAE,QAAQ,KAAG,CAAC,GAAG,UAAQA,GAAE,SAAS,MAAMD,GAAE,MAAKG,EAAC,MAAI,WAAMC,GAAE,SAAOL,QAAKK,GAAE,eAAe,GAAEA,GAAE,gBAAgB;AAAA,MAAG;AAAC,aAAOW,GAAE,gBAAcA,GAAE,aAAa,KAAK,MAAKX,EAAC,GAAEA,GAAE;AAAA,IAAM;AAAA,EAAC,GAAE,UAAS,SAASR,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,CAAC,GAAEC,KAAEP,GAAE,eAAcQ,KAAET,GAAE;AAAO,QAAGQ,MAAGC,GAAE,YAAU,EAAE,YAAUT,GAAE,QAAM,KAAGA,GAAE;AAAQ,aAAKS,OAAI,MAAKA,KAAEA,GAAE,cAAY,KAAK,KAAG,MAAIA,GAAE,aAAW,YAAUT,GAAE,QAAM,SAAKS,GAAE,WAAU;AAAC,aAAIJ,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEJ,KAAE,GAAEA,KAAEM,IAAEN,KAAI,YAASI,GAAEF,MAAGD,KAAEF,GAAEC,EAAC,GAAG,WAAS,GAAG,MAAII,GAAEF,EAAC,IAAED,GAAE,eAAa,KAAG,GAAGC,IAAE,IAAI,EAAE,MAAMK,EAAC,IAAE,GAAG,KAAKL,IAAE,MAAK,MAAK,CAACK,EAAC,CAAC,EAAE,SAAQH,GAAEF,EAAC,KAAGC,GAAE,KAAKF,EAAC;AAAE,QAAAE,GAAE,UAAQE,GAAE,KAAK,EAAC,MAAKE,IAAE,UAASJ,GAAC,CAAC;AAAA,MAAC;AAAA;AAAC,WAAOI,KAAE,MAAKD,KAAEP,GAAE,UAAQM,GAAE,KAAK,EAAC,MAAKE,IAAE,UAASR,GAAE,MAAMO,EAAC,EAAC,CAAC,GAAED;AAAA,EAAC,GAAE,SAAQ,SAASN,IAAED,IAAE;AAAC,WAAO,eAAe,GAAG,MAAM,WAAUC,IAAE,EAAC,YAAW,MAAG,cAAa,MAAG,KAAI,EAAED,EAAC,IAAE,WAAU;AAAC,UAAG,KAAK,cAAc,QAAOA,GAAE,KAAK,aAAa;AAAA,IAAC,IAAE,WAAU;AAAC,UAAG,KAAK,cAAc,QAAO,KAAK,cAAcC,EAAC;AAAA,IAAC,GAAE,KAAI,SAASD,IAAE;AAAC,aAAO,eAAe,MAAKC,IAAE,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAMD,GAAC,CAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC,GAAE,KAAI,SAASA,IAAE;AAAC,WAAOA,GAAE,GAAG,OAAO,IAAEA,KAAE,IAAI,GAAG,MAAMA,EAAC;AAAA,EAAC,GAAE,SAAQ,EAAC,MAAK,EAAC,UAAS,KAAE,GAAE,OAAM,EAAC,OAAM,SAASA,IAAE;AAAC,QAAIC,KAAE,QAAMD;AAAE,WAAO,GAAG,KAAKC,GAAE,IAAI,KAAGA,GAAE,SAAO,GAAGA,IAAE,OAAO,KAAG,GAAGA,IAAE,SAAQ,IAAE,GAAE;AAAA,EAAE,GAAE,SAAQ,SAASD,IAAE;AAAC,QAAIC,KAAE,QAAMD;AAAE,WAAO,GAAG,KAAKC,GAAE,IAAI,KAAGA,GAAE,SAAO,GAAGA,IAAE,OAAO,KAAG,GAAGA,IAAE,OAAO,GAAE;AAAA,EAAE,GAAE,UAAS,SAASD,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAO,WAAO,GAAG,KAAKC,GAAE,IAAI,KAAGA,GAAE,SAAO,GAAGA,IAAE,OAAO,KAAG,EAAE,IAAIA,IAAE,OAAO,KAAG,GAAGA,IAAE,GAAG;AAAA,EAAC,EAAC,GAAE,cAAa,EAAC,cAAa,SAASD,IAAE;AAAC,eAASA,GAAE,UAAQA,GAAE,kBAAgBA,GAAE,cAAc,cAAYA,GAAE;AAAA,EAAO,EAAC,EAAC,EAAC,GAAE,GAAG,cAAY,SAASA,IAAEC,IAAEC,IAAE;AAAC,IAAAF,GAAE,uBAAqBA,GAAE,oBAAoBC,IAAEC,EAAC;AAAA,EAAC,GAAE,GAAG,QAAM,SAASF,IAAEC,IAAE;AAAC,QAAG,EAAE,gBAAgB,GAAG,OAAO,QAAO,IAAI,GAAG,MAAMD,IAAEC,EAAC;AAAE,IAAAD,MAAGA,GAAE,QAAM,KAAK,gBAAcA,IAAE,KAAK,OAAKA,GAAE,MAAK,KAAK,qBAAmBA,GAAE,oBAAkB,WAASA,GAAE,oBAAkB,UAAKA,GAAE,cAAY,KAAG,IAAG,KAAK,SAAOA,GAAE,UAAQ,MAAIA,GAAE,OAAO,WAASA,GAAE,OAAO,aAAWA,GAAE,QAAO,KAAK,gBAAcA,GAAE,eAAc,KAAK,gBAAcA,GAAE,iBAAe,KAAK,OAAKA,IAAEC,MAAG,GAAG,OAAO,MAAKA,EAAC,GAAE,KAAK,YAAUD,MAAGA,GAAE,aAAW,KAAK,IAAI,GAAE,KAAK,GAAG,OAAO,IAAE;AAAA,EAAE,GAAE,GAAG,MAAM,YAAU,EAAC,aAAY,GAAG,OAAM,oBAAmB,IAAG,sBAAqB,IAAG,+BAA8B,IAAG,aAAY,OAAG,gBAAe,WAAU;AAAC,QAAIA,KAAE,KAAK;AAAc,SAAK,qBAAmB,IAAGA,MAAG,CAAC,KAAK,eAAaA,GAAE,eAAe;AAAA,EAAC,GAAE,iBAAgB,WAAU;AAAC,QAAIA,KAAE,KAAK;AAAc,SAAK,uBAAqB,IAAGA,MAAG,CAAC,KAAK,eAAaA,GAAE,gBAAgB;AAAA,EAAC,GAAE,0BAAyB,WAAU;AAAC,QAAIA,KAAE,KAAK;AAAc,SAAK,gCAA8B,IAAGA,MAAG,CAAC,KAAK,eAAaA,GAAE,yBAAyB,GAAE,KAAK,gBAAgB;AAAA,EAAC,EAAC,GAAE,GAAG,KAAK,EAAC,QAAO,MAAG,SAAQ,MAAG,YAAW,MAAG,gBAAe,MAAG,SAAQ,MAAG,QAAO,MAAG,YAAW,MAAG,SAAQ,MAAG,OAAM,MAAG,OAAM,MAAG,UAAS,MAAG,MAAK,MAAG,QAAO,MAAG,MAAK,MAAG,UAAS,MAAG,KAAI,MAAG,SAAQ,MAAG,QAAO,MAAG,SAAQ,MAAG,SAAQ,MAAG,SAAQ,MAAG,SAAQ,MAAG,SAAQ,MAAG,WAAU,MAAG,aAAY,MAAG,SAAQ,MAAG,SAAQ,MAAG,eAAc,MAAG,WAAU,MAAG,SAAQ,MAAG,OAAM,KAAE,GAAE,GAAG,MAAM,OAAO,GAAE,GAAG,KAAK,EAAC,OAAM,WAAU,MAAK,WAAU,GAAE,SAASG,IAAEC,IAAE;AAAC,aAASC,GAAEL,IAAE;AAAC,UAAG,EAAE,cAAa;AAAC,YAAIC,KAAE,EAAE,IAAI,MAAK,QAAQ,GAAEC,KAAE,GAAG,MAAM,IAAIF,EAAC;AAAE,QAAAE,GAAE,OAAK,cAAYF,GAAE,OAAK,UAAQ,QAAOE,GAAE,cAAY,MAAGD,GAAED,EAAC,GAAEE,GAAE,WAASA,GAAE,iBAAeD,GAAEC,EAAC;AAAA,MAAC,MAAM,IAAG,MAAM,SAASE,IAAEJ,GAAE,QAAO,GAAG,MAAM,IAAIA,EAAC,CAAC;AAAA,IAAC;AAAC,OAAG,MAAM,QAAQG,EAAC,IAAE,EAAC,OAAM,WAAU;AAAC,UAAIH;AAAE,UAAG,GAAG,MAAKG,IAAE,IAAE,GAAE,CAAC,EAAE,aAAa,QAAM;AAAG,OAACH,KAAE,EAAE,IAAI,MAAKI,EAAC,MAAI,KAAK,iBAAiBA,IAAEC,EAAC,GAAE,EAAE,IAAI,MAAKD,KAAGJ,MAAG,KAAG,CAAC;AAAA,IAAC,GAAE,SAAQ,WAAU;AAAC,aAAO,GAAG,MAAKG,EAAC,GAAE;AAAA,IAAE,GAAE,UAAS,WAAU;AAAC,UAAIH;AAAE,UAAG,CAAC,EAAE,aAAa,QAAM;AAAG,OAACA,KAAE,EAAE,IAAI,MAAKI,EAAC,IAAE,KAAG,EAAE,IAAI,MAAKA,IAAEJ,EAAC,KAAG,KAAK,oBAAoBI,IAAEC,EAAC,GAAE,EAAE,OAAO,MAAKD,EAAC;AAAA,IAAE,GAAE,UAAS,SAASJ,IAAE;AAAC,aAAO,EAAE,IAAIA,GAAE,QAAOG,EAAC;AAAA,IAAC,GAAE,cAAaC,GAAC,GAAE,GAAG,MAAM,QAAQA,EAAC,IAAE,EAAC,OAAM,WAAU;AAAC,UAAIJ,KAAE,KAAK,iBAAe,KAAK,YAAU,MAAKC,KAAE,EAAE,eAAa,OAAKD,IAAEE,KAAE,EAAE,IAAID,IAAEG,EAAC;AAAE,MAAAF,OAAI,EAAE,eAAa,KAAK,iBAAiBE,IAAEC,EAAC,IAAEL,GAAE,iBAAiBG,IAAEE,IAAE,IAAE,IAAG,EAAE,IAAIJ,IAAEG,KAAGF,MAAG,KAAG,CAAC;AAAA,IAAC,GAAE,UAAS,WAAU;AAAC,UAAIF,KAAE,KAAK,iBAAe,KAAK,YAAU,MAAKC,KAAE,EAAE,eAAa,OAAKD,IAAEE,KAAE,EAAE,IAAID,IAAEG,EAAC,IAAE;AAAE,MAAAF,KAAE,EAAE,IAAID,IAAEG,IAAEF,EAAC,KAAG,EAAE,eAAa,KAAK,oBAAoBE,IAAEC,EAAC,IAAEL,GAAE,oBAAoBG,IAAEE,IAAE,IAAE,GAAE,EAAE,OAAOJ,IAAEG,EAAC;AAAA,IAAE,EAAC;AAAA,EAAC,CAAC,GAAE,GAAG,KAAK,EAAC,YAAW,aAAY,YAAW,YAAW,cAAa,eAAc,cAAa,aAAY,GAAE,SAASJ,IAAEI,IAAE;AAAC,OAAG,MAAM,QAAQJ,EAAC,IAAE,EAAC,cAAaI,IAAE,UAASA,IAAE,QAAO,SAASJ,IAAE;AAAC,UAAIC,IAAEC,KAAEF,GAAE,eAAcG,KAAEH,GAAE;AAAU,aAAOE,OAAIA,OAAI,QAAM,GAAG,SAAS,MAAKA,EAAC,OAAKF,GAAE,OAAKG,GAAE,UAASF,KAAEE,GAAE,QAAQ,MAAM,MAAK,SAAS,GAAEH,GAAE,OAAKI,KAAGH;AAAA,IAAC,EAAC;AAAA,EAAC,CAAC,GAAE,GAAG,GAAG,OAAO,EAAC,IAAG,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAG,MAAKH,IAAEC,IAAEC,IAAEC,EAAC;AAAA,EAAC,GAAE,KAAI,SAASH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAG,MAAKH,IAAEC,IAAEC,IAAEC,IAAE,CAAC;AAAA,EAAC,GAAE,KAAI,SAASH,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC;AAAE,QAAGJ,MAAGA,GAAE,kBAAgBA,GAAE,UAAU,QAAOG,KAAEH,GAAE,WAAU,GAAGA,GAAE,cAAc,EAAE,IAAIG,GAAE,YAAUA,GAAE,WAAS,MAAIA,GAAE,YAAUA,GAAE,UAASA,GAAE,UAASA,GAAE,OAAO,GAAE;AAAK,QAAG,YAAU,OAAOH,IAAE;AAAC,WAAII,MAAKJ,GAAE,MAAK,IAAII,IAAEH,IAAED,GAAEI,EAAC,CAAC;AAAE,aAAO;AAAA,IAAI;AAAC,WAAM,UAAKH,MAAG,cAAY,OAAOA,OAAIC,KAAED,IAAEA,KAAE,SAAQ,UAAKC,OAAIA,KAAE,KAAI,KAAK,KAAK,WAAU;AAAC,SAAG,MAAM,OAAO,MAAKF,IAAEE,IAAED,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,EAAC,CAAC;AAAE,MAAI,KAAG,yBAAwB,KAAG,qCAAoC,KAAG;AAA6B,WAAS,GAAGD,IAAEC,IAAE;AAAC,WAAO,GAAGD,IAAE,OAAO,KAAG,GAAG,OAAKC,GAAE,WAASA,KAAEA,GAAE,YAAW,IAAI,KAAG,GAAGD,EAAC,EAAE,SAAS,OAAO,EAAE,CAAC,KAAGA;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAE;AAAC,WAAOA,GAAE,QAAM,SAAOA,GAAE,aAAa,MAAM,KAAG,MAAIA,GAAE,MAAKA;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAE;AAAC,WAAM,aAAWA,GAAE,QAAM,IAAI,MAAM,GAAE,CAAC,IAAEA,GAAE,OAAKA,GAAE,KAAK,MAAM,CAAC,IAAEA,GAAE,gBAAgB,MAAM,GAAEA;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC;AAAE,QAAG,MAAIN,GAAE,UAAS;AAAC,UAAG,EAAE,QAAQD,EAAC,MAAIO,KAAE,EAAE,IAAIP,EAAC,EAAE,QAAQ,MAAII,MAAK,EAAE,OAAOH,IAAE,eAAe,GAAEM,GAAE,MAAIL,KAAE,GAAEC,KAAEI,GAAEH,EAAC,EAAE,QAAOF,KAAEC,IAAED,KAAI,IAAG,MAAM,IAAID,IAAEG,IAAEG,GAAEH,EAAC,EAAEF,EAAC,CAAC;AAAE,QAAE,QAAQF,EAAC,MAAIK,KAAE,EAAE,OAAOL,EAAC,GAAEM,KAAE,GAAG,OAAO,CAAC,GAAED,EAAC,GAAE,EAAE,IAAIJ,IAAEK,EAAC;AAAA,IAAE;AAAA,EAAC;AAAC,WAAS,GAAGJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,IAAAF,KAAE,EAAEA,EAAC;AAAE,QAAIH,IAAEC,IAAEK,IAAEC,IAAEC,IAAEC,IAAEU,KAAE,GAAEE,KAAEnB,GAAE,QAAOoB,KAAED,KAAE,GAAEP,KAAEX,GAAE,CAAC,GAAEiB,KAAE,EAAEN,EAAC;AAAE,QAAGM,MAAG,IAAEC,MAAG,YAAU,OAAOP,MAAG,CAAC,GAAG,cAAY,GAAG,KAAKA,EAAC,EAAE,QAAOZ,GAAE,KAAK,SAASF,IAAE;AAAC,UAAIC,KAAEC,GAAE,GAAGF,EAAC;AAAE,MAAAoB,OAAIjB,GAAE,CAAC,IAAEW,GAAE,KAAK,MAAKd,IAAEC,GAAE,KAAK,CAAC,IAAG,GAAGA,IAAEE,IAAEC,IAAEC,EAAC;AAAA,IAAC,CAAC;AAAE,QAAGgB,OAAIpB,MAAGD,KAAE,GAAGG,IAAED,GAAE,CAAC,EAAE,eAAc,OAAGA,IAAEG,EAAC,GAAG,YAAW,MAAIL,GAAE,WAAW,WAASA,KAAEC,KAAGA,MAAGI,KAAG;AAAC,WAAIE,MAAGD,KAAE,GAAG,IAAI,GAAGN,IAAE,QAAQ,GAAE,EAAE,GAAG,QAAOmB,KAAEE,IAAEF,KAAI,CAAAX,KAAER,IAAEmB,OAAIG,OAAId,KAAE,GAAG,MAAMA,IAAE,MAAG,IAAE,GAAED,MAAG,GAAG,MAAMD,IAAE,GAAGE,IAAE,QAAQ,CAAC,IAAGJ,GAAE,KAAKF,GAAEiB,EAAC,GAAEX,IAAEW,EAAC;AAAE,UAAGZ,GAAE,MAAIE,KAAEH,GAAEA,GAAE,SAAO,CAAC,EAAE,eAAc,GAAG,IAAIA,IAAE,EAAE,GAAEa,KAAE,GAAEA,KAAEZ,IAAEY,KAAI,CAAAX,KAAEF,GAAEa,EAAC,GAAE,GAAG,KAAKX,GAAE,QAAM,EAAE,KAAG,CAAC,EAAE,OAAOA,IAAE,YAAY,KAAG,GAAG,SAASC,IAAED,EAAC,MAAIA,GAAE,OAAK,cAAYA,GAAE,QAAM,IAAI,YAAY,IAAE,GAAG,YAAU,CAACA,GAAE,YAAU,GAAG,SAASA,GAAE,KAAI,EAAC,OAAMA,GAAE,SAAOA,GAAE,aAAa,OAAO,EAAC,GAAEC,EAAC,IAAE,EAAED,GAAE,YAAY,QAAQ,IAAG,EAAE,GAAEA,IAAEC,EAAC;AAAA,IAAE;AAAC,WAAOP;AAAA,EAAC;AAAC,WAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,aAAQC,IAAEC,KAAEH,KAAE,GAAG,OAAOA,IAAED,EAAC,IAAEA,IAAEK,KAAE,GAAE,SAAOF,KAAEC,GAAEC,EAAC,IAAGA,KAAI,CAAAH,MAAG,MAAIC,GAAE,YAAU,GAAG,UAAU,GAAGA,EAAC,CAAC,GAAEA,GAAE,eAAaD,MAAG,EAAEC,EAAC,KAAG,GAAG,GAAGA,IAAE,QAAQ,CAAC,GAAEA,GAAE,WAAW,YAAYA,EAAC;AAAG,WAAOH;AAAA,EAAC;AAAC,KAAG,OAAO,EAAC,eAAc,SAASA,IAAE;AAAC,WAAOA;AAAA,EAAC,GAAE,OAAM,SAASA,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEU,KAAEnB,GAAE,UAAU,IAAE,GAAEqB,KAAE,EAAErB,EAAC;AAAE,QAAG,EAAE,GAAG,kBAAgB,MAAIA,GAAE,YAAU,OAAKA,GAAE,YAAU,GAAG,SAASA,EAAC,GAAG,MAAIM,KAAE,GAAGa,EAAC,GAAEhB,KAAE,GAAEC,MAAGC,KAAE,GAAGL,EAAC,GAAG,QAAOG,KAAEC,IAAED,KAAI,CAAAI,KAAEF,GAAEF,EAAC,GAAEK,KAAEF,GAAEH,EAAC,GAAE,QAAO,aAAWM,KAAED,GAAE,SAAS,YAAY,MAAI,GAAG,KAAKD,GAAE,IAAI,IAAEC,GAAE,UAAQD,GAAE,UAAQ,YAAUE,MAAG,eAAaA,OAAID,GAAE,eAAaD,GAAE;AAAc,QAAGN,GAAE,KAAGC,GAAE,MAAIG,KAAEA,MAAG,GAAGL,EAAC,GAAEM,KAAEA,MAAG,GAAGa,EAAC,GAAEhB,KAAE,GAAEC,KAAEC,GAAE,QAAOF,KAAEC,IAAED,KAAI,IAAGE,GAAEF,EAAC,GAAEG,GAAEH,EAAC,CAAC;AAAA,QAAO,IAAGH,IAAEmB,EAAC;AAAE,WAAO,KAAGb,KAAE,GAAGa,IAAE,QAAQ,GAAG,UAAQ,GAAGb,IAAE,CAACe,MAAG,GAAGrB,IAAE,QAAQ,CAAC,GAAEmB;AAAA,EAAC,GAAE,WAAU,SAASnB,IAAE;AAAC,aAAQC,IAAEC,IAAEC,IAAEC,KAAE,GAAG,MAAM,SAAQC,KAAE,GAAE,YAAUH,KAAEF,GAAEK,EAAC,IAAGA,KAAI,KAAG,EAAEH,EAAC,GAAE;AAAC,UAAGD,KAAEC,GAAE,EAAE,OAAO,GAAE;AAAC,YAAGD,GAAE,OAAO,MAAIE,MAAKF,GAAE,OAAO,CAAAG,GAAED,EAAC,IAAE,GAAG,MAAM,OAAOD,IAAEC,EAAC,IAAE,GAAG,YAAYD,IAAEC,IAAEF,GAAE,MAAM;AAAE,QAAAC,GAAE,EAAE,OAAO,IAAE;AAAA,MAAM;AAAC,MAAAA,GAAE,EAAE,OAAO,MAAIA,GAAE,EAAE,OAAO,IAAE;AAAA,IAAO;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,GAAG,OAAO,EAAC,QAAO,SAASF,IAAE;AAAC,WAAO,GAAG,MAAKA,IAAE,IAAE;AAAA,EAAC,GAAE,QAAO,SAASA,IAAE;AAAC,WAAO,GAAG,MAAKA,EAAC;AAAA,EAAC,GAAE,MAAK,SAASA,IAAE;AAAC,WAAO,EAAE,MAAK,SAASA,IAAE;AAAC,aAAO,WAASA,KAAE,GAAG,KAAK,IAAI,IAAE,KAAK,MAAM,EAAE,KAAK,WAAU;AAAC,cAAI,KAAK,YAAU,OAAK,KAAK,YAAU,MAAI,KAAK,aAAW,KAAK,cAAYA;AAAA,MAAE,CAAC;AAAA,IAAC,GAAE,MAAKA,IAAE,UAAU,MAAM;AAAA,EAAC,GAAE,QAAO,WAAU;AAAC,WAAO,GAAG,MAAK,WAAU,SAASA,IAAE;AAAC,YAAI,KAAK,YAAU,OAAK,KAAK,YAAU,MAAI,KAAK,YAAU,GAAG,MAAKA,EAAC,EAAE,YAAYA,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,SAAQ,WAAU;AAAC,WAAO,GAAG,MAAK,WAAU,SAASA,IAAE;AAAC,UAAG,MAAI,KAAK,YAAU,OAAK,KAAK,YAAU,MAAI,KAAK,UAAS;AAAC,YAAIC,KAAE,GAAG,MAAKD,EAAC;AAAE,QAAAC,GAAE,aAAaD,IAAEC,GAAE,UAAU;AAAA,MAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,QAAO,WAAU;AAAC,WAAO,GAAG,MAAK,WAAU,SAASD,IAAE;AAAC,WAAK,cAAY,KAAK,WAAW,aAAaA,IAAE,IAAI;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,OAAM,WAAU;AAAC,WAAO,GAAG,MAAK,WAAU,SAASA,IAAE;AAAC,WAAK,cAAY,KAAK,WAAW,aAAaA,IAAE,KAAK,WAAW;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,OAAM,WAAU;AAAC,aAAQA,IAAEC,KAAE,GAAE,SAAOD,KAAE,KAAKC,EAAC,IAAGA,KAAI,OAAID,GAAE,aAAW,GAAG,UAAU,GAAGA,IAAE,KAAE,CAAC,GAAEA,GAAE,cAAY;AAAI,WAAO;AAAA,EAAI,GAAE,OAAM,SAASA,IAAEC,IAAE;AAAC,WAAOD,KAAE,QAAMA,MAAGA,IAAEC,KAAE,QAAMA,KAAED,KAAEC,IAAE,KAAK,IAAI,WAAU;AAAC,aAAO,GAAG,MAAM,MAAKD,IAAEC,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,MAAK,SAASD,IAAE;AAAC,WAAO,EAAE,MAAK,SAASA,IAAE;AAAC,UAAIC,KAAE,KAAK,CAAC,KAAG,CAAC,GAAEC,KAAE,GAAEC,KAAE,KAAK;AAAO,UAAG,WAASH,MAAG,MAAIC,GAAE,SAAS,QAAOA,GAAE;AAAU,UAAG,YAAU,OAAOD,MAAG,CAAC,GAAG,KAAKA,EAAC,KAAG,CAAC,IAAI,GAAG,KAAKA,EAAC,KAAG,CAAC,IAAG,EAAE,GAAG,CAAC,EAAE,YAAY,CAAC,GAAE;AAAC,QAAAA,KAAE,GAAG,cAAcA,EAAC;AAAE,YAAG;AAAC,iBAAKE,KAAEC,IAAED,KAAI,QAAKD,KAAE,KAAKC,EAAC,KAAG,CAAC,GAAG,aAAW,GAAG,UAAU,GAAGD,IAAE,KAAE,CAAC,GAAEA,GAAE,YAAUD;AAAG,UAAAC,KAAE;AAAA,QAAC,SAAOD,IAAE;AAAA,QAAC;AAAA,MAAC;AAAC,MAAAC,MAAG,KAAK,MAAM,EAAE,OAAOD,EAAC;AAAA,IAAC,GAAE,MAAKA,IAAE,UAAU,MAAM;AAAA,EAAC,GAAE,aAAY,WAAU;AAAC,QAAIE,KAAE,CAAC;AAAE,WAAO,GAAG,MAAK,WAAU,SAASF,IAAE;AAAC,UAAIC,KAAE,KAAK;AAAW,SAAG,QAAQ,MAAKC,EAAC,IAAE,MAAI,GAAG,UAAU,GAAG,IAAI,CAAC,GAAED,MAAGA,GAAE,aAAaD,IAAE,IAAI;AAAA,IAAE,GAAEE,EAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,KAAK,EAAC,UAAS,UAAS,WAAU,WAAU,cAAa,UAAS,aAAY,SAAQ,YAAW,cAAa,GAAE,SAASF,IAAEM,IAAE;AAAC,OAAG,GAAGN,EAAC,IAAE,SAASA,IAAE;AAAC,eAAQC,IAAEC,KAAE,CAAC,GAAEC,KAAE,GAAGH,EAAC,GAAEI,KAAED,GAAE,SAAO,GAAEE,KAAE,GAAEA,MAAGD,IAAEC,KAAI,CAAAJ,KAAEI,OAAID,KAAE,OAAK,KAAK,MAAM,IAAE,GAAE,GAAGD,GAAEE,EAAC,CAAC,EAAEC,EAAC,EAAEL,EAAC,GAAE,EAAE,MAAMC,IAAED,GAAE,IAAI,CAAC;AAAE,aAAO,KAAK,UAAUC,EAAC;AAAA,IAAC;AAAA,EAAC,CAAC;AAAE,MAAI,KAAG,IAAI,OAAO,OAAK,IAAE,mBAAkB,GAAG,GAAE,KAAG,OAAM,KAAG,SAASF,IAAE;AAAC,QAAIC,KAAED,GAAE,cAAc;AAAY,WAAOC,MAAGA,GAAE,WAASA,KAAE,KAAIA,GAAE,iBAAiBD,EAAC;AAAA,EAAC,GAAE,KAAG,SAASA,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAE,CAAC;AAAE,SAAID,MAAKH,GAAE,CAAAI,GAAED,EAAC,IAAEJ,GAAE,MAAMI,EAAC,GAAEJ,GAAE,MAAMI,EAAC,IAAEH,GAAEG,EAAC;AAAE,SAAIA,MAAKD,KAAED,GAAE,KAAKF,EAAC,GAAEC,GAAE,CAAAD,GAAE,MAAMI,EAAC,IAAEC,GAAED,EAAC;AAAE,WAAOD;AAAA,EAAC,GAAE,KAAG,IAAI,OAAO,EAAE,KAAK,GAAG,GAAE,GAAG;AAAE,WAAS,GAAGH,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,GAAG,KAAKN,EAAC,GAAEO,KAAER,GAAE;AAAM,YAAOE,KAAEA,MAAG,GAAGF,EAAC,OAAKM,KAAEJ,GAAE,iBAAiBD,EAAC,KAAGC,GAAED,EAAC,GAAEM,MAAGD,OAAIA,KAAEA,GAAE,QAAQ,IAAG,IAAI,KAAG,SAAQ,OAAKA,MAAG,EAAEN,EAAC,MAAIM,KAAE,GAAG,MAAMN,IAAEC,EAAC,IAAG,CAAC,GAAG,eAAe,KAAG,GAAG,KAAKK,EAAC,KAAG,GAAG,KAAKL,EAAC,MAAIE,KAAEK,GAAE,OAAMJ,KAAEI,GAAE,UAASH,KAAEG,GAAE,UAASA,GAAE,WAASA,GAAE,WAASA,GAAE,QAAMF,IAAEA,KAAEJ,GAAE,OAAMM,GAAE,QAAML,IAAEK,GAAE,WAASJ,IAAEI,GAAE,WAASH,MAAI,WAASC,KAAEA,KAAE,KAAGA;AAAA,EAAC;AAAC,WAAS,GAAGN,IAAEC,IAAE;AAAC,WAAM,EAAC,KAAI,WAAU;AAAC,UAAG,CAACD,GAAE,EAAE,SAAO,KAAK,MAAIC,IAAG,MAAM,MAAK,SAAS;AAAE,aAAO,KAAK;AAAA,IAAG,EAAC;AAAA,EAAC;AAAC,GAAC,WAAU;AAAC,aAASD,KAAG;AAAC,UAAGS,IAAE;AAAC,QAAAD,GAAE,MAAM,UAAQ,gFAA+EC,GAAE,MAAM,UAAQ,6HAA4H,EAAE,YAAYD,EAAC,EAAE,YAAYC,EAAC;AAAE,YAAIT,KAAE,GAAG,iBAAiBS,EAAC;AAAE,QAAAP,KAAE,SAAOF,GAAE,KAAIO,KAAE,OAAKN,GAAED,GAAE,UAAU,GAAES,GAAE,MAAM,QAAM,OAAMJ,KAAE,OAAKJ,GAAED,GAAE,KAAK,GAAEG,KAAE,OAAKF,GAAED,GAAE,KAAK,GAAES,GAAE,MAAM,WAAS,YAAWL,KAAE,OAAKH,GAAEQ,GAAE,cAAY,CAAC,GAAE,EAAE,YAAYD,EAAC,GAAEC,KAAE;AAAA,MAAI;AAAA,IAAC;AAAC,aAASR,GAAED,IAAE;AAAC,aAAO,KAAK,MAAM,WAAWA,EAAC,CAAC;AAAA,IAAC;AAAC,QAAIE,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,EAAE,cAAc,KAAK,GAAEC,KAAE,EAAE,cAAc,KAAK;AAAE,IAAAA,GAAE,UAAQA,GAAE,MAAM,iBAAe,eAAcA,GAAE,UAAU,IAAE,EAAE,MAAM,iBAAe,IAAG,GAAG,kBAAgB,kBAAgBA,GAAE,MAAM,gBAAe,GAAG,OAAO,IAAG,EAAC,mBAAkB,WAAU;AAAC,aAAOT,GAAE,GAAEG;AAAA,IAAC,GAAE,gBAAe,WAAU;AAAC,aAAOH,GAAE,GAAEK;AAAA,IAAC,GAAE,eAAc,WAAU;AAAC,aAAOL,GAAE,GAAEE;AAAA,IAAC,GAAE,oBAAmB,WAAU;AAAC,aAAOF,GAAE,GAAEO;AAAA,IAAC,GAAE,eAAc,WAAU;AAAC,aAAOP,GAAE,GAAEI;AAAA,IAAC,GAAE,sBAAqB,WAAU;AAAC,UAAIJ,IAAEC,IAAEC,IAAEC;AAAE,aAAO,QAAMG,OAAIN,KAAE,EAAE,cAAc,OAAO,GAAEC,KAAE,EAAE,cAAc,IAAI,GAAEC,KAAE,EAAE,cAAc,KAAK,GAAEF,GAAE,MAAM,UAAQ,4DAA2DC,GAAE,MAAM,UAAQ,2CAA0CA,GAAE,MAAM,SAAO,OAAMC,GAAE,MAAM,SAAO,OAAMA,GAAE,MAAM,UAAQ,SAAQ,EAAE,YAAYF,EAAC,EAAE,YAAYC,EAAC,EAAE,YAAYC,EAAC,GAAEC,KAAE,GAAG,iBAAiBF,EAAC,GAAEK,KAAE,SAASH,GAAE,QAAO,EAAE,IAAE,SAASA,GAAE,gBAAe,EAAE,IAAE,SAASA,GAAE,mBAAkB,EAAE,MAAIF,GAAE,cAAa,EAAE,YAAYD,EAAC,IAAGM;AAAA,IAAC,EAAC,CAAC;AAAA,EAAE,EAAE;AAAE,MAAI,KAAG,CAAC,UAAS,OAAM,IAAI,GAAE,KAAG,EAAE,cAAc,KAAK,EAAE,OAAM,KAAG,CAAC;AAAE,WAAS,GAAGN,IAAE;AAAC,QAAIC,KAAE,GAAG,SAASD,EAAC,KAAG,GAAGA,EAAC;AAAE,WAAOC,OAAID,MAAK,KAAGA,KAAE,GAAGA,EAAC,IAAE,SAASA,IAAE;AAAC,UAAIC,KAAED,GAAE,CAAC,EAAE,YAAY,IAAEA,GAAE,MAAM,CAAC,GAAEE,KAAE,GAAG;AAAO,aAAMA,KAAI,MAAIF,KAAE,GAAGE,EAAC,IAAED,OAAK,GAAG,QAAOD;AAAA,IAAC,EAAEA,EAAC,KAAGA;AAAA,EAAE;AAAC,MAAI,KAAG,6BAA4B,KAAG,EAAC,UAAS,YAAW,YAAW,UAAS,SAAQ,QAAO,GAAE,KAAG,EAAC,eAAc,KAAI,YAAW,MAAK;AAAE,WAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAE,EAAE,KAAKF,EAAC;AAAE,WAAOE,KAAE,KAAK,IAAI,GAAEA,GAAE,CAAC,KAAGD,MAAG,EAAE,KAAGC,GAAE,CAAC,KAAG,QAAMF;AAAA,EAAC;AAAC,WAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAE,YAAUL,KAAE,IAAE,GAAEM,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,QAAGP,QAAKC,KAAE,WAAS,WAAW,QAAO;AAAE,WAAKG,KAAE,GAAEA,MAAG,EAAE,cAAWJ,OAAIO,MAAG,GAAG,IAAIT,IAAEE,KAAE,EAAEI,EAAC,GAAE,MAAGF,EAAC,IAAGD,MAAG,cAAYD,OAAIM,MAAG,GAAG,IAAIR,IAAE,YAAU,EAAEM,EAAC,GAAE,MAAGF,EAAC,IAAG,aAAWF,OAAIM,MAAG,GAAG,IAAIR,IAAE,WAAS,EAAEM,EAAC,IAAE,SAAQ,MAAGF,EAAC,OAAKI,MAAG,GAAG,IAAIR,IAAE,YAAU,EAAEM,EAAC,GAAE,MAAGF,EAAC,GAAE,cAAYF,KAAEM,MAAG,GAAG,IAAIR,IAAE,WAAS,EAAEM,EAAC,IAAE,SAAQ,MAAGF,EAAC,IAAEG,MAAG,GAAG,IAAIP,IAAE,WAAS,EAAEM,EAAC,IAAE,SAAQ,MAAGF,EAAC;AAAG,WAAM,CAACD,MAAG,KAAGE,OAAIG,MAAG,KAAK,IAAI,GAAE,KAAK,KAAKR,GAAE,WAASC,GAAE,CAAC,EAAE,YAAY,IAAEA,GAAE,MAAM,CAAC,CAAC,IAAEI,KAAEG,KAAED,KAAE,GAAE,CAAC,KAAG,IAAGC,KAAEC;AAAA,EAAC;AAAC,WAAS,GAAGT,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAE,GAAGH,EAAC,GAAEI,MAAG,CAAC,GAAG,kBAAkB,KAAGF,OAAI,iBAAe,GAAG,IAAIF,IAAE,aAAY,OAAGG,EAAC,GAAEE,KAAED,IAAEE,KAAE,GAAGN,IAAEC,IAAEE,EAAC,GAAEI,KAAE,WAASN,GAAE,CAAC,EAAE,YAAY,IAAEA,GAAE,MAAM,CAAC;AAAE,QAAG,GAAG,KAAKK,EAAC,GAAE;AAAC,UAAG,CAACJ,GAAE,QAAOI;AAAE,MAAAA,KAAE;AAAA,IAAM;AAAC,YAAO,CAAC,GAAG,kBAAkB,KAAGF,MAAG,CAAC,GAAG,qBAAqB,KAAG,GAAGJ,IAAE,IAAI,KAAG,WAASM,MAAG,CAAC,WAAWA,EAAC,KAAG,aAAW,GAAG,IAAIN,IAAE,WAAU,OAAGG,EAAC,MAAIH,GAAE,eAAe,EAAE,WAASI,KAAE,iBAAe,GAAG,IAAIJ,IAAE,aAAY,OAAGG,EAAC,IAAGE,KAAEE,MAAKP,QAAKM,KAAEN,GAAEO,EAAC,MAAKD,KAAE,WAAWA,EAAC,KAAG,KAAG,GAAGN,IAAEC,IAAEC,OAAIE,KAAE,WAAS,YAAWC,IAAEF,IAAEG,EAAC,IAAE;AAAA,EAAI;AAAC,WAAS,GAAGN,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAO,IAAI,GAAG,UAAU,KAAKJ,IAAEC,IAAEC,IAAEC,IAAEC,EAAC;AAAA,EAAC;AAAC,KAAG,OAAO,EAAC,UAAS,EAAC,SAAQ,EAAC,KAAI,SAASJ,IAAEC,IAAE;AAAC,QAAGA,IAAE;AAAC,UAAIC,KAAE,GAAGF,IAAE,SAAS;AAAE,aAAM,OAAKE,KAAE,MAAIA;AAAA,IAAC;AAAA,EAAC,EAAC,EAAC,GAAE,WAAU,EAAC,yBAAwB,MAAG,aAAY,MAAG,kBAAiB,MAAG,aAAY,MAAG,UAAS,MAAG,YAAW,MAAG,YAAW,MAAG,UAAS,MAAG,YAAW,MAAG,eAAc,MAAG,iBAAgB,MAAG,SAAQ,MAAG,YAAW,MAAG,cAAa,MAAG,YAAW,MAAG,SAAQ,MAAG,OAAM,MAAG,SAAQ,MAAG,OAAM,MAAG,QAAO,MAAG,QAAO,MAAG,MAAK,MAAG,aAAY,MAAG,cAAa,MAAG,aAAY,MAAG,kBAAiB,MAAG,eAAc,KAAE,GAAE,UAAS,CAAC,GAAE,OAAM,SAASF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAGH,MAAG,MAAIA,GAAE,YAAU,MAAIA,GAAE,YAAUA,GAAE,OAAM;AAAC,UAAII,IAAEC,IAAEC,IAAEC,KAAE,EAAEN,EAAC,GAAEO,KAAE,GAAG,KAAKP,EAAC,GAAEQ,KAAET,GAAE;AAAM,UAAGQ,OAAIP,KAAE,GAAGM,EAAC,IAAGD,KAAE,GAAG,SAASL,EAAC,KAAG,GAAG,SAASM,EAAC,GAAE,WAASL,GAAE,QAAOI,MAAG,SAAQA,MAAG,YAAUF,KAAEE,GAAE,IAAIN,IAAE,OAAGG,EAAC,KAAGC,KAAEK,GAAER,EAAC;AAAE,oBAAYI,KAAE,OAAOH,QAAKE,KAAE,EAAE,KAAKF,EAAC,MAAIE,GAAE,CAAC,MAAIF,KAAE,GAAGF,IAAEC,IAAEG,EAAC,GAAEC,KAAE,WAAU,QAAMH,MAAGA,MAAGA,OAAI,aAAWG,MAAGG,OAAIN,MAAGE,MAAGA,GAAE,CAAC,MAAI,GAAG,UAAUG,EAAC,IAAE,KAAG,QAAO,GAAG,mBAAiB,OAAKL,MAAG,MAAID,GAAE,QAAQ,YAAY,MAAIQ,GAAER,EAAC,IAAE,YAAWK,MAAG,SAAQA,MAAG,YAAUJ,KAAEI,GAAE,IAAIN,IAAEE,IAAEC,EAAC,OAAKK,KAAEC,GAAE,YAAYR,IAAEC,EAAC,IAAEO,GAAER,EAAC,IAAEC;AAAA,IAAG;AAAA,EAAC,GAAE,KAAI,SAASF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,KAAE,EAAEN,EAAC;AAAE,WAAO,GAAG,KAAKA,EAAC,MAAIA,KAAE,GAAGM,EAAC,KAAID,KAAE,GAAG,SAASL,EAAC,KAAG,GAAG,SAASM,EAAC,MAAI,SAAQD,OAAIF,KAAEE,GAAE,IAAIN,IAAE,MAAGE,EAAC,IAAG,WAASE,OAAIA,KAAE,GAAGJ,IAAEC,IAAEE,EAAC,IAAG,aAAWC,MAAGH,MAAK,OAAKG,KAAE,GAAGH,EAAC,IAAG,OAAKC,MAAGA,MAAGG,KAAE,WAAWD,EAAC,GAAE,SAAKF,MAAG,SAASG,EAAC,IAAEA,MAAG,IAAED,MAAGA;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,KAAK,CAAC,UAAS,OAAO,GAAE,SAASJ,IAAEQ,IAAE;AAAC,OAAG,SAASA,EAAC,IAAE,EAAC,KAAI,SAASR,IAAEC,IAAEC,IAAE;AAAC,UAAGD,GAAE,QAAM,CAAC,GAAG,KAAK,GAAG,IAAID,IAAE,SAAS,CAAC,KAAGA,GAAE,eAAe,EAAE,UAAQA,GAAE,sBAAsB,EAAE,QAAM,GAAGA,IAAEQ,IAAEN,EAAC,IAAE,GAAGF,IAAE,IAAG,WAAU;AAAC,eAAO,GAAGA,IAAEQ,IAAEN,EAAC;AAAA,MAAC,CAAC;AAAA,IAAC,GAAE,KAAI,SAASF,IAAEC,IAAEC,IAAE;AAAC,UAAIC,IAAEC,KAAE,GAAGJ,EAAC,GAAEK,KAAE,CAAC,GAAG,cAAc,KAAG,eAAaD,GAAE,UAASE,MAAGD,MAAGH,OAAI,iBAAe,GAAG,IAAIF,IAAE,aAAY,OAAGI,EAAC,GAAEG,KAAEL,KAAE,GAAGF,IAAEQ,IAAEN,IAAEI,IAAEF,EAAC,IAAE;AAAE,aAAOE,MAAGD,OAAIE,MAAG,KAAK,KAAKP,GAAE,WAASQ,GAAE,CAAC,EAAE,YAAY,IAAEA,GAAE,MAAM,CAAC,CAAC,IAAE,WAAWJ,GAAEI,EAAC,CAAC,IAAE,GAAGR,IAAEQ,IAAE,UAAS,OAAGJ,EAAC,IAAE,GAAE,IAAGG,OAAIJ,KAAE,EAAE,KAAKF,EAAC,MAAI,UAAQE,GAAE,CAAC,KAAG,UAAQH,GAAE,MAAMQ,EAAC,IAAEP,IAAEA,KAAE,GAAG,IAAID,IAAEQ,EAAC,IAAG,GAAG,GAAEP,IAAEM,EAAC;AAAA,IAAC,EAAC;AAAA,EAAC,CAAC,GAAE,GAAG,SAAS,aAAW,GAAG,GAAG,oBAAmB,SAASP,IAAEC,IAAE;AAAC,QAAGA,GAAE,SAAO,WAAW,GAAGD,IAAE,YAAY,CAAC,KAAGA,GAAE,sBAAsB,EAAE,OAAK,GAAGA,IAAE,EAAC,YAAW,EAAC,GAAE,WAAU;AAAC,aAAOA,GAAE,sBAAsB,EAAE;AAAA,IAAI,CAAC,KAAG;AAAA,EAAI,CAAC,GAAE,GAAG,KAAK,EAAC,QAAO,IAAG,SAAQ,IAAG,QAAO,QAAO,GAAE,SAASI,IAAEC,IAAE;AAAC,OAAG,SAASD,KAAEC,EAAC,IAAE,EAAC,QAAO,SAASL,IAAE;AAAC,eAAQC,KAAE,GAAEC,KAAE,CAAC,GAAEC,KAAE,YAAU,OAAOH,KAAEA,GAAE,MAAM,GAAG,IAAE,CAACA,EAAC,GAAEC,KAAE,GAAEA,KAAI,CAAAC,GAAEE,KAAE,EAAEH,EAAC,IAAEI,EAAC,IAAEF,GAAEF,EAAC,KAAGE,GAAEF,KAAE,CAAC,KAAGE,GAAE,CAAC;AAAE,aAAOD;AAAA,IAAC,EAAC,GAAE,aAAWE,OAAI,GAAG,SAASA,KAAEC,EAAC,EAAE,MAAI;AAAA,EAAG,CAAC,GAAE,GAAG,GAAG,OAAO,EAAC,KAAI,SAASL,IAAEC,IAAE;AAAC,WAAO,EAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC,UAAIC,IAAEC,IAAEC,KAAE,CAAC,GAAEC,KAAE;AAAE,UAAG,MAAM,QAAQL,EAAC,GAAE;AAAC,aAAIE,KAAE,GAAGH,EAAC,GAAEI,KAAEH,GAAE,QAAOK,KAAEF,IAAEE,KAAI,CAAAD,GAAEJ,GAAEK,EAAC,CAAC,IAAE,GAAG,IAAIN,IAAEC,GAAEK,EAAC,GAAE,OAAGH,EAAC;AAAE,eAAOE;AAAA,MAAC;AAAC,aAAO,WAASH,KAAE,GAAG,MAAMF,IAAEC,IAAEC,EAAC,IAAE,GAAG,IAAIF,IAAEC,EAAC;AAAA,IAAC,GAAED,IAAEC,IAAE,IAAE,UAAU,MAAM;AAAA,EAAC,EAAC,CAAC,KAAI,GAAG,QAAM,IAAI,YAAU,EAAC,aAAY,IAAG,MAAK,SAASD,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,OAAKL,IAAE,KAAK,OAAKE,IAAE,KAAK,SAAOE,MAAG,GAAG,OAAO,UAAS,KAAK,UAAQH,IAAE,KAAK,QAAM,KAAK,MAAI,KAAK,IAAI,GAAE,KAAK,MAAIE,IAAE,KAAK,OAAKE,OAAI,GAAG,UAAUH,EAAC,IAAE,KAAG;AAAA,EAAK,GAAE,KAAI,WAAU;AAAC,QAAIF,KAAE,GAAG,UAAU,KAAK,IAAI;AAAE,WAAOA,MAAGA,GAAE,MAAIA,GAAE,IAAI,IAAI,IAAE,GAAG,UAAU,SAAS,IAAI,IAAI;AAAA,EAAC,GAAE,KAAI,SAASA,IAAE;AAAC,QAAIC,IAAEC,KAAE,GAAG,UAAU,KAAK,IAAI;AAAE,WAAO,KAAK,QAAQ,WAAS,KAAK,MAAID,KAAE,GAAG,OAAO,KAAK,MAAM,EAAED,IAAE,KAAK,QAAQ,WAASA,IAAE,GAAE,GAAE,KAAK,QAAQ,QAAQ,IAAE,KAAK,MAAIC,KAAED,IAAE,KAAK,OAAK,KAAK,MAAI,KAAK,SAAOC,KAAE,KAAK,OAAM,KAAK,QAAQ,QAAM,KAAK,QAAQ,KAAK,KAAK,KAAK,MAAK,KAAK,KAAI,IAAI,GAAEC,MAAGA,GAAE,MAAIA,GAAE,IAAI,IAAI,IAAE,GAAG,UAAU,SAAS,IAAI,IAAI,GAAE;AAAA,EAAI,EAAC,GAAG,KAAK,YAAU,GAAG,YAAW,GAAG,YAAU,EAAC,UAAS,EAAC,KAAI,SAASF,IAAE;AAAC,QAAIC;AAAE,WAAO,MAAID,GAAE,KAAK,YAAU,QAAMA,GAAE,KAAKA,GAAE,IAAI,KAAG,QAAMA,GAAE,KAAK,MAAMA,GAAE,IAAI,IAAEA,GAAE,KAAKA,GAAE,IAAI,KAAGC,KAAE,GAAG,IAAID,GAAE,MAAKA,GAAE,MAAK,EAAE,MAAI,WAASC,KAAEA,KAAE;AAAA,EAAC,GAAE,KAAI,SAASD,IAAE;AAAC,OAAG,GAAG,KAAKA,GAAE,IAAI,IAAE,GAAG,GAAG,KAAKA,GAAE,IAAI,EAAEA,EAAC,IAAE,MAAIA,GAAE,KAAK,YAAU,CAAC,GAAG,SAASA,GAAE,IAAI,KAAG,QAAMA,GAAE,KAAK,MAAM,GAAGA,GAAE,IAAI,CAAC,IAAEA,GAAE,KAAKA,GAAE,IAAI,IAAEA,GAAE,MAAI,GAAG,MAAMA,GAAE,MAAKA,GAAE,MAAKA,GAAE,MAAIA,GAAE,IAAI;AAAA,EAAC,EAAC,EAAC,GAAG,YAAU,GAAG,UAAU,aAAW,EAAC,KAAI,SAASA,IAAE;AAAC,IAAAA,GAAE,KAAK,YAAUA,GAAE,KAAK,eAAaA,GAAE,KAAKA,GAAE,IAAI,IAAEA,GAAE;AAAA,EAAI,EAAC,GAAE,GAAG,SAAO,EAAC,QAAO,SAASA,IAAE;AAAC,WAAOA;AAAA,EAAC,GAAE,OAAM,SAASA,IAAE;AAAC,WAAM,MAAG,KAAK,IAAIA,KAAE,KAAK,EAAE,IAAE;AAAA,EAAC,GAAE,UAAS,QAAO,GAAE,GAAG,KAAG,GAAG,UAAU,MAAK,GAAG,GAAG,OAAK,CAAC;AAAE,MAAI,IAAG,IAAG,IAAG,IAAG,KAAG,0BAAyB,KAAG;AAAc,WAAS,KAAI;AAAC,WAAK,UAAK,EAAE,UAAQ,GAAG,wBAAsB,GAAG,sBAAsB,EAAE,IAAE,GAAG,WAAW,IAAG,GAAG,GAAG,QAAQ,GAAE,GAAG,GAAG,KAAK;AAAA,EAAE;AAAC,WAAS,KAAI;AAAC,WAAO,GAAG,WAAW,WAAU;AAAC,WAAG;AAAA,IAAM,CAAC,GAAE,KAAG,KAAK,IAAI;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAEC,IAAE;AAAC,QAAIC,IAAEC,KAAE,GAAEC,KAAE,EAAC,QAAOJ,GAAC;AAAE,SAAIC,KAAEA,KAAE,IAAE,GAAEE,KAAE,GAAEA,MAAG,IAAEF,GAAE,CAAAG,GAAE,YAAUF,KAAE,EAAEC,EAAC,EAAE,IAAEC,GAAE,YAAUF,EAAC,IAAEF;AAAE,WAAOC,OAAIG,GAAE,UAAQA,GAAE,QAAMJ,KAAGI;AAAA,EAAC;AAAC,WAAS,GAAGJ,IAAEC,IAAEC,IAAE;AAAC,aAAQC,IAAEC,MAAG,GAAG,SAASH,EAAC,KAAG,CAAC,GAAG,OAAO,GAAG,SAAS,GAAG,CAAC,GAAEI,KAAE,GAAEC,KAAEF,GAAE,QAAOC,KAAEC,IAAED,KAAI,KAAGF,KAAEC,GAAEC,EAAC,EAAE,KAAKH,IAAED,IAAED,EAAC,EAAE,QAAOG;AAAA,EAAC;AAAC,WAAS,GAAGE,IAAEL,IAAEC,IAAE;AAAC,QAAIC,IAAEI,IAAEH,KAAE,GAAEC,KAAE,GAAG,WAAW,QAAOG,KAAE,GAAG,SAAS,EAAE,OAAO,WAAU;AAAC,aAAOC,GAAE;AAAA,IAAI,CAAC,GAAEA,KAAE,WAAU;AAAC,UAAGF,GAAE,QAAM;AAAG,eAAQN,KAAE,MAAI,GAAG,GAAEC,KAAE,KAAK,IAAI,GAAEQ,GAAE,YAAUA,GAAE,WAAST,EAAC,GAAEE,KAAE,KAAGD,KAAEQ,GAAE,YAAU,IAAGN,KAAE,GAAEC,KAAEK,GAAE,OAAO,QAAON,KAAEC,IAAED,KAAI,CAAAM,GAAE,OAAON,EAAC,EAAE,IAAID,EAAC;AAAE,aAAOK,GAAE,WAAWF,IAAE,CAACI,IAAEP,IAAED,EAAC,CAAC,GAAEC,KAAE,KAAGE,KAAEH,MAAGG,MAAGG,GAAE,WAAWF,IAAE,CAACI,IAAE,GAAE,CAAC,CAAC,GAAEF,GAAE,YAAYF,IAAE,CAACI,EAAC,CAAC,GAAE;AAAA,IAAG,GAAEA,KAAEF,GAAE,QAAQ,EAAC,MAAKF,IAAE,OAAM,GAAG,OAAO,CAAC,GAAEL,EAAC,GAAE,MAAK,GAAG,OAAO,MAAG,EAAC,eAAc,CAAC,GAAE,QAAO,GAAG,OAAO,SAAQ,GAAEC,EAAC,GAAE,oBAAmBD,IAAE,iBAAgBC,IAAE,WAAU,MAAI,GAAG,GAAE,UAASA,GAAE,UAAS,QAAO,CAAC,GAAE,aAAY,SAASD,IAAEC,IAAE;AAAC,UAAIC,KAAE,GAAG,MAAMG,IAAEI,GAAE,MAAKT,IAAEC,IAAEQ,GAAE,KAAK,cAAcT,EAAC,KAAGS,GAAE,KAAK,MAAM;AAAE,aAAOA,GAAE,OAAO,KAAKP,EAAC,GAAEA;AAAA,IAAC,GAAE,MAAK,SAASF,IAAE;AAAC,UAAIC,KAAE,GAAEC,KAAEF,KAAES,GAAE,OAAO,SAAO;AAAE,UAAGH,GAAE,QAAO;AAAK,WAAIA,KAAE,MAAGL,KAAEC,IAAED,KAAI,CAAAQ,GAAE,OAAOR,EAAC,EAAE,IAAI,CAAC;AAAE,aAAOD,MAAGO,GAAE,WAAWF,IAAE,CAACI,IAAE,GAAE,CAAC,CAAC,GAAEF,GAAE,YAAYF,IAAE,CAACI,IAAET,EAAC,CAAC,KAAGO,GAAE,WAAWF,IAAE,CAACI,IAAET,EAAC,CAAC,GAAE;AAAA,IAAI,EAAC,CAAC,GAAEmB,KAAEV,GAAE;AAAM,SAAI,CAAC,SAAST,IAAEC,IAAE;AAAC,UAAIC,IAAEC,IAAEC,IAAEC,IAAEC;AAAE,WAAIJ,MAAKF,GAAE,KAAGI,KAAEH,GAAEE,KAAE,EAAED,EAAC,CAAC,GAAEG,KAAEL,GAAEE,EAAC,GAAE,MAAM,QAAQG,EAAC,MAAID,KAAEC,GAAE,CAAC,GAAEA,KAAEL,GAAEE,EAAC,IAAEG,GAAE,CAAC,IAAGH,OAAIC,OAAIH,GAAEG,EAAC,IAAEE,IAAE,OAAOL,GAAEE,EAAC,KAAII,KAAE,GAAG,SAASH,EAAC,MAAI,YAAWG,GAAE,MAAIJ,MAAKG,KAAEC,GAAE,OAAOD,EAAC,GAAE,OAAOL,GAAEG,EAAC,GAAEE,GAAE,CAAAH,MAAKF,OAAIA,GAAEE,EAAC,IAAEG,GAAEH,EAAC,GAAED,GAAEC,EAAC,IAAEE;AAAA,UAAQ,CAAAH,GAAEE,EAAC,IAAEC;AAAA,IAAC,EAAEe,IAAEV,GAAE,KAAK,aAAa,GAAEN,KAAEC,IAAED,KAAI,KAAGD,KAAE,GAAG,WAAWC,EAAC,EAAE,KAAKM,IAAEJ,IAAEc,IAAEV,GAAE,IAAI,EAAE,QAAO,EAAEP,GAAE,IAAI,MAAI,GAAG,YAAYO,GAAE,MAAKA,GAAE,KAAK,KAAK,EAAE,OAAKP,GAAE,KAAK,KAAKA,EAAC,IAAGA;AAAE,WAAO,GAAG,IAAIiB,IAAE,IAAGV,EAAC,GAAE,EAAEA,GAAE,KAAK,KAAK,KAAGA,GAAE,KAAK,MAAM,KAAKJ,IAAEI,EAAC,GAAEA,GAAE,SAASA,GAAE,KAAK,QAAQ,EAAE,KAAKA,GAAE,KAAK,MAAKA,GAAE,KAAK,QAAQ,EAAE,KAAKA,GAAE,KAAK,IAAI,EAAE,OAAOA,GAAE,KAAK,MAAM,GAAE,GAAG,GAAG,MAAM,GAAG,OAAOD,IAAE,EAAC,MAAKH,IAAE,MAAKI,IAAE,OAAMA,GAAE,KAAK,MAAK,CAAC,CAAC,GAAEA;AAAA,EAAC;AAAC,KAAG,YAAU,GAAG,OAAO,IAAG,EAAC,UAAS,EAAC,KAAI,CAAC,SAAST,IAAEC,IAAE;AAAC,QAAIC,KAAE,KAAK,YAAYF,IAAEC,EAAC;AAAE,WAAO,GAAGC,GAAE,MAAKF,IAAE,EAAE,KAAKC,EAAC,GAAEC,EAAC,GAAEA;AAAA,EAAC,CAAC,EAAC,GAAE,SAAQ,SAASF,IAAEC,IAAE;AAAC,MAAED,EAAC,KAAGC,KAAED,IAAEA,KAAE,CAAC,GAAG,KAAGA,KAAEA,GAAE,MAAM,CAAC;AAAE,aAAQE,IAAEC,KAAE,GAAEC,KAAEJ,GAAE,QAAOG,KAAEC,IAAED,KAAI,CAAAD,KAAEF,GAAEG,EAAC,GAAE,GAAG,SAASD,EAAC,IAAE,GAAG,SAASA,EAAC,KAAG,CAAC,GAAE,GAAG,SAASA,EAAC,EAAE,QAAQD,EAAC;AAAA,EAAC,GAAE,YAAW,CAAC,SAASD,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEU,IAAEE,KAAE,WAAUpB,MAAG,YAAWA,IAAEqB,KAAE,MAAKR,KAAE,CAAC,GAAEM,KAAEpB,GAAE,OAAMuB,KAAEvB,GAAE,YAAU,GAAGA,EAAC,GAAEwB,KAAE,EAAE,IAAIxB,IAAE,QAAQ;AAAE,SAAIG,MAAKD,GAAE,UAAQ,SAAOI,KAAE,GAAG,YAAYN,IAAE,IAAI,GAAG,aAAWM,GAAE,WAAS,GAAEC,KAAED,GAAE,MAAM,MAAKA,GAAE,MAAM,OAAK,WAAU;AAAC,MAAAA,GAAE,YAAUC,GAAE;AAAA,IAAC,IAAGD,GAAE,YAAWgB,GAAE,OAAO,WAAU;AAAC,MAAAA,GAAE,OAAO,WAAU;AAAC,QAAAhB,GAAE,YAAW,GAAG,MAAMN,IAAE,IAAI,EAAE,UAAQM,GAAE,MAAM,KAAK;AAAA,MAAC,CAAC;AAAA,IAAC,CAAC,IAAGL,GAAE,KAAGG,KAAEH,GAAEE,EAAC,GAAE,GAAG,KAAKC,EAAC,GAAE;AAAC,UAAG,OAAOH,GAAEE,EAAC,GAAEE,KAAEA,MAAG,aAAWD,IAAEA,QAAKmB,KAAE,SAAO,SAAQ;AAAC,YAAG,WAASnB,MAAG,CAACoB,MAAG,WAASA,GAAErB,EAAC,EAAE;AAAS,QAAAoB,KAAE;AAAA,MAAE;AAAC,MAAAT,GAAEX,EAAC,IAAEqB,MAAGA,GAAErB,EAAC,KAAG,GAAG,MAAMH,IAAEG,EAAC;AAAA,IAAC;AAAC,SAAIK,KAAE,CAAC,GAAG,cAAcP,EAAC,MAAI,CAAC,GAAG,cAAca,EAAC,EAAE,MAAIX,MAAKkB,MAAG,MAAIrB,GAAE,aAAWE,GAAE,WAAS,CAACkB,GAAE,UAASA,GAAE,WAAUA,GAAE,SAAS,GAAE,SAAOX,KAAEe,MAAGA,GAAE,aAAWf,KAAE,EAAE,IAAIT,IAAE,SAAS,IAAG,YAAUmB,KAAE,GAAG,IAAInB,IAAE,SAAS,OAAKS,KAAEU,KAAEV,MAAG,GAAG,CAACT,EAAC,GAAE,IAAE,GAAES,KAAET,GAAE,MAAM,WAASS,IAAEU,KAAE,GAAG,IAAInB,IAAE,SAAS,GAAE,GAAG,CAACA,EAAC,CAAC,MAAK,aAAWmB,MAAG,mBAAiBA,MAAG,QAAMV,OAAI,WAAS,GAAG,IAAIT,IAAE,OAAO,MAAIQ,OAAIc,GAAE,KAAK,WAAU;AAAC,MAAAF,GAAE,UAAQX;AAAA,IAAC,CAAC,GAAE,QAAMA,OAAIU,KAAEC,GAAE,SAAQX,KAAE,WAASU,KAAE,KAAGA,MAAIC,GAAE,UAAQ,kBAAiBlB,GAAE,aAAWkB,GAAE,WAAS,UAASE,GAAE,OAAO,WAAU;AAAC,MAAAF,GAAE,WAASlB,GAAE,SAAS,CAAC,GAAEkB,GAAE,YAAUlB,GAAE,SAAS,CAAC,GAAEkB,GAAE,YAAUlB,GAAE,SAAS,CAAC;AAAA,IAAC,CAAC,IAAGM,KAAE,OAAGM,GAAE,CAAAN,OAAIgB,KAAE,YAAWA,OAAID,KAAEC,GAAE,UAAQA,KAAE,EAAE,OAAOxB,IAAE,UAAS,EAAC,SAAQS,GAAC,CAAC,GAAEJ,OAAImB,GAAE,SAAO,CAACD,KAAGA,MAAG,GAAG,CAACvB,EAAC,GAAE,IAAE,GAAEsB,GAAE,KAAK,WAAU;AAAC,WAAInB,MAAKoB,MAAG,GAAG,CAACvB,EAAC,CAAC,GAAE,EAAE,OAAOA,IAAE,QAAQ,GAAEc,GAAE,IAAG,MAAMd,IAAEG,IAAEW,GAAEX,EAAC,CAAC;AAAA,IAAC,CAAC,IAAGK,KAAE,GAAGe,KAAEC,GAAErB,EAAC,IAAE,GAAEA,IAAEmB,EAAC,GAAEnB,MAAKqB,OAAIA,GAAErB,EAAC,IAAEK,GAAE,OAAMe,OAAIf,GAAE,MAAIA,GAAE,OAAMA,GAAE,QAAM;AAAA,EAAG,CAAC,GAAE,WAAU,SAASR,IAAEC,IAAE;AAAC,IAAAA,KAAE,GAAG,WAAW,QAAQD,EAAC,IAAE,GAAG,WAAW,KAAKA,EAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,QAAM,SAASA,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAEH,MAAG,YAAU,OAAOA,KAAE,GAAG,OAAO,CAAC,GAAEA,EAAC,IAAE,EAAC,UAASE,MAAG,CAACA,MAAGD,MAAG,EAAED,EAAC,KAAGA,IAAE,UAASA,IAAE,QAAOE,MAAGD,MAAGA,MAAG,CAAC,EAAEA,EAAC,KAAGA,GAAC;AAAE,WAAO,GAAG,GAAG,MAAIE,GAAE,WAAS,IAAE,YAAU,OAAOA,GAAE,aAAWA,GAAE,YAAY,GAAG,GAAG,SAAOA,GAAE,WAAS,GAAG,GAAG,OAAOA,GAAE,QAAQ,IAAEA,GAAE,WAAS,GAAG,GAAG,OAAO,WAAU,QAAMA,GAAE,SAAO,SAAKA,GAAE,UAAQA,GAAE,QAAM,OAAMA,GAAE,MAAIA,GAAE,UAASA,GAAE,WAAS,WAAU;AAAC,QAAEA,GAAE,GAAG,KAAGA,GAAE,IAAI,KAAK,IAAI,GAAEA,GAAE,SAAO,GAAG,QAAQ,MAAKA,GAAE,KAAK;AAAA,IAAC,GAAEA;AAAA,EAAC,GAAE,GAAG,GAAG,OAAO,EAAC,QAAO,SAASH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,OAAO,EAAE,EAAE,IAAI,WAAU,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAC,SAAQF,GAAC,GAAED,IAAEE,IAAEC,EAAC;AAAA,EAAC,GAAE,SAAQ,SAASF,IAAED,IAAEE,IAAEC,IAAE;AAAC,QAAIC,KAAE,GAAG,cAAcH,EAAC,GAAEI,KAAE,GAAG,MAAML,IAAEE,IAAEC,EAAC,GAAEG,KAAE,WAAU;AAAC,UAAIN,KAAE,GAAG,MAAK,GAAG,OAAO,CAAC,GAAEC,EAAC,GAAEI,EAAC;AAAE,OAACD,MAAG,EAAE,IAAI,MAAK,QAAQ,MAAIJ,GAAE,KAAK,IAAE;AAAA,IAAC;AAAE,WAAOM,GAAE,SAAOA,IAAEF,MAAG,UAAKC,GAAE,QAAM,KAAK,KAAKC,EAAC,IAAE,KAAK,MAAMD,GAAE,OAAMC,EAAC;AAAA,EAAC,GAAE,MAAK,SAASF,IAAEJ,IAAEK,IAAE;AAAC,QAAIC,KAAE,SAASN,IAAE;AAAC,UAAIC,KAAED,GAAE;AAAK,aAAOA,GAAE,MAAKC,GAAEI,EAAC;AAAA,IAAC;AAAE,WAAM,YAAU,OAAOD,OAAIC,KAAEL,IAAEA,KAAEI,IAAEA,KAAE,SAAQJ,MAAG,KAAK,MAAMI,MAAG,MAAK,CAAC,CAAC,GAAE,KAAK,KAAK,WAAU;AAAC,UAAIJ,KAAE,MAAGC,KAAE,QAAMG,MAAGA,KAAE,cAAaF,KAAE,GAAG,QAAOC,KAAE,EAAE,IAAI,IAAI;AAAE,UAAGF,GAAE,CAAAE,GAAEF,EAAC,KAAGE,GAAEF,EAAC,EAAE,QAAMK,GAAEH,GAAEF,EAAC,CAAC;AAAA,UAAO,MAAIA,MAAKE,GAAE,CAAAA,GAAEF,EAAC,KAAGE,GAAEF,EAAC,EAAE,QAAM,GAAG,KAAKA,EAAC,KAAGK,GAAEH,GAAEF,EAAC,CAAC;AAAE,WAAIA,KAAEC,GAAE,QAAOD,OAAK,CAAAC,GAAED,EAAC,EAAE,SAAO,QAAM,QAAMG,MAAGF,GAAED,EAAC,EAAE,UAAQG,OAAIF,GAAED,EAAC,EAAE,KAAK,KAAKI,EAAC,GAAEL,KAAE,OAAGE,GAAE,OAAOD,IAAE,CAAC;AAAG,OAACD,MAAGK,MAAG,GAAG,QAAQ,MAAKD,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,QAAO,SAASE,IAAE;AAAC,WAAM,UAAKA,OAAIA,KAAEA,MAAG,OAAM,KAAK,KAAK,WAAU;AAAC,UAAIN,IAAEC,KAAE,EAAE,IAAI,IAAI,GAAEC,KAAED,GAAEK,KAAE,OAAO,GAAEH,KAAEF,GAAEK,KAAE,YAAY,GAAEF,KAAE,GAAG,QAAOC,KAAEH,KAAEA,GAAE,SAAO;AAAE,WAAID,GAAE,SAAO,MAAG,GAAG,MAAM,MAAKK,IAAE,CAAC,CAAC,GAAEH,MAAGA,GAAE,QAAMA,GAAE,KAAK,KAAK,MAAK,IAAE,GAAEH,KAAEI,GAAE,QAAOJ,OAAK,CAAAI,GAAEJ,EAAC,EAAE,SAAO,QAAMI,GAAEJ,EAAC,EAAE,UAAQM,OAAIF,GAAEJ,EAAC,EAAE,KAAK,KAAK,IAAE,GAAEI,GAAE,OAAOJ,IAAE,CAAC;AAAG,WAAIA,KAAE,GAAEA,KAAEK,IAAEL,KAAI,CAAAE,GAAEF,EAAC,KAAGE,GAAEF,EAAC,EAAE,UAAQE,GAAEF,EAAC,EAAE,OAAO,KAAK,IAAI;AAAE,aAAOC,GAAE;AAAA,IAAM,CAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,KAAK,CAAC,UAAS,QAAO,MAAM,GAAE,SAASD,IAAEG,IAAE;AAAC,QAAIC,KAAE,GAAG,GAAGD,EAAC;AAAE,OAAG,GAAGA,EAAC,IAAE,SAASH,IAAEC,IAAEC,IAAE;AAAC,aAAO,QAAMF,MAAG,aAAW,OAAOA,KAAEI,GAAE,MAAM,MAAK,SAAS,IAAE,KAAK,QAAQ,GAAGD,IAAE,IAAE,GAAEH,IAAEC,IAAEC,EAAC;AAAA,IAAC;AAAA,EAAC,CAAC,GAAE,GAAG,KAAK,EAAC,WAAU,GAAG,MAAM,GAAE,SAAQ,GAAG,MAAM,GAAE,aAAY,GAAG,QAAQ,GAAE,QAAO,EAAC,SAAQ,OAAM,GAAE,SAAQ,EAAC,SAAQ,OAAM,GAAE,YAAW,EAAC,SAAQ,SAAQ,EAAC,GAAE,SAASF,IAAEG,IAAE;AAAC,OAAG,GAAGH,EAAC,IAAE,SAASA,IAAEC,IAAEC,IAAE;AAAC,aAAO,KAAK,QAAQC,IAAEH,IAAEC,IAAEC,EAAC;AAAA,IAAC;AAAA,EAAC,CAAC,GAAE,GAAG,SAAO,CAAC,GAAE,GAAG,GAAG,OAAK,WAAU;AAAC,QAAIF,IAAEC,KAAE,GAAEC,KAAE,GAAG;AAAO,SAAI,KAAG,KAAK,IAAI,GAAED,KAAEC,GAAE,QAAOD,KAAI,EAACD,KAAEE,GAAED,EAAC,GAAG,KAAGC,GAAED,EAAC,MAAID,MAAGE,GAAE,OAAOD,MAAI,CAAC;AAAE,IAAAC,GAAE,UAAQ,GAAG,GAAG,KAAK,GAAE,KAAG;AAAA,EAAM,GAAE,GAAG,GAAG,QAAM,SAASF,IAAE;AAAC,OAAG,OAAO,KAAKA,EAAC,GAAE,GAAG,GAAG,MAAM;AAAA,EAAC,GAAE,GAAG,GAAG,WAAS,IAAG,GAAG,GAAG,QAAM,WAAU;AAAC,WAAK,KAAG,MAAG,GAAG;AAAA,EAAE,GAAE,GAAG,GAAG,OAAK,WAAU;AAAC,SAAG;AAAA,EAAI,GAAE,GAAG,GAAG,SAAO,EAAC,MAAK,KAAI,MAAK,KAAI,UAAS,IAAG,GAAE,GAAG,GAAG,QAAM,SAASG,IAAEH,IAAE;AAAC,WAAOG,KAAE,GAAG,MAAI,GAAG,GAAG,OAAOA,EAAC,KAAGA,IAAEH,KAAEA,MAAG,MAAK,KAAK,MAAMA,IAAE,SAASA,IAAEC,IAAE;AAAC,UAAIC,KAAE,GAAG,WAAWF,IAAEG,EAAC;AAAE,MAAAF,GAAE,OAAK,WAAU;AAAC,WAAG,aAAaC,EAAC;AAAA,MAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,KAAG,EAAE,cAAc,OAAO,GAAE,KAAG,EAAE,cAAc,QAAQ,EAAE,YAAY,EAAE,cAAc,QAAQ,CAAC,GAAE,GAAG,OAAK,YAAW,GAAG,UAAQ,OAAK,GAAG,OAAM,GAAG,cAAY,GAAG,WAAU,KAAG,EAAE,cAAc,OAAO,GAAG,QAAM,KAAI,GAAG,OAAK,SAAQ,GAAG,aAAW,QAAM,GAAG;AAAM,MAAI,IAAG,KAAG,GAAG,KAAK;AAAW,KAAG,GAAG,OAAO,EAAC,MAAK,SAASF,IAAEC,IAAE;AAAC,WAAO,EAAE,MAAK,GAAG,MAAKD,IAAEC,IAAE,IAAE,UAAU,MAAM;AAAA,EAAC,GAAE,YAAW,SAASD,IAAE;AAAC,WAAO,KAAK,KAAK,WAAU;AAAC,SAAG,WAAW,MAAKA,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,OAAO,EAAC,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAEL,GAAE;AAAS,QAAG,MAAIK,MAAG,MAAIA,MAAG,MAAIA,GAAE,QAAM,eAAa,OAAOL,GAAE,eAAa,GAAG,KAAKA,IAAEC,IAAEC,EAAC,KAAG,MAAIG,MAAG,GAAG,SAASL,EAAC,MAAII,KAAE,GAAG,UAAUH,GAAE,YAAY,CAAC,MAAI,GAAG,KAAK,MAAM,KAAK,KAAKA,EAAC,IAAE,KAAG,UAAS,WAASC,KAAE,SAAOA,KAAE,KAAK,GAAG,WAAWF,IAAEC,EAAC,IAAEG,MAAG,SAAQA,MAAG,YAAUD,KAAEC,GAAE,IAAIJ,IAAEE,IAAED,EAAC,KAAGE,MAAGH,GAAE,aAAaC,IAAEC,KAAE,EAAE,GAAEA,MAAGE,MAAG,SAAQA,MAAG,UAAQD,KAAEC,GAAE,IAAIJ,IAAEC,EAAC,KAAGE,KAAE,SAAOA,KAAE,GAAG,KAAK,KAAKH,IAAEC,EAAC,KAAG,SAAOE;AAAA,EAAE,GAAE,WAAU,EAAC,MAAK,EAAC,KAAI,SAASH,IAAEC,IAAE;AAAC,QAAG,CAAC,GAAG,cAAY,YAAUA,MAAG,GAAGD,IAAE,OAAO,GAAE;AAAC,UAAIE,KAAEF,GAAE;AAAM,aAAOA,GAAE,aAAa,QAAOC,EAAC,GAAEC,OAAIF,GAAE,QAAME,KAAGD;AAAA,IAAC;AAAA,EAAC,EAAC,EAAC,GAAE,YAAW,SAASD,IAAEC,IAAE;AAAC,QAAIC,IAAEC,KAAE,GAAEC,KAAEH,MAAGA,GAAE,MAAM,CAAC;AAAE,QAAGG,MAAG,MAAIJ,GAAE,SAAS,QAAME,KAAEE,GAAED,IAAG,EAAE,CAAAH,GAAE,gBAAgBE,EAAC;AAAA,EAAC,EAAC,CAAC,GAAE,KAAG,EAAC,KAAI,SAASF,IAAEC,IAAEC,IAAE;AAAC,WAAM,UAAKD,KAAE,GAAG,WAAWD,IAAEE,EAAC,IAAEF,GAAE,aAAaE,IAAEA,EAAC,GAAEA;AAAA,EAAC,EAAC,GAAE,GAAG,KAAK,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM,MAAM,GAAE,SAASF,IAAEC,IAAE;AAAC,QAAIK,KAAE,GAAGL,EAAC,KAAG,GAAG,KAAK;AAAK,OAAGA,EAAC,IAAE,SAASD,IAAEC,IAAEC,IAAE;AAAC,UAAIC,IAAEC,IAAEC,KAAEJ,GAAE,YAAY;AAAE,aAAOC,OAAIE,KAAE,GAAGC,EAAC,GAAE,GAAGA,EAAC,IAAEF,IAAEA,KAAE,QAAMG,GAAEN,IAAEC,IAAEC,EAAC,IAAEG,KAAE,MAAK,GAAGA,EAAC,IAAED,KAAGD;AAAA,IAAC;AAAA,EAAC,CAAC;AAAE,MAAI,KAAG,uCAAsC,KAAG;AAAgB,WAAS,GAAGH,IAAE;AAAC,YAAOA,GAAE,MAAM,CAAC,KAAG,CAAC,GAAG,KAAK,GAAG;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAE;AAAC,WAAOA,GAAE,gBAAcA,GAAE,aAAa,OAAO,KAAG;AAAA,EAAE;AAAC,WAAS,GAAGA,IAAE;AAAC,WAAO,MAAM,QAAQA,EAAC,IAAEA,KAAE,YAAU,OAAOA,MAAGA,GAAE,MAAM,CAAC,KAAG,CAAC;AAAA,EAAC;AAAC,KAAG,GAAG,OAAO,EAAC,MAAK,SAASA,IAAEC,IAAE;AAAC,WAAO,EAAE,MAAK,GAAG,MAAKD,IAAEC,IAAE,IAAE,UAAU,MAAM;AAAA,EAAC,GAAE,YAAW,SAASD,IAAE;AAAC,WAAO,KAAK,KAAK,WAAU;AAAC,aAAO,KAAK,GAAG,QAAQA,EAAC,KAAGA,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,OAAO,EAAC,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAEL,GAAE;AAAS,QAAG,MAAIK,MAAG,MAAIA,MAAG,MAAIA,GAAE,QAAO,MAAIA,MAAG,GAAG,SAASL,EAAC,MAAIC,KAAE,GAAG,QAAQA,EAAC,KAAGA,IAAEG,KAAE,GAAG,UAAUH,EAAC,IAAG,WAASC,KAAEE,MAAG,SAAQA,MAAG,YAAUD,KAAEC,GAAE,IAAIJ,IAAEE,IAAED,EAAC,KAAGE,KAAEH,GAAEC,EAAC,IAAEC,KAAEE,MAAG,SAAQA,MAAG,UAAQD,KAAEC,GAAE,IAAIJ,IAAEC,EAAC,KAAGE,KAAEH,GAAEC,EAAC;AAAA,EAAC,GAAE,WAAU,EAAC,UAAS,EAAC,KAAI,SAASD,IAAE;AAAC,QAAIC,KAAE,GAAG,KAAK,KAAKD,IAAE,UAAU;AAAE,WAAOC,KAAE,SAASA,IAAE,EAAE,IAAE,GAAG,KAAKD,GAAE,QAAQ,KAAG,GAAG,KAAKA,GAAE,QAAQ,KAAGA,GAAE,OAAK,IAAE;AAAA,EAAE,EAAC,EAAC,GAAE,SAAQ,EAAC,OAAM,WAAU,SAAQ,YAAW,EAAC,CAAC,GAAE,GAAG,gBAAc,GAAG,UAAU,WAAS,EAAC,KAAI,SAASA,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAW,WAAOC,MAAGA,GAAE,cAAYA,GAAE,WAAW,eAAc;AAAA,EAAI,GAAE,KAAI,SAASD,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAW,IAAAC,OAAIA,GAAE,eAAcA,GAAE,cAAYA,GAAE,WAAW;AAAA,EAAc,EAAC,IAAG,GAAG,KAAK,CAAC,YAAW,YAAW,aAAY,eAAc,eAAc,WAAU,WAAU,UAAS,eAAc,iBAAiB,GAAE,WAAU;AAAC,OAAG,QAAQ,KAAK,YAAY,CAAC,IAAE;AAAA,EAAI,CAAC,GAAE,GAAG,GAAG,OAAO,EAAC,UAAS,SAASA,IAAE;AAAC,QAAID,IAAEE,IAAEC,IAAEC,IAAEC,IAAEC;AAAE,WAAO,EAAEL,EAAC,IAAE,KAAK,KAAK,SAASD,IAAE;AAAC,SAAG,IAAI,EAAE,SAASC,GAAE,KAAK,MAAKD,IAAE,GAAG,IAAI,CAAC,CAAC;AAAA,IAAC,CAAC,KAAGA,KAAE,GAAGC,EAAC,GAAG,SAAO,KAAK,KAAK,WAAU;AAAC,UAAGE,KAAE,GAAG,IAAI,GAAED,KAAE,MAAI,KAAK,YAAU,MAAI,GAAGC,EAAC,IAAE,KAAI;AAAC,aAAIE,KAAE,GAAEA,KAAEL,GAAE,QAAOK,KAAI,CAAAD,KAAEJ,GAAEK,EAAC,GAAEH,GAAE,QAAQ,MAAIE,KAAE,GAAG,IAAE,MAAIF,MAAGE,KAAE;AAAK,QAAAE,KAAE,GAAGJ,EAAC,GAAEC,OAAIG,MAAG,KAAK,aAAa,SAAQA,EAAC;AAAA,MAAC;AAAA,IAAC,CAAC,IAAE;AAAA,EAAI,GAAE,aAAY,SAASL,IAAE;AAAC,QAAID,IAAEE,IAAEC,IAAEC,IAAEC,IAAEC;AAAE,WAAO,EAAEL,EAAC,IAAE,KAAK,KAAK,SAASD,IAAE;AAAC,SAAG,IAAI,EAAE,YAAYC,GAAE,KAAK,MAAKD,IAAE,GAAG,IAAI,CAAC,CAAC;AAAA,IAAC,CAAC,IAAE,UAAU,UAAQA,KAAE,GAAGC,EAAC,GAAG,SAAO,KAAK,KAAK,WAAU;AAAC,UAAGE,KAAE,GAAG,IAAI,GAAED,KAAE,MAAI,KAAK,YAAU,MAAI,GAAGC,EAAC,IAAE,KAAI;AAAC,aAAIE,KAAE,GAAEA,KAAEL,GAAE,QAAOK,MAAI;AAAC,UAAAD,KAAEJ,GAAEK,EAAC;AAAE,iBAAM,KAAGH,GAAE,QAAQ,MAAIE,KAAE,GAAG,EAAE,CAAAF,KAAEA,GAAE,QAAQ,MAAIE,KAAE,KAAI,GAAG;AAAA,QAAC;AAAC,QAAAE,KAAE,GAAGJ,EAAC,GAAEC,OAAIG,MAAG,KAAK,aAAa,SAAQA,EAAC;AAAA,MAAC;AAAA,IAAC,CAAC,IAAE,OAAK,KAAK,KAAK,SAAQ,EAAE;AAAA,EAAC,GAAE,aAAY,SAASL,IAAEC,IAAE;AAAC,QAAIF,IAAEG,IAAEC,IAAEC,IAAEC,KAAE,OAAOL,IAAEM,KAAE,aAAWD,MAAG,MAAM,QAAQL,EAAC;AAAE,WAAO,EAAEA,EAAC,IAAE,KAAK,KAAK,SAASD,IAAE;AAAC,SAAG,IAAI,EAAE,YAAYC,GAAE,KAAK,MAAKD,IAAE,GAAG,IAAI,GAAEE,EAAC,GAAEA,EAAC;AAAA,IAAC,CAAC,IAAE,aAAW,OAAOA,MAAGK,KAAEL,KAAE,KAAK,SAASD,EAAC,IAAE,KAAK,YAAYA,EAAC,KAAGD,KAAE,GAAGC,EAAC,GAAE,KAAK,KAAK,WAAU;AAAC,UAAGM,GAAE,MAAIF,KAAE,GAAG,IAAI,GAAED,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,KAAI,CAAAD,KAAEH,GAAEI,EAAC,GAAEC,GAAE,SAASF,EAAC,IAAEE,GAAE,YAAYF,EAAC,IAAEE,GAAE,SAASF,EAAC;AAAA,UAAO,YAASF,MAAG,cAAYK,QAAKH,KAAE,GAAG,IAAI,MAAI,EAAE,IAAI,MAAK,iBAAgBA,EAAC,GAAE,KAAK,gBAAc,KAAK,aAAa,SAAQA,MAAG,UAAKF,KAAE,KAAG,EAAE,IAAI,MAAK,eAAe,KAAG,EAAE;AAAA,IAAE,CAAC;AAAA,EAAE,GAAE,UAAS,SAASD,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAE;AAAE,IAAAF,KAAE,MAAID,KAAE;AAAI,WAAME,KAAE,KAAKC,IAAG,EAAE,KAAG,MAAID,GAAE,YAAU,MAAI,MAAI,GAAG,GAAGA,EAAC,CAAC,IAAE,KAAK,QAAQD,EAAC,EAAE,QAAM;AAAG,WAAM;AAAA,EAAE,EAAC,CAAC;AAAE,MAAI,KAAG;AAAM,KAAG,GAAG,OAAO,EAAC,KAAI,SAASC,IAAE;AAAC,QAAIC,IAAEH,IAAEI,IAAEH,KAAE,KAAK,CAAC;AAAE,WAAO,UAAU,UAAQG,KAAE,EAAEF,EAAC,GAAE,KAAK,KAAK,SAASF,IAAE;AAAC,UAAIC;AAAE,YAAI,KAAK,aAAW,SAAOA,KAAEG,KAAEF,GAAE,KAAK,MAAKF,IAAE,GAAG,IAAI,EAAE,IAAI,CAAC,IAAEE,MAAGD,KAAE,KAAG,YAAU,OAAOA,KAAEA,MAAG,KAAG,MAAM,QAAQA,EAAC,MAAIA,KAAE,GAAG,IAAIA,IAAE,SAASD,IAAE;AAAC,eAAO,QAAMA,KAAE,KAAGA,KAAE;AAAA,MAAE,CAAC,KAAIG,KAAE,GAAG,SAAS,KAAK,IAAI,KAAG,GAAG,SAAS,KAAK,SAAS,YAAY,CAAC,MAAI,SAAQA,MAAG,WAASA,GAAE,IAAI,MAAKF,IAAE,OAAO,MAAI,KAAK,QAAMA;AAAA,IAAG,CAAC,KAAGA,MAAGE,KAAE,GAAG,SAASF,GAAE,IAAI,KAAG,GAAG,SAASA,GAAE,SAAS,YAAY,CAAC,MAAI,SAAQE,MAAG,YAAUH,KAAEG,GAAE,IAAIF,IAAE,OAAO,KAAGD,KAAE,YAAU,QAAOA,KAAEC,GAAE,SAAOD,GAAE,QAAQ,IAAG,EAAE,IAAE,QAAMA,KAAE,KAAGA,KAAE;AAAA,EAAM,EAAC,CAAC,GAAE,GAAG,OAAO,EAAC,UAAS,EAAC,QAAO,EAAC,KAAI,SAASA,IAAE;AAAC,QAAIC,KAAE,GAAG,KAAK,KAAKD,IAAE,OAAO;AAAE,WAAO,QAAMC,KAAEA,KAAE,GAAG,GAAG,KAAKD,EAAC,CAAC;AAAA,EAAC,EAAC,GAAE,QAAO,EAAC,KAAI,SAASA,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,KAAEJ,GAAE,SAAQK,KAAEL,GAAE,eAAcM,KAAE,iBAAeN,GAAE,MAAKO,KAAED,KAAE,OAAK,CAAC,GAAEE,KAAEF,KAAED,KAAE,IAAED,GAAE;AAAO,SAAID,KAAEE,KAAE,IAAEG,KAAEF,KAAED,KAAE,GAAEF,KAAEK,IAAEL,KAAI,OAAKD,KAAEE,GAAED,EAAC,GAAG,YAAUA,OAAIE,OAAI,CAACH,GAAE,aAAW,CAACA,GAAE,WAAW,YAAU,CAAC,GAAGA,GAAE,YAAW,UAAU,IAAG;AAAC,UAAGD,KAAE,GAAGC,EAAC,EAAE,IAAI,GAAEI,GAAE,QAAOL;AAAE,MAAAM,GAAE,KAAKN,EAAC;AAAA,IAAC;AAAC,WAAOM;AAAA,EAAC,GAAE,KAAI,SAASP,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAEJ,GAAE,SAAQK,KAAE,GAAG,UAAUJ,EAAC,GAAEK,KAAEF,GAAE;AAAO,WAAME,KAAI,GAAEH,KAAEC,GAAEE,EAAC,GAAG,WAAS,KAAG,GAAG,QAAQ,GAAG,SAAS,OAAO,IAAIH,EAAC,GAAEE,EAAC,OAAKH,KAAE;AAAI,WAAOA,OAAIF,GAAE,gBAAc,KAAIK;AAAA,EAAC,EAAC,EAAC,EAAC,CAAC,GAAE,GAAG,KAAK,CAAC,SAAQ,UAAU,GAAE,WAAU;AAAC,OAAG,SAAS,IAAI,IAAE,EAAC,KAAI,SAASL,IAAEC,IAAE;AAAC,UAAG,MAAM,QAAQA,EAAC,EAAE,QAAOD,GAAE,UAAQ,KAAG,GAAG,QAAQ,GAAGA,EAAC,EAAE,IAAI,GAAEC,EAAC;AAAA,IAAC,EAAC,GAAE,GAAG,YAAU,GAAG,SAAS,IAAI,EAAE,MAAI,SAASD,IAAE;AAAC,aAAO,SAAOA,GAAE,aAAa,OAAO,IAAE,OAAKA,GAAE;AAAA,IAAK;AAAA,EAAE,CAAC;AAAE,MAAI,KAAG,GAAG,UAAS,KAAG,EAAC,MAAK,KAAK,IAAI,EAAC,GAAE,KAAG;AAAK,KAAG,WAAS,SAASA,IAAE;AAAC,QAAIC,IAAEC;AAAE,QAAG,CAACF,MAAG,YAAU,OAAOA,GAAE,QAAO;AAAK,QAAG;AAAC,MAAAC,KAAG,IAAI,GAAG,YAAW,gBAAgBD,IAAE,UAAU;AAAA,IAAC,SAAOA,IAAE;AAAA,IAAC;AAAC,WAAOE,KAAED,MAAGA,GAAE,qBAAqB,aAAa,EAAE,CAAC,GAAEA,MAAG,CAACC,MAAG,GAAG,MAAM,mBAAiBA,KAAE,GAAG,IAAIA,GAAE,YAAW,SAASF,IAAE;AAAC,aAAOA,GAAE;AAAA,IAAW,CAAC,EAAE,KAAK,IAAI,IAAEA,GAAE,GAAEC;AAAA,EAAC;AAAE,MAAI,KAAG,mCAAkC,KAAG,SAASD,IAAE;AAAC,IAAAA,GAAE,gBAAgB;AAAA,EAAC;AAAE,KAAG,OAAO,GAAG,OAAM,EAAC,SAAQ,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEU,IAAEE,IAAEC,KAAE,CAACpB,MAAG,CAAC,GAAEY,KAAE,GAAG,KAAKd,IAAE,MAAM,IAAEA,GAAE,OAAKA,IAAEoB,KAAE,GAAG,KAAKpB,IAAE,WAAW,IAAEA,GAAE,UAAU,MAAM,GAAG,IAAE,CAAC;AAAE,QAAGK,KAAEgB,KAAEf,KAAEJ,KAAEA,MAAG,GAAE,MAAIA,GAAE,YAAU,MAAIA,GAAE,YAAU,CAAC,GAAG,KAAKY,KAAE,GAAG,MAAM,SAAS,MAAI,KAAGA,GAAE,QAAQ,GAAG,MAAIA,MAAGM,KAAEN,GAAE,MAAM,GAAG,GAAG,MAAM,GAAEM,GAAE,KAAK,IAAGZ,KAAEM,GAAE,QAAQ,GAAG,IAAE,KAAG,OAAKA,KAAGd,KAAEA,GAAE,GAAG,OAAO,IAAEA,KAAE,IAAI,GAAG,MAAMc,IAAE,YAAU,OAAOd,MAAGA,EAAC,GAAG,YAAUG,KAAE,IAAE,GAAEH,GAAE,YAAUoB,GAAE,KAAK,GAAG,GAAEpB,GAAE,aAAWA,GAAE,YAAU,IAAI,OAAO,YAAUoB,GAAE,KAAK,eAAe,IAAE,SAAS,IAAE,MAAKpB,GAAE,SAAO,QAAOA,GAAE,WAASA,GAAE,SAAOE,KAAGD,KAAE,QAAMA,KAAE,CAACD,EAAC,IAAE,GAAG,UAAUC,IAAE,CAACD,EAAC,CAAC,GAAEmB,KAAE,GAAG,MAAM,QAAQL,EAAC,KAAG,CAAC,GAAEX,MAAG,CAACgB,GAAE,WAAS,UAAKA,GAAE,QAAQ,MAAMjB,IAAED,EAAC,IAAG;AAAC,UAAG,CAACE,MAAG,CAACgB,GAAE,YAAU,CAAC,EAAEjB,EAAC,GAAE;AAAC,aAAIK,KAAEY,GAAE,gBAAcL,IAAE,GAAG,KAAKP,KAAEO,EAAC,MAAIT,KAAEA,GAAE,aAAYA,IAAEA,KAAEA,GAAE,WAAW,CAAAiB,GAAE,KAAKjB,EAAC,GAAEC,KAAED;AAAE,QAAAC,QAAKJ,GAAE,iBAAe,MAAIoB,GAAE,KAAKhB,GAAE,eAAaA,GAAE,gBAAc,EAAE;AAAA,MAAC;AAAC,MAAAF,KAAE;AAAE,cAAOC,KAAEiB,GAAElB,IAAG,MAAI,CAACJ,GAAE,qBAAqB,EAAE,CAAAqB,KAAEhB,IAAEL,GAAE,OAAK,IAAEI,KAAEG,KAAEY,GAAE,YAAUL,KAAGL,MAAG,EAAE,IAAIJ,IAAE,QAAQ,KAAG,uBAAO,OAAO,IAAI,GAAGL,GAAE,IAAI,KAAG,EAAE,IAAIK,IAAE,QAAQ,MAAII,GAAE,MAAMJ,IAAEJ,EAAC,IAAGQ,KAAED,MAAGH,GAAEG,EAAC,MAAIC,GAAE,SAAO,EAAEJ,EAAC,MAAIL,GAAE,SAAOS,GAAE,MAAMJ,IAAEJ,EAAC,GAAE,UAAKD,GAAE,UAAQA,GAAE,eAAe;AAAG,aAAOA,GAAE,OAAKc,IAAEX,MAAGH,GAAE,mBAAmB,KAAGmB,GAAE,YAAU,UAAKA,GAAE,SAAS,MAAMG,GAAE,IAAI,GAAErB,EAAC,KAAG,CAAC,EAAEC,EAAC,KAAGM,MAAG,EAAEN,GAAEY,EAAC,CAAC,KAAG,CAAC,EAAEZ,EAAC,OAAKI,KAAEJ,GAAEM,EAAC,OAAKN,GAAEM,EAAC,IAAE,OAAM,GAAG,MAAM,YAAUM,IAAEd,GAAE,qBAAqB,KAAGqB,GAAE,iBAAiBP,IAAE,EAAE,GAAEZ,GAAEY,EAAC,EAAE,GAAEd,GAAE,qBAAqB,KAAGqB,GAAE,oBAAoBP,IAAE,EAAE,GAAE,GAAG,MAAM,YAAU,QAAOR,OAAIJ,GAAEM,EAAC,IAAEF,MAAIN,GAAE;AAAA,IAAM;AAAA,EAAC,GAAE,UAAS,SAASA,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAE,GAAG,OAAO,IAAI,GAAG,SAAMD,IAAE,EAAC,MAAKF,IAAE,aAAY,KAAE,CAAC;AAAE,OAAG,MAAM,QAAQG,IAAE,MAAKF,EAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,GAAG,OAAO,EAAC,SAAQ,SAASD,IAAEC,IAAE;AAAC,WAAO,KAAK,KAAK,WAAU;AAAC,SAAG,MAAM,QAAQD,IAAEC,IAAE,IAAI;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,gBAAe,SAASD,IAAEC,IAAE;AAAC,QAAIC,KAAE,KAAK,CAAC;AAAE,QAAGA,GAAE,QAAO,GAAG,MAAM,QAAQF,IAAEC,IAAEC,IAAE,IAAE;AAAA,EAAC,EAAC,CAAC;AAAE,MAAI,KAAG,SAAQ,KAAG,UAAS,KAAG,yCAAwC,KAAG;AAAqC,WAAS,GAAGA,IAAEF,IAAEG,IAAEC,IAAE;AAAC,QAAIH;AAAE,QAAG,MAAM,QAAQD,EAAC,EAAE,IAAG,KAAKA,IAAE,SAASA,IAAEC,IAAE;AAAC,MAAAE,MAAG,GAAG,KAAKD,EAAC,IAAEE,GAAEF,IAAED,EAAC,IAAE,GAAGC,KAAE,OAAK,YAAU,OAAOD,MAAG,QAAMA,KAAED,KAAE,MAAI,KAAIC,IAAEE,IAAEC,EAAC;AAAA,IAAC,CAAC;AAAA,aAAUD,MAAG,aAAW,EAAEH,EAAC,EAAE,CAAAI,GAAEF,IAAEF,EAAC;AAAA,QAAO,MAAIC,MAAKD,GAAE,IAAGE,KAAE,MAAID,KAAE,KAAID,GAAEC,EAAC,GAAEE,IAAEC,EAAC;AAAA,EAAC;AAAC,KAAG,QAAM,SAASJ,IAAEC,IAAE;AAAC,QAAIC,IAAEC,KAAE,CAAC,GAAEC,KAAE,SAASJ,IAAEC,IAAE;AAAC,UAAIC,KAAE,EAAED,EAAC,IAAEA,GAAE,IAAEA;AAAE,MAAAE,GAAEA,GAAE,MAAM,IAAE,mBAAmBH,EAAC,IAAE,MAAI,mBAAmB,QAAME,KAAE,KAAGA,EAAC;AAAA,IAAC;AAAE,QAAG,QAAMF,GAAE,QAAM;AAAG,QAAG,MAAM,QAAQA,EAAC,KAAGA,GAAE,UAAQ,CAAC,GAAG,cAAcA,EAAC,EAAE,IAAG,KAAKA,IAAE,WAAU;AAAC,MAAAI,GAAE,KAAK,MAAK,KAAK,KAAK;AAAA,IAAC,CAAC;AAAA,QAAO,MAAIF,MAAKF,GAAE,IAAGE,IAAEF,GAAEE,EAAC,GAAED,IAAEG,EAAC;AAAE,WAAOD,GAAE,KAAK,GAAG;AAAA,EAAC,GAAE,GAAG,GAAG,OAAO,EAAC,WAAU,WAAU;AAAC,WAAO,GAAG,MAAM,KAAK,eAAe,CAAC;AAAA,EAAC,GAAE,gBAAe,WAAU;AAAC,WAAO,KAAK,IAAI,WAAU;AAAC,UAAIH,KAAE,GAAG,KAAK,MAAK,UAAU;AAAE,aAAOA,KAAE,GAAG,UAAUA,EAAC,IAAE;AAAA,IAAI,CAAC,EAAE,OAAO,WAAU;AAAC,UAAIA,KAAE,KAAK;AAAK,aAAO,KAAK,QAAM,CAAC,GAAG,IAAI,EAAE,GAAG,WAAW,KAAG,GAAG,KAAK,KAAK,QAAQ,KAAG,CAAC,GAAG,KAAKA,EAAC,MAAI,KAAK,WAAS,CAAC,GAAG,KAAKA,EAAC;AAAA,IAAE,CAAC,EAAE,IAAI,SAASA,IAAEC,IAAE;AAAC,UAAIC,KAAE,GAAG,IAAI,EAAE,IAAI;AAAE,aAAO,QAAMA,KAAE,OAAK,MAAM,QAAQA,EAAC,IAAE,GAAG,IAAIA,IAAE,SAASF,IAAE;AAAC,eAAM,EAAC,MAAKC,GAAE,MAAK,OAAMD,GAAE,QAAQ,IAAG,MAAM,EAAC;AAAA,MAAC,CAAC,IAAE,EAAC,MAAKC,GAAE,MAAK,OAAMC,GAAE,QAAQ,IAAG,MAAM,EAAC;AAAA,IAAC,CAAC,EAAE,IAAI;AAAA,EAAC,EAAC,CAAC;AAAE,MAAI,KAAG,QAAO,KAAG,QAAO,KAAG,iBAAgB,KAAG,8BAA6B,KAAG,kBAAiB,KAAG,SAAQ,KAAG,CAAC,GAAE,KAAG,CAAC,GAAE,KAAG,KAAK,OAAO,GAAG,GAAE,KAAG,EAAE,cAAc,GAAG;AAAE,WAAS,GAAGG,IAAE;AAAC,WAAO,SAASL,IAAEC,IAAE;AAAC,kBAAU,OAAOD,OAAIC,KAAED,IAAEA,KAAE;AAAK,UAAIE,IAAEC,KAAE,GAAEC,KAAEJ,GAAE,YAAY,EAAE,MAAM,CAAC,KAAG,CAAC;AAAE,UAAG,EAAEC,EAAC,EAAE,QAAMC,KAAEE,GAAED,IAAG,EAAE,SAAMD,GAAE,CAAC,KAAGA,KAAEA,GAAE,MAAM,CAAC,KAAG,MAAKG,GAAEH,EAAC,IAAEG,GAAEH,EAAC,KAAG,CAAC,GAAG,QAAQD,EAAC,MAAII,GAAEH,EAAC,IAAEG,GAAEH,EAAC,KAAG,CAAC,GAAG,KAAKD,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAEG,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAE,CAAC,GAAEC,KAAEP,OAAI;AAAG,aAASQ,GAAET,IAAE;AAAC,UAAIG;AAAE,aAAOI,GAAEP,EAAC,IAAE,MAAG,GAAG,KAAKC,GAAED,EAAC,KAAG,CAAC,GAAE,SAASA,IAAEC,IAAE;AAAC,YAAIC,KAAED,GAAEG,IAAEC,IAAEC,EAAC;AAAE,eAAM,YAAU,OAAOJ,MAAGM,MAAGD,GAAEL,EAAC,IAAEM,KAAE,EAAEL,KAAED,MAAG,UAAQE,GAAE,UAAU,QAAQF,EAAC,GAAEO,GAAEP,EAAC,GAAE;AAAA,MAAG,CAAC,GAAEC;AAAA,IAAC;AAAC,WAAOM,GAAEL,GAAE,UAAU,CAAC,CAAC,KAAG,CAACG,GAAE,GAAG,KAAGE,GAAE,GAAG;AAAA,EAAC;AAAC,WAAS,GAAGT,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAE,GAAG,aAAa,eAAa,CAAC;AAAE,SAAIF,MAAKD,GAAE,YAASA,GAAEC,EAAC,OAAKE,GAAEF,EAAC,IAAEF,KAAEG,OAAIA,KAAE,CAAC,IAAID,EAAC,IAAED,GAAEC,EAAC;AAAG,WAAOC,MAAG,GAAG,OAAO,MAAGH,IAAEG,EAAC,GAAEH;AAAA,EAAC;AAAC,KAAG,OAAK,GAAG,MAAK,GAAG,OAAO,EAAC,QAAO,GAAE,cAAa,CAAC,GAAE,MAAK,CAAC,GAAE,cAAa,EAAC,KAAI,GAAG,MAAK,MAAK,OAAM,SAAQ,4DAA4D,KAAK,GAAG,QAAQ,GAAE,QAAO,MAAG,aAAY,MAAG,OAAM,MAAG,aAAY,oDAAmD,SAAQ,EAAC,KAAI,IAAG,MAAK,cAAa,MAAK,aAAY,KAAI,6BAA4B,MAAK,oCAAmC,GAAE,UAAS,EAAC,KAAI,WAAU,MAAK,UAAS,MAAK,WAAU,GAAE,gBAAe,EAAC,KAAI,eAAc,MAAK,gBAAe,MAAK,eAAc,GAAE,YAAW,EAAC,UAAS,QAAO,aAAY,MAAG,aAAY,KAAK,OAAM,YAAW,GAAG,SAAQ,GAAE,aAAY,EAAC,KAAI,MAAG,SAAQ,KAAE,EAAC,GAAE,WAAU,SAASA,IAAEC,IAAE;AAAC,WAAOA,KAAE,GAAG,GAAGD,IAAE,GAAG,YAAY,GAAEC,EAAC,IAAE,GAAG,GAAG,cAAaD,EAAC;AAAA,EAAC,GAAE,eAAc,GAAG,EAAE,GAAE,eAAc,GAAG,EAAE,GAAE,MAAK,SAASA,IAAEC,IAAE;AAAC,gBAAU,OAAOD,OAAIC,KAAED,IAAEA,KAAE,SAAQC,KAAEA,MAAG,CAAC;AAAE,QAAIkB,IAAEE,IAAEC,IAAEpB,IAAEY,IAAEX,IAAEiB,IAAEG,IAAEnB,IAAEC,IAAEmB,KAAE,GAAG,UAAU,CAAC,GAAEvB,EAAC,GAAEwB,KAAED,GAAE,WAASA,IAAEE,KAAEF,GAAE,YAAUC,GAAE,YAAUA,GAAE,UAAQ,GAAGA,EAAC,IAAE,GAAG,OAAME,KAAE,GAAG,SAAS,GAAEjB,KAAE,GAAG,UAAU,aAAa,GAAEC,KAAEa,GAAE,cAAY,CAAC,GAAElB,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEC,KAAE,YAAWI,KAAE,EAAC,YAAW,GAAE,mBAAkB,SAASZ,IAAE;AAAC,UAAIC;AAAE,UAAGmB,IAAE;AAAC,YAAG,CAAClB,IAAE;AAAC,UAAAA,KAAE,CAAC;AAAE,iBAAMD,KAAE,GAAG,KAAKqB,EAAC,EAAE,CAAApB,GAAED,GAAE,CAAC,EAAE,YAAY,IAAE,GAAG,KAAGC,GAAED,GAAE,CAAC,EAAE,YAAY,IAAE,GAAG,KAAG,CAAC,GAAG,OAAOA,GAAE,CAAC,CAAC;AAAA,QAAC;AAAC,QAAAA,KAAEC,GAAEF,GAAE,YAAY,IAAE,GAAG;AAAA,MAAC;AAAC,aAAO,QAAMC,KAAE,OAAKA,GAAE,KAAK,IAAI;AAAA,IAAC,GAAE,uBAAsB,WAAU;AAAC,aAAOmB,KAAEE,KAAE;AAAA,IAAI,GAAE,kBAAiB,SAAStB,IAAEC,IAAE;AAAC,aAAO,QAAMmB,OAAIpB,KAAEO,GAAEP,GAAE,YAAY,CAAC,IAAEO,GAAEP,GAAE,YAAY,CAAC,KAAGA,IAAEM,GAAEN,EAAC,IAAEC,KAAG;AAAA,IAAI,GAAE,kBAAiB,SAASD,IAAE;AAAC,aAAO,QAAMoB,OAAII,GAAE,WAASxB,KAAG;AAAA,IAAI,GAAE,YAAW,SAASA,IAAE;AAAC,UAAIC;AAAE,UAAGD,GAAE,KAAGoB,GAAE,CAAAR,GAAE,OAAOZ,GAAEY,GAAE,MAAM,CAAC;AAAA,UAAO,MAAIX,MAAKD,GAAE,CAAAW,GAAEV,EAAC,IAAE,CAACU,GAAEV,EAAC,GAAED,GAAEC,EAAC,CAAC;AAAE,aAAO;AAAA,IAAI,GAAE,OAAM,SAASD,IAAE;AAAC,UAAIC,KAAED,MAAGQ;AAAE,aAAOW,MAAGA,GAAE,MAAMlB,EAAC,GAAEQ,GAAE,GAAER,EAAC,GAAE;AAAA,IAAI,EAAC;AAAE,QAAG0B,GAAE,QAAQf,EAAC,GAAEY,GAAE,QAAMxB,MAAGwB,GAAE,OAAK,GAAG,QAAM,IAAI,QAAQ,IAAG,GAAG,WAAS,IAAI,GAAEA,GAAE,OAAKvB,GAAE,UAAQA,GAAE,QAAMuB,GAAE,UAAQA,GAAE,MAAKA,GAAE,aAAWA,GAAE,YAAU,KAAK,YAAY,EAAE,MAAM,CAAC,KAAG,CAAC,EAAE,GAAE,QAAMA,GAAE,aAAY;AAAC,MAAArB,KAAE,EAAE,cAAc,GAAG;AAAE,UAAG;AAAC,QAAAA,GAAE,OAAKqB,GAAE,KAAIrB,GAAE,OAAKA,GAAE,MAAKqB,GAAE,cAAY,GAAG,WAAS,OAAK,GAAG,QAAMrB,GAAE,WAAS,OAAKA,GAAE;AAAA,MAAI,SAAOH,IAAE;AAAC,QAAAwB,GAAE,cAAY;AAAA,MAAE;AAAA,IAAC;AAAC,QAAGA,GAAE,QAAMA,GAAE,eAAa,YAAU,OAAOA,GAAE,SAAOA,GAAE,OAAK,GAAG,MAAMA,GAAE,MAAKA,GAAE,WAAW,IAAG,GAAG,IAAGA,IAAEvB,IAAEW,EAAC,GAAEQ,GAAE,QAAOR;AAAE,SAAIR,OAAKmB,KAAE,GAAG,SAAOC,GAAE,WAAS,KAAG,GAAG,YAAU,GAAG,MAAM,QAAQ,WAAW,GAAEA,GAAE,OAAKA,GAAE,KAAK,YAAY,GAAEA,GAAE,aAAW,CAAC,GAAG,KAAKA,GAAE,IAAI,GAAEH,KAAEG,GAAE,IAAI,QAAQ,IAAG,EAAE,GAAEA,GAAE,aAAWA,GAAE,QAAMA,GAAE,eAAa,OAAKA,GAAE,eAAa,IAAI,QAAQ,mCAAmC,MAAIA,GAAE,OAAKA,GAAE,KAAK,QAAQ,IAAG,GAAG,MAAInB,KAAEmB,GAAE,IAAI,MAAMH,GAAE,MAAM,GAAEG,GAAE,SAAOA,GAAE,eAAa,YAAU,OAAOA,GAAE,UAAQH,OAAI,GAAG,KAAKA,EAAC,IAAE,MAAI,OAAKG,GAAE,MAAK,OAAOA,GAAE,OAAM,UAAKA,GAAE,UAAQH,KAAEA,GAAE,QAAQ,IAAG,IAAI,GAAEhB,MAAG,GAAG,KAAKgB,EAAC,IAAE,MAAI,OAAK,OAAK,GAAG,SAAOhB,KAAGmB,GAAE,MAAIH,KAAEhB,KAAGmB,GAAE,eAAa,GAAG,aAAaH,EAAC,KAAGT,GAAE,iBAAiB,qBAAoB,GAAG,aAAaS,EAAC,CAAC,GAAE,GAAG,KAAKA,EAAC,KAAGT,GAAE,iBAAiB,iBAAgB,GAAG,KAAKS,EAAC,CAAC,KAAIG,GAAE,QAAMA,GAAE,cAAY,UAAKA,GAAE,eAAavB,GAAE,gBAAcW,GAAE,iBAAiB,gBAAeY,GAAE,WAAW,GAAEZ,GAAE,iBAAiB,UAASY,GAAE,UAAU,CAAC,KAAGA,GAAE,QAAQA,GAAE,UAAU,CAAC,CAAC,IAAEA,GAAE,QAAQA,GAAE,UAAU,CAAC,CAAC,KAAG,QAAMA,GAAE,UAAU,CAAC,IAAE,OAAK,KAAG,aAAW,MAAIA,GAAE,QAAQ,GAAG,CAAC,GAAEA,GAAE,QAAQ,CAAAZ,GAAE,iBAAiBR,IAAEoB,GAAE,QAAQpB,EAAC,CAAC;AAAE,QAAGoB,GAAE,eAAa,UAAKA,GAAE,WAAW,KAAKC,IAAEb,IAAEY,EAAC,KAAGJ,IAAG,QAAOR,GAAE,MAAM;AAAE,QAAGJ,KAAE,SAAQE,GAAE,IAAIc,GAAE,QAAQ,GAAEZ,GAAE,KAAKY,GAAE,OAAO,GAAEZ,GAAE,KAAKY,GAAE,KAAK,GAAEL,KAAE,GAAG,IAAGK,IAAEvB,IAAEW,EAAC,GAAE;AAAC,UAAGA,GAAE,aAAW,GAAEW,MAAGG,GAAE,QAAQ,YAAW,CAACd,IAAEY,EAAC,CAAC,GAAEJ,GAAE,QAAOR;AAAE,MAAAY,GAAE,SAAO,IAAEA,GAAE,YAAUV,KAAE,GAAG,WAAW,WAAU;AAAC,QAAAF,GAAE,MAAM,SAAS;AAAA,MAAC,GAAEY,GAAE,OAAO;AAAG,UAAG;AAAC,QAAAJ,KAAE,OAAGD,GAAE,KAAKb,IAAEG,EAAC;AAAA,MAAC,SAAOT,IAAE;AAAC,YAAGoB,GAAE,OAAMpB;AAAE,QAAAS,GAAE,IAAGT,EAAC;AAAA,MAAC;AAAA,IAAC,MAAM,CAAAS,GAAE,IAAG,cAAc;AAAE,aAASA,GAAET,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAER;AAAE,MAAAmB,OAAIA,KAAE,MAAGN,MAAG,GAAG,aAAaA,EAAC,GAAEK,KAAE,QAAOG,KAAEnB,MAAG,IAAGS,GAAE,aAAW,IAAEZ,KAAE,IAAE,GAAEI,KAAE,OAAKJ,MAAGA,KAAE,OAAK,QAAMA,IAAEE,OAAIK,KAAE,SAASP,IAAEC,IAAEC,IAAE;AAAC,YAAIC,IAAEC,IAAEC,IAAEC,IAAEC,KAAEP,GAAE,UAASQ,KAAER,GAAE;AAAU,eAAM,QAAMQ,GAAE,CAAC,EAAE,CAAAA,GAAE,MAAM,GAAE,WAASL,OAAIA,KAAEH,GAAE,YAAUC,GAAE,kBAAkB,cAAc;AAAG,YAAGE;AAAE,eAAIC,MAAKG,GAAE,KAAGA,GAAEH,EAAC,KAAGG,GAAEH,EAAC,EAAE,KAAKD,EAAC,GAAE;AAAC,YAAAK,GAAE,QAAQJ,EAAC;AAAE;AAAA,UAAK;AAAA;AAAC,YAAGI,GAAE,CAAC,KAAIN,GAAE,CAAAG,KAAEG,GAAE,CAAC;AAAA,aAAM;AAAC,eAAIJ,MAAKF,IAAE;AAAC,gBAAG,CAACM,GAAE,CAAC,KAAGR,GAAE,WAAWI,KAAE,MAAII,GAAE,CAAC,CAAC,GAAE;AAAC,cAAAH,KAAED;AAAE;AAAA,YAAK;AAAC,YAAAE,OAAIA,KAAEF;AAAA,UAAE;AAAC,UAAAC,KAAEA,MAAGC;AAAA,QAAC;AAAC,YAAGD,GAAE,QAAOA,OAAIG,GAAE,CAAC,KAAGA,GAAE,QAAQH,EAAC,GAAEH,GAAEG,EAAC;AAAA,MAAC,EAAEmB,IAAEZ,IAAEV,EAAC,IAAG,CAACE,MAAG,KAAG,GAAG,QAAQ,UAASoB,GAAE,SAAS,KAAG,GAAG,QAAQ,QAAOA,GAAE,SAAS,IAAE,MAAIA,GAAE,WAAW,aAAa,IAAE,WAAU;AAAA,MAAC,IAAGjB,KAAE,SAASP,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,CAAC,GAAEU,KAAEnB,GAAE,UAAU,MAAM;AAAE,YAAGmB,GAAE,CAAC,EAAE,MAAIb,MAAKN,GAAE,WAAW,CAAAS,GAAEH,GAAE,YAAY,CAAC,IAAEN,GAAE,WAAWM,EAAC;AAAE,QAAAD,KAAEc,GAAE,MAAM;AAAE,eAAMd,GAAE,KAAGL,GAAE,eAAeK,EAAC,MAAIH,GAAEF,GAAE,eAAeK,EAAC,CAAC,IAAEJ,KAAG,CAACO,MAAGL,MAAGH,GAAE,eAAaC,KAAED,GAAE,WAAWC,IAAED,GAAE,QAAQ,IAAGQ,KAAEH,IAAEA,KAAEc,GAAE,MAAM;AAAE,cAAG,QAAMd,GAAE,CAAAA,KAAEG;AAAA,mBAAU,QAAMA,MAAGA,OAAIH,IAAE;AAAC,gBAAG,EAAEC,KAAEG,GAAED,KAAE,MAAIH,EAAC,KAAGI,GAAE,OAAKJ,EAAC;AAAG,mBAAID,MAAKK,GAAE,MAAIF,KAAEH,GAAE,MAAM,GAAG,GAAG,CAAC,MAAIC,OAAIC,KAAEG,GAAED,KAAE,MAAID,GAAE,CAAC,CAAC,KAAGE,GAAE,OAAKF,GAAE,CAAC,CAAC,IAAG;AAAC,yBAAKD,KAAEA,KAAEG,GAAEL,EAAC,IAAE,SAAKK,GAAEL,EAAC,MAAIC,KAAEE,GAAE,CAAC,GAAEY,GAAE,QAAQZ,GAAE,CAAC,CAAC;AAAG;AAAA,cAAK;AAAA;AAAC,gBAAG,SAAKD,GAAE,KAAGA,MAAGN,GAAE,QAAQ,EAAE,CAAAC,KAAEK,GAAEL,EAAC;AAAA,gBAAO,KAAG;AAAC,cAAAA,KAAEK,GAAEL,EAAC;AAAA,YAAC,SAAOD,IAAE;AAAC,qBAAM,EAAC,OAAM,eAAc,OAAMM,KAAEN,KAAE,wBAAsBQ,KAAE,SAAOH,GAAC;AAAA,YAAC;AAAA,UAAC;AAAA;AAAC,eAAM,EAAC,OAAM,WAAU,MAAKJ,GAAC;AAAA,MAAC,EAAEuB,IAAEjB,IAAEK,IAAER,EAAC,GAAEA,MAAGoB,GAAE,gBAAchB,KAAEI,GAAE,kBAAkB,eAAe,OAAK,GAAG,aAAaS,EAAC,IAAEb,MAAIA,KAAEI,GAAE,kBAAkB,MAAM,OAAK,GAAG,KAAKS,EAAC,IAAEb,MAAI,QAAMR,MAAG,WAASwB,GAAE,OAAKf,KAAE,cAAY,QAAMT,KAAES,KAAE,iBAAeA,KAAEF,GAAE,OAAMF,KAAEE,GAAE,MAAKH,KAAE,EAAEE,KAAEC,GAAE,YAAUD,KAAEG,IAAE,CAACT,MAAGS,OAAIA,KAAE,SAAQT,KAAE,MAAIA,KAAE,MAAKY,GAAE,SAAOZ,IAAEY,GAAE,cAAYX,MAAGQ,MAAG,IAAGL,KAAEuB,GAAE,YAAYF,IAAE,CAACpB,IAAEI,IAAEG,EAAC,CAAC,IAAEe,GAAE,WAAWF,IAAE,CAACb,IAAEH,IAAEH,EAAC,CAAC,GAAEM,GAAE,WAAWD,EAAC,GAAEA,KAAE,QAAOY,MAAGG,GAAE,QAAQtB,KAAE,gBAAc,aAAY,CAACQ,IAAEY,IAAEpB,KAAEC,KAAEC,EAAC,CAAC,GAAEI,GAAE,SAASe,IAAE,CAACb,IAAEH,EAAC,CAAC,GAAEc,OAAIG,GAAE,QAAQ,gBAAe,CAACd,IAAEY,EAAC,CAAC,GAAE,EAAE,GAAG,UAAQ,GAAG,MAAM,QAAQ,UAAU;AAAA,IAAG;AAAC,WAAOZ;AAAA,EAAC,GAAE,SAAQ,SAASZ,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAIF,IAAEC,IAAEC,IAAE,MAAM;AAAA,EAAC,GAAE,WAAU,SAASF,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAID,IAAE,QAAOC,IAAE,QAAQ;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,KAAK,CAAC,OAAM,MAAM,GAAE,SAASD,IAAEI,IAAE;AAAC,OAAGA,EAAC,IAAE,SAASJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,aAAO,EAAEF,EAAC,MAAIE,KAAEA,MAAGD,IAAEA,KAAED,IAAEA,KAAE,SAAQ,GAAG,KAAK,GAAG,OAAO,EAAC,KAAID,IAAE,MAAKI,IAAE,UAASD,IAAE,MAAKF,IAAE,SAAQC,GAAC,GAAE,GAAG,cAAcF,EAAC,KAAGA,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC,CAAC,GAAE,GAAG,cAAc,SAASA,IAAE;AAAC,QAAIC;AAAE,SAAIA,MAAKD,GAAE,QAAQ,oBAAiBC,GAAE,YAAY,MAAID,GAAE,cAAYA,GAAE,QAAQC,EAAC,KAAG;AAAA,EAAG,CAAC,GAAE,GAAG,WAAS,SAASD,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAG,KAAK,EAAC,KAAIF,IAAE,MAAK,OAAM,UAAS,UAAS,OAAM,MAAG,OAAM,OAAG,QAAO,OAAG,YAAW,EAAC,eAAc,WAAU;AAAA,IAAC,EAAC,GAAE,YAAW,SAASA,IAAE;AAAC,SAAG,WAAWA,IAAEC,IAAEC,EAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC,GAAE,GAAG,GAAG,OAAO,EAAC,SAAQ,SAASF,IAAE;AAAC,QAAIC;AAAE,WAAO,KAAK,CAAC,MAAI,EAAED,EAAC,MAAIA,KAAEA,GAAE,KAAK,KAAK,CAAC,CAAC,IAAGC,KAAE,GAAGD,IAAE,KAAK,CAAC,EAAE,aAAa,EAAE,GAAG,CAAC,EAAE,MAAM,IAAE,GAAE,KAAK,CAAC,EAAE,cAAYC,GAAE,aAAa,KAAK,CAAC,CAAC,GAAEA,GAAE,IAAI,WAAU;AAAC,UAAID,KAAE;AAAK,aAAMA,GAAE,kBAAkB,CAAAA,KAAEA,GAAE;AAAkB,aAAOA;AAAA,IAAC,CAAC,EAAE,OAAO,IAAI,IAAG;AAAA,EAAI,GAAE,WAAU,SAASE,IAAE;AAAC,WAAO,EAAEA,EAAC,IAAE,KAAK,KAAK,SAASF,IAAE;AAAC,SAAG,IAAI,EAAE,UAAUE,GAAE,KAAK,MAAKF,EAAC,CAAC;AAAA,IAAC,CAAC,IAAE,KAAK,KAAK,WAAU;AAAC,UAAIA,KAAE,GAAG,IAAI,GAAEC,KAAED,GAAE,SAAS;AAAE,MAAAC,GAAE,SAAOA,GAAE,QAAQC,EAAC,IAAEF,GAAE,OAAOE,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,MAAK,SAASD,IAAE;AAAC,QAAIC,KAAE,EAAED,EAAC;AAAE,WAAO,KAAK,KAAK,SAASD,IAAE;AAAC,SAAG,IAAI,EAAE,QAAQE,KAAED,GAAE,KAAK,MAAKD,EAAC,IAAEC,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,QAAO,SAASD,IAAE;AAAC,WAAO,KAAK,OAAOA,EAAC,EAAE,IAAI,MAAM,EAAE,KAAK,WAAU;AAAC,SAAG,IAAI,EAAE,YAAY,KAAK,UAAU;AAAA,IAAC,CAAC,GAAE;AAAA,EAAI,EAAC,CAAC,GAAE,GAAG,KAAK,QAAQ,SAAO,SAASA,IAAE;AAAC,WAAM,CAAC,GAAG,KAAK,QAAQ,QAAQA,EAAC;AAAA,EAAC,GAAE,GAAG,KAAK,QAAQ,UAAQ,SAASA,IAAE;AAAC,WAAM,CAAC,EAAEA,GAAE,eAAaA,GAAE,gBAAcA,GAAE,eAAe,EAAE;AAAA,EAAO,GAAE,GAAG,aAAa,MAAI,WAAU;AAAC,QAAG;AAAC,aAAO,IAAI,GAAG;AAAA,IAAc,SAAOA,IAAE;AAAA,IAAC;AAAA,EAAC;AAAE,MAAI,KAAG,EAAC,GAAE,KAAI,MAAK,IAAG,GAAE,KAAG,GAAG,aAAa,IAAI;AAAE,KAAG,OAAK,CAAC,CAAC,MAAI,qBAAoB,IAAG,GAAG,OAAK,KAAG,CAAC,CAAC,IAAG,GAAG,cAAc,SAASI,IAAE;AAAC,QAAIC,IAAEC;AAAE,QAAG,GAAG,QAAM,MAAI,CAACF,GAAE,YAAY,QAAM,EAAC,MAAK,SAASJ,IAAEC,IAAE;AAAC,UAAIC,IAAEC,KAAEC,GAAE,IAAI;AAAE,UAAGD,GAAE,KAAKC,GAAE,MAAKA,GAAE,KAAIA,GAAE,OAAMA,GAAE,UAASA,GAAE,QAAQ,GAAEA,GAAE,UAAU,MAAIF,MAAKE,GAAE,UAAU,CAAAD,GAAED,EAAC,IAAEE,GAAE,UAAUF,EAAC;AAAE,WAAIA,MAAKE,GAAE,YAAUD,GAAE,oBAAkBA,GAAE,iBAAiBC,GAAE,QAAQ,GAAEA,GAAE,eAAaJ,GAAE,kBAAkB,MAAIA,GAAE,kBAAkB,IAAE,mBAAkBA,GAAE,CAAAG,GAAE,iBAAiBD,IAAEF,GAAEE,EAAC,CAAC;AAAE,MAAAG,KAAE,SAASL,IAAE;AAAC,eAAO,WAAU;AAAC,UAAAK,OAAIA,KAAEC,KAAEH,GAAE,SAAOA,GAAE,UAAQA,GAAE,UAAQA,GAAE,YAAUA,GAAE,qBAAmB,MAAK,YAAUH,KAAEG,GAAE,MAAM,IAAE,YAAUH,KAAE,YAAU,OAAOG,GAAE,SAAOF,GAAE,GAAE,OAAO,IAAEA,GAAEE,GAAE,QAAOA,GAAE,UAAU,IAAEF,GAAE,GAAGE,GAAE,MAAM,KAAGA,GAAE,QAAOA,GAAE,YAAW,YAAUA,GAAE,gBAAc,WAAS,YAAU,OAAOA,GAAE,eAAa,EAAC,QAAOA,GAAE,SAAQ,IAAE,EAAC,MAAKA,GAAE,aAAY,GAAEA,GAAE,sBAAsB,CAAC;AAAA,QAAE;AAAA,MAAC,GAAEA,GAAE,SAAOE,GAAE,GAAEC,KAAEH,GAAE,UAAQA,GAAE,YAAUE,GAAE,OAAO,GAAE,WAASF,GAAE,UAAQA,GAAE,UAAQG,KAAEH,GAAE,qBAAmB,WAAU;AAAC,cAAIA,GAAE,cAAY,GAAG,WAAW,WAAU;AAAC,UAAAE,MAAGC,GAAE;AAAA,QAAC,CAAC;AAAA,MAAC,GAAED,KAAEA,GAAE,OAAO;AAAE,UAAG;AAAC,QAAAF,GAAE,KAAKC,GAAE,cAAYA,GAAE,QAAM,IAAI;AAAA,MAAC,SAAOJ,IAAE;AAAC,YAAGK,GAAE,OAAML;AAAA,MAAC;AAAA,IAAC,GAAE,OAAM,WAAU;AAAC,MAAAK,MAAGA,GAAE;AAAA,IAAC,EAAC;AAAA,EAAC,CAAC,GAAE,GAAG,cAAc,SAASL,IAAE;AAAC,IAAAA,GAAE,gBAAcA,GAAE,SAAS,SAAO;AAAA,EAAG,CAAC,GAAE,GAAG,UAAU,EAAC,SAAQ,EAAC,QAAO,4FAA2F,GAAE,UAAS,EAAC,QAAO,0BAAyB,GAAE,YAAW,EAAC,eAAc,SAASA,IAAE;AAAC,WAAO,GAAG,WAAWA,EAAC,GAAEA;AAAA,EAAC,EAAC,EAAC,CAAC,GAAE,GAAG,cAAc,UAAS,SAASA,IAAE;AAAC,eAASA,GAAE,UAAQA,GAAE,QAAM,QAAIA,GAAE,gBAAcA,GAAE,OAAK;AAAA,EAAM,CAAC,GAAE,GAAG,cAAc,UAAS,SAASE,IAAE;AAAC,QAAIC,IAAEC;AAAE,QAAGF,GAAE,eAAaA,GAAE,YAAY,QAAM,EAAC,MAAK,SAASF,IAAEC,IAAE;AAAC,MAAAE,KAAE,GAAG,UAAU,EAAE,KAAKD,GAAE,eAAa,CAAC,CAAC,EAAE,KAAK,EAAC,SAAQA,GAAE,eAAc,KAAIA,GAAE,IAAG,CAAC,EAAE,GAAG,cAAaE,KAAE,SAASJ,IAAE;AAAC,QAAAG,GAAE,OAAO,GAAEC,KAAE,MAAKJ,MAAGC,GAAE,YAAUD,GAAE,OAAK,MAAI,KAAIA,GAAE,IAAI;AAAA,MAAC,CAAC,GAAE,EAAE,KAAK,YAAYG,GAAE,CAAC,CAAC;AAAA,IAAC,GAAE,OAAM,WAAU;AAAC,MAAAC,MAAGA,GAAE;AAAA,IAAC,EAAC;AAAA,EAAC,CAAC;AAAE,MAAI,IAAG,KAAG,CAAC,GAAE,KAAG;AAAoB,KAAG,UAAU,EAAC,OAAM,YAAW,eAAc,WAAU;AAAC,QAAIJ,KAAE,GAAG,IAAI,KAAG,GAAG,UAAQ,MAAI,GAAG;AAAO,WAAO,KAAKA,EAAC,IAAE,MAAGA;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,cAAc,cAAa,SAASA,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,KAAE,UAAKN,GAAE,UAAQ,GAAG,KAAKA,GAAE,GAAG,IAAE,QAAM,YAAU,OAAOA,GAAE,QAAM,OAAKA,GAAE,eAAa,IAAI,QAAQ,mCAAmC,KAAG,GAAG,KAAKA,GAAE,IAAI,KAAG;AAAQ,QAAGM,MAAG,YAAUN,GAAE,UAAU,CAAC,EAAE,QAAOG,KAAEH,GAAE,gBAAc,EAAEA,GAAE,aAAa,IAAEA,GAAE,cAAc,IAAEA,GAAE,eAAcM,KAAEN,GAAEM,EAAC,IAAEN,GAAEM,EAAC,EAAE,QAAQ,IAAG,OAAKH,EAAC,IAAE,UAAKH,GAAE,UAAQA,GAAE,QAAM,GAAG,KAAKA,GAAE,GAAG,IAAE,MAAI,OAAKA,GAAE,QAAM,MAAIG,KAAGH,GAAE,WAAW,aAAa,IAAE,WAAU;AAAC,aAAOK,MAAG,GAAG,MAAMF,KAAE,iBAAiB,GAAEE,GAAE,CAAC;AAAA,IAAC,GAAEL,GAAE,UAAU,CAAC,IAAE,QAAOI,KAAE,GAAGD,EAAC,GAAE,GAAGA,EAAC,IAAE,WAAU;AAAC,MAAAE,KAAE;AAAA,IAAS,GAAEH,GAAE,OAAO,WAAU;AAAC,iBAASE,KAAE,GAAG,EAAE,EAAE,WAAWD,EAAC,IAAE,GAAGA,EAAC,IAAEC,IAAEJ,GAAEG,EAAC,MAAIH,GAAE,gBAAcC,GAAE,eAAc,GAAG,KAAKE,EAAC,IAAGE,MAAG,EAAED,EAAC,KAAGA,GAAEC,GAAE,CAAC,CAAC,GAAEA,KAAED,KAAE;AAAA,IAAM,CAAC,GAAE;AAAA,EAAQ,CAAC,GAAE,GAAG,uBAAqB,KAAG,EAAE,eAAe,mBAAmB,EAAE,EAAE,MAAM,YAAU,8BAA6B,MAAI,GAAG,WAAW,SAAQ,GAAG,YAAU,SAASJ,IAAEC,IAAEC,IAAE;AAAC,WAAM,YAAU,OAAOF,KAAE,CAAC,KAAG,aAAW,OAAOC,OAAIC,KAAED,IAAEA,KAAE,QAAIA,OAAI,GAAG,uBAAqBE,MAAGF,KAAE,EAAE,eAAe,mBAAmB,EAAE,GAAG,cAAc,MAAM,GAAG,OAAK,EAAE,SAAS,MAAKA,GAAE,KAAK,YAAYE,EAAC,KAAGF,KAAE,IAAGI,KAAE,CAACH,MAAG,CAAC,IAAGE,KAAE,EAAE,KAAKJ,EAAC,KAAG,CAACC,GAAE,cAAcG,GAAE,CAAC,CAAC,CAAC,KAAGA,KAAE,GAAG,CAACJ,EAAC,GAAEC,IAAEI,EAAC,GAAEA,MAAGA,GAAE,UAAQ,GAAGA,EAAC,EAAE,OAAO,GAAE,GAAG,MAAM,CAAC,GAAED,GAAE,UAAU;AAAI,QAAID,IAAEC,IAAEC;AAAA,EAAC,GAAE,GAAG,GAAG,OAAK,SAASL,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,KAAE,MAAKC,KAAEP,GAAE,QAAQ,GAAG;AAAE,WAAM,KAAGO,OAAIJ,KAAE,GAAGH,GAAE,MAAMO,EAAC,CAAC,GAAEP,KAAEA,GAAE,MAAM,GAAEO,EAAC,IAAG,EAAEN,EAAC,KAAGC,KAAED,IAAEA,KAAE,UAAQA,MAAG,YAAU,OAAOA,OAAIG,KAAE,SAAQ,IAAEE,GAAE,UAAQ,GAAG,KAAK,EAAC,KAAIN,IAAE,MAAKI,MAAG,OAAM,UAAS,QAAO,MAAKH,GAAC,CAAC,EAAE,KAAK,SAASD,IAAE;AAAC,MAAAK,KAAE,WAAUC,GAAE,KAAKH,KAAE,GAAG,OAAO,EAAE,OAAO,GAAG,UAAUH,EAAC,CAAC,EAAE,KAAKG,EAAC,IAAEH,EAAC;AAAA,IAAC,CAAC,EAAE,OAAOE,MAAG,SAASF,IAAEC,IAAE;AAAC,MAAAK,GAAE,KAAK,WAAU;AAAC,QAAAJ,GAAE,MAAM,MAAKG,MAAG,CAACL,GAAE,cAAaC,IAAED,EAAC,CAAC;AAAA,MAAC,CAAC;AAAA,IAAC,CAAC,GAAE;AAAA,EAAI,GAAE,GAAG,KAAK,QAAQ,WAAS,SAASC,IAAE;AAAC,WAAO,GAAG,KAAK,GAAG,QAAO,SAASD,IAAE;AAAC,aAAOC,OAAID,GAAE;AAAA,IAAI,CAAC,EAAE;AAAA,EAAM,GAAE,GAAG,SAAO,EAAC,WAAU,SAASA,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,GAAG,IAAIT,IAAE,UAAU,GAAEmB,KAAE,GAAGnB,EAAC,GAAEqB,KAAE,CAAC;AAAE,iBAAWZ,OAAIT,GAAE,MAAM,WAAS,aAAYO,KAAEY,GAAE,OAAO,GAAEd,KAAE,GAAG,IAAIL,IAAE,KAAK,GAAEQ,KAAE,GAAG,IAAIR,IAAE,MAAM,IAAG,eAAaS,MAAG,YAAUA,OAAI,MAAIJ,KAAEG,IAAG,QAAQ,MAAM,KAAGF,MAAGH,KAAEgB,GAAE,SAAS,GAAG,KAAIf,KAAED,GAAE,SAAOG,KAAE,WAAWD,EAAC,KAAG,GAAED,KAAE,WAAWI,EAAC,KAAG,IAAG,EAAEP,EAAC,MAAIA,KAAEA,GAAE,KAAKD,IAAEE,IAAE,GAAG,OAAO,CAAC,GAAEK,EAAC,CAAC,IAAG,QAAMN,GAAE,QAAMoB,GAAE,MAAIpB,GAAE,MAAIM,GAAE,MAAID,KAAG,QAAML,GAAE,SAAOoB,GAAE,OAAKpB,GAAE,OAAKM,GAAE,OAAKH,KAAG,WAAUH,KAAEA,GAAE,MAAM,KAAKD,IAAEqB,EAAC,IAAEF,GAAE,IAAIE,EAAC;AAAA,EAAC,EAAC,GAAE,GAAG,GAAG,OAAO,EAAC,QAAO,SAASpB,IAAE;AAAC,QAAG,UAAU,OAAO,QAAO,WAASA,KAAE,OAAK,KAAK,KAAK,SAASD,IAAE;AAAC,SAAG,OAAO,UAAU,MAAKC,IAAED,EAAC;AAAA,IAAC,CAAC;AAAE,QAAIA,IAAEE,IAAEC,KAAE,KAAK,CAAC;AAAE,WAAOA,KAAEA,GAAE,eAAe,EAAE,UAAQH,KAAEG,GAAE,sBAAsB,GAAED,KAAEC,GAAE,cAAc,aAAY,EAAC,KAAIH,GAAE,MAAIE,GAAE,aAAY,MAAKF,GAAE,OAAKE,GAAE,YAAW,KAAG,EAAC,KAAI,GAAE,MAAK,EAAC,IAAE;AAAA,EAAM,GAAE,UAAS,WAAU;AAAC,QAAG,KAAK,CAAC,GAAE;AAAC,UAAIF,IAAEC,IAAEC,IAAEC,KAAE,KAAK,CAAC,GAAEC,KAAE,EAAC,KAAI,GAAE,MAAK,EAAC;AAAE,UAAG,YAAU,GAAG,IAAID,IAAE,UAAU,EAAE,CAAAF,KAAEE,GAAE,sBAAsB;AAAA,WAAM;AAAC,QAAAF,KAAE,KAAK,OAAO,GAAEC,KAAEC,GAAE,eAAcH,KAAEG,GAAE,gBAAcD,GAAE;AAAgB,eAAMF,OAAIA,OAAIE,GAAE,QAAMF,OAAIE,GAAE,oBAAkB,aAAW,GAAG,IAAIF,IAAE,UAAU,EAAE,CAAAA,KAAEA,GAAE;AAAW,QAAAA,MAAGA,OAAIG,MAAG,MAAIH,GAAE,cAAYI,KAAE,GAAGJ,EAAC,EAAE,OAAO,GAAG,OAAK,GAAG,IAAIA,IAAE,kBAAiB,IAAE,GAAEI,GAAE,QAAM,GAAG,IAAIJ,IAAE,mBAAkB,IAAE;AAAA,MAAE;AAAC,aAAM,EAAC,KAAIC,GAAE,MAAIG,GAAE,MAAI,GAAG,IAAID,IAAE,aAAY,IAAE,GAAE,MAAKF,GAAE,OAAKG,GAAE,OAAK,GAAG,IAAID,IAAE,cAAa,IAAE,EAAC;AAAA,IAAC;AAAA,EAAC,GAAE,cAAa,WAAU;AAAC,WAAO,KAAK,IAAI,WAAU;AAAC,UAAIH,KAAE,KAAK;AAAa,aAAMA,MAAG,aAAW,GAAG,IAAIA,IAAE,UAAU,EAAE,CAAAA,KAAEA,GAAE;AAAa,aAAOA,MAAG;AAAA,IAAC,CAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,KAAK,EAAC,YAAW,eAAc,WAAU,cAAa,GAAE,SAASC,IAAEG,IAAE;AAAC,QAAIC,KAAE,kBAAgBD;AAAE,OAAG,GAAGH,EAAC,IAAE,SAASD,IAAE;AAAC,aAAO,EAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC,YAAIC;AAAE,YAAG,EAAEH,EAAC,IAAEG,KAAEH,KAAE,MAAIA,GAAE,aAAWG,KAAEH,GAAE,cAAa,WAASE,GAAE,QAAOC,KAAEA,GAAEC,EAAC,IAAEJ,GAAEC,EAAC;AAAE,QAAAE,KAAEA,GAAE,SAASE,KAAEF,GAAE,cAAYD,IAAEG,KAAEH,KAAEC,GAAE,WAAW,IAAEH,GAAEC,EAAC,IAAEC;AAAA,MAAC,GAAED,IAAED,IAAE,UAAU,MAAM;AAAA,IAAC;AAAA,EAAC,CAAC,GAAE,GAAG,KAAK,CAAC,OAAM,MAAM,GAAE,SAASA,IAAEE,IAAE;AAAC,OAAG,SAASA,EAAC,IAAE,GAAG,GAAG,eAAc,SAASF,IAAEC,IAAE;AAAC,UAAGA,GAAE,QAAOA,KAAE,GAAGD,IAAEE,EAAC,GAAE,GAAG,KAAKD,EAAC,IAAE,GAAGD,EAAC,EAAE,SAAS,EAAEE,EAAC,IAAE,OAAKD;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC,GAAE,GAAG,KAAK,EAAC,QAAO,UAAS,OAAM,QAAO,GAAE,SAASK,IAAEC,IAAE;AAAC,OAAG,KAAK,EAAC,SAAQ,UAAQD,IAAE,SAAQC,IAAE,IAAG,UAAQD,GAAC,GAAE,SAASH,IAAEE,IAAE;AAAC,SAAG,GAAGA,EAAC,IAAE,SAASL,IAAEC,IAAE;AAAC,YAAIC,KAAE,UAAU,WAASC,MAAG,aAAW,OAAOH,KAAGI,KAAED,OAAI,SAAKH,MAAG,SAAKC,KAAE,WAAS;AAAU,eAAO,EAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC,cAAIC;AAAE,iBAAO,EAAEH,EAAC,IAAE,MAAIK,GAAE,QAAQ,OAAO,IAAEL,GAAE,UAAQM,EAAC,IAAEN,GAAE,SAAS,gBAAgB,WAASM,EAAC,IAAE,MAAIN,GAAE,YAAUG,KAAEH,GAAE,iBAAgB,KAAK,IAAIA,GAAE,KAAK,WAASM,EAAC,GAAEH,GAAE,WAASG,EAAC,GAAEN,GAAE,KAAK,WAASM,EAAC,GAAEH,GAAE,WAASG,EAAC,GAAEH,GAAE,WAASG,EAAC,CAAC,KAAG,WAASJ,KAAE,GAAG,IAAIF,IAAEC,IAAEG,EAAC,IAAE,GAAG,MAAMJ,IAAEC,IAAEC,IAAEE,EAAC;AAAA,QAAC,GAAEG,IAAEL,KAAEF,KAAE,QAAOE,EAAC;AAAA,MAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC,GAAE,GAAG,KAAK,CAAC,aAAY,YAAW,gBAAe,aAAY,eAAc,UAAU,GAAE,SAASF,IAAEC,IAAE;AAAC,OAAG,GAAGA,EAAC,IAAE,SAASD,IAAE;AAAC,aAAO,KAAK,GAAGC,IAAED,EAAC;AAAA,IAAC;AAAA,EAAC,CAAC,GAAE,GAAG,GAAG,OAAO,EAAC,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,GAAGF,IAAE,MAAKC,IAAEC,EAAC;AAAA,EAAC,GAAE,QAAO,SAASF,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAID,IAAE,MAAKC,EAAC;AAAA,EAAC,GAAE,UAAS,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,GAAGF,IAAED,IAAEE,IAAEC,EAAC;AAAA,EAAC,GAAE,YAAW,SAASH,IAAEC,IAAEC,IAAE;AAAC,WAAO,MAAI,UAAU,SAAO,KAAK,IAAIF,IAAE,IAAI,IAAE,KAAK,IAAIC,IAAED,MAAG,MAAKE,EAAC;AAAA,EAAC,GAAE,OAAM,SAASF,IAAEC,IAAE;AAAC,WAAO,KAAK,GAAG,cAAaD,EAAC,EAAE,GAAG,cAAaC,MAAGD,EAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAG,KAAK,wLAAwL,MAAM,GAAG,GAAE,SAASA,IAAEE,IAAE;AAAC,OAAG,GAAGA,EAAC,IAAE,SAASF,IAAEC,IAAE;AAAC,aAAO,IAAE,UAAU,SAAO,KAAK,GAAGC,IAAE,MAAKF,IAAEC,EAAC,IAAE,KAAK,QAAQC,EAAC;AAAA,IAAC;AAAA,EAAC,CAAC;AAAE,MAAI,KAAG;AAAsD,KAAG,QAAM,SAASF,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC;AAAE,QAAG,YAAU,OAAOH,OAAIC,KAAEF,GAAEC,EAAC,GAAEA,KAAED,IAAEA,KAAEE,KAAG,EAAEF,EAAC,EAAE,QAAOG,KAAE,GAAG,KAAK,WAAU,CAAC,IAAGC,KAAE,WAAU;AAAC,aAAOJ,GAAE,MAAMC,MAAG,MAAKE,GAAE,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC;AAAA,IAAC,GAAG,OAAKH,GAAE,OAAKA,GAAE,QAAM,GAAG,QAAOI;AAAA,EAAC,GAAE,GAAG,YAAU,SAASJ,IAAE;AAAC,IAAAA,KAAE,GAAG,cAAY,GAAG,MAAM,IAAE;AAAA,EAAC,GAAE,GAAG,UAAQ,MAAM,SAAQ,GAAG,YAAU,KAAK,OAAM,GAAG,WAAS,IAAG,GAAG,aAAW,GAAE,GAAG,WAAS,GAAE,GAAG,YAAU,GAAE,GAAG,OAAK,GAAE,GAAG,MAAI,KAAK,KAAI,GAAG,YAAU,SAASA,IAAE;AAAC,QAAIC,KAAE,GAAG,KAAKD,EAAC;AAAE,YAAO,aAAWC,MAAG,aAAWA,OAAI,CAAC,MAAMD,KAAE,WAAWA,EAAC,CAAC;AAAA,EAAC,GAAE,GAAG,OAAK,SAASA,IAAE;AAAC,WAAO,QAAMA,KAAE,MAAIA,KAAE,IAAI,QAAQ,IAAG,IAAI;AAAA,EAAC,GAAE,cAAY,OAAO,UAAQ,OAAO,OAAK,OAAO,UAAS,CAAC,GAAE,WAAU;AAAC,WAAO;AAAA,EAAE,CAAC;AAAE,MAAI,KAAG,GAAG,QAAO,KAAG,GAAG;AAAE,SAAO,GAAG,aAAW,SAASA,IAAE;AAAC,WAAO,GAAG,MAAI,OAAK,GAAG,IAAE,KAAIA,MAAG,GAAG,WAAS,OAAK,GAAG,SAAO,KAAI;AAAA,EAAE,GAAE,eAAa,OAAO,MAAI,GAAG,SAAO,GAAG,IAAE,KAAI;AAAE,CAAC;AAEj5qF;AAAA;AAAA;AAAA;AAAA;AAKA,CAAC,SAAS,GAAE,GAAE;AAAC,cAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,YAAU,EAAE;AAAC,EAAE,MAAM,WAAU;AAAC;AAAa,QAAM,IAAE,oBAAI,OAAI,IAAE,EAAC,IAAIA,IAAEI,IAAEF,IAAE;AAAC,MAAE,IAAIF,EAAC,KAAG,EAAE,IAAIA,IAAE,oBAAI,KAAG;AAAE,UAAMO,KAAE,EAAE,IAAIP,EAAC;AAAE,IAAAO,GAAE,IAAIH,EAAC,KAAG,MAAIG,GAAE,OAAKA,GAAE,IAAIH,IAAEF,EAAC,IAAE,QAAQ,MAAM,+EAA+E,MAAM,KAAKK,GAAE,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG;AAAA,EAAC,GAAE,KAAI,CAACP,IAAEI,OAAI,EAAE,IAAIJ,EAAC,KAAG,EAAE,IAAIA,EAAC,EAAE,IAAII,EAAC,KAAG,MAAK,OAAOJ,IAAEI,IAAE;AAAC,QAAG,CAAC,EAAE,IAAIJ,EAAC,EAAE;AAAO,UAAME,KAAE,EAAE,IAAIF,EAAC;AAAE,IAAAE,GAAE,OAAOE,EAAC,GAAE,MAAIF,GAAE,QAAM,EAAE,OAAOF,EAAC;AAAA,EAAC,EAAC,GAAE,IAAE,iBAAgB,IAAE,CAAAC,QAAIA,MAAG,OAAO,OAAK,OAAO,IAAI,WAASA,KAAEA,GAAE,QAAQ,iBAAiB,CAACA,IAAED,OAAI,IAAI,IAAI,OAAOA,EAAC,CAAC,EAAG,IAAGC,KAAG,IAAE,CAAAA,OAAG;AAAC,IAAAA,GAAE,cAAc,IAAI,MAAM,CAAC,CAAC;AAAA,EAAC,GAAE,IAAE,CAAAA,OAAG,EAAE,CAACA,MAAG,YAAU,OAAOA,QAAK,WAASA,GAAE,WAASA,KAAEA,GAAE,CAAC,IAAG,WAASA,GAAE,WAAU,IAAE,CAAAA,OAAG,EAAEA,EAAC,IAAEA,GAAE,SAAOA,GAAE,CAAC,IAAEA,KAAE,YAAU,OAAOA,MAAGA,GAAE,SAAO,IAAE,SAAS,cAAc,EAAEA,EAAC,CAAC,IAAE,MAAK,IAAE,CAAAA,OAAG;AAAC,QAAG,CAAC,EAAEA,EAAC,KAAG,MAAIA,GAAE,eAAe,EAAE,OAAO,QAAM;AAAG,UAAMD,KAAE,cAAY,iBAAiBC,EAAC,EAAE,iBAAiB,YAAY,GAAEG,KAAEH,GAAE,QAAQ,qBAAqB;AAAE,QAAG,CAACG,GAAE,QAAOJ;AAAE,QAAGI,OAAIH,IAAE;AAAC,YAAMD,KAAEC,GAAE,QAAQ,SAAS;AAAE,UAAGD,MAAGA,GAAE,eAAaI,GAAE,QAAM;AAAG,UAAG,SAAOJ,GAAE,QAAM;AAAA,IAAE;AAAC,WAAOA;AAAA,EAAC,GAAE,IAAE,CAAAC,OAAG,CAACA,MAAGA,GAAE,aAAW,KAAK,gBAAc,CAAC,CAACA,GAAE,UAAU,SAAS,UAAU,MAAI,WAASA,GAAE,WAASA,GAAE,WAASA,GAAE,aAAa,UAAU,KAAG,YAAUA,GAAE,aAAa,UAAU,IAAG,IAAE,CAAAA,OAAG;AAAC,QAAG,CAAC,SAAS,gBAAgB,aAAa,QAAO;AAAK,QAAG,cAAY,OAAOA,GAAE,aAAY;AAAC,YAAMD,KAAEC,GAAE,YAAY;AAAE,aAAOD,cAAa,aAAWA,KAAE;AAAA,IAAI;AAAC,WAAOC,cAAa,aAAWA,KAAEA,GAAE,aAAW,EAAEA,GAAE,UAAU,IAAE;AAAA,EAAI,GAAE,IAAE,MAAI;AAAA,EAAC,GAAE,IAAE,CAAAA,OAAG;AAAC,IAAAA,GAAE;AAAA,EAAY,GAAE,IAAE,MAAI,OAAO,UAAQ,CAAC,SAAS,KAAK,aAAa,mBAAmB,IAAE,OAAO,SAAO,MAAK,IAAE,CAAC,GAAE,IAAE,MAAI,UAAQ,SAAS,gBAAgB,KAAI,IAAE,CAAAA,OAAG;AAAC,QAAID;AAAE,IAAAA,KAAE,MAAI;AAAC,YAAMA,KAAE,EAAE;AAAE,UAAGA,IAAE;AAAC,cAAMI,KAAEH,GAAE,MAAKC,KAAEF,GAAE,GAAGI,EAAC;AAAE,QAAAJ,GAAE,GAAGI,EAAC,IAAEH,GAAE,iBAAgBD,GAAE,GAAGI,EAAC,EAAE,cAAYH,IAAED,GAAE,GAAGI,EAAC,EAAE,aAAW,OAAKJ,GAAE,GAAGI,EAAC,IAAEF,IAAED,GAAE;AAAA,MAAgB;AAAA,IAAC,GAAE,cAAY,SAAS,cAAY,EAAE,UAAQ,SAAS,iBAAiB,oBAAoB,MAAI;AAAC,iBAAUA,MAAK,EAAE,CAAAA,GAAE;AAAA,IAAC,CAAE,GAAE,EAAE,KAAKD,EAAC,KAAGA,GAAE;AAAA,EAAC,GAAE,IAAE,CAACC,IAAED,KAAE,CAAC,GAAEI,KAAEH,OAAI,cAAY,OAAOA,KAAEA,GAAE,KAAK,GAAGD,EAAC,IAAEI,IAAE,IAAE,CAACH,IAAED,IAAEE,KAAE,SAAK;AAAC,QAAG,CAACA,GAAE,QAAO,KAAK,EAAED,EAAC;AAAE,UAAMI,MAAG,CAAAJ,OAAG;AAAC,UAAG,CAACA,GAAE,QAAO;AAAE,UAAG,EAAC,oBAAmBD,IAAE,iBAAgBI,GAAC,IAAE,OAAO,iBAAiBH,EAAC;AAAE,YAAMC,KAAE,OAAO,WAAWF,EAAC,GAAEO,KAAE,OAAO,WAAWH,EAAC;AAAE,aAAOF,MAAGK,MAAGP,KAAEA,GAAE,MAAM,GAAG,EAAE,CAAC,GAAEI,KAAEA,GAAE,MAAM,GAAG,EAAE,CAAC,GAAE,OAAK,OAAO,WAAWJ,EAAC,IAAE,OAAO,WAAWI,EAAC,MAAI;AAAA,IAAC,GAAGJ,EAAC,IAAE;AAAE,QAAIG,KAAE;AAAG,UAAMG,KAAE,CAAC,EAAC,QAAOJ,GAAC,MAAI;AAAC,MAAAA,OAAIF,OAAIG,KAAE,MAAGH,GAAE,oBAAoB,GAAEM,EAAC,GAAE,EAAEL,EAAC;AAAA,IAAE;AAAE,IAAAD,GAAE,iBAAiB,GAAEM,EAAC,GAAE,WAAY,MAAI;AAAC,MAAAH,MAAG,EAAEH,EAAC;AAAA,IAAC,GAAGK,EAAC;AAAA,EAAC,GAAE,IAAE,CAACJ,IAAED,IAAEI,IAAEF,OAAI;AAAC,UAAMK,KAAEN,GAAE;AAAO,QAAII,KAAEJ,GAAE,QAAQD,EAAC;AAAE,WAAM,OAAKK,KAAE,CAACD,MAAGF,KAAED,GAAEM,KAAE,CAAC,IAAEN,GAAE,CAAC,KAAGI,MAAGD,KAAE,IAAE,IAAGF,OAAIG,MAAGA,KAAEE,MAAGA,KAAGN,GAAE,KAAK,IAAI,GAAE,KAAK,IAAII,IAAEE,KAAE,CAAC,CAAC,CAAC;AAAA,EAAE,GAAE,IAAE,sBAAqB,IAAE,QAAO,IAAE,UAAS,IAAE,CAAC;AAAE,MAAI,IAAE;AAAE,QAAM,IAAE,EAAC,YAAW,aAAY,YAAW,WAAU,GAAE,IAAE,oBAAI,IAAI,CAAC,SAAQ,YAAW,WAAU,aAAY,eAAc,cAAa,kBAAiB,aAAY,YAAW,aAAY,eAAc,aAAY,WAAU,YAAW,SAAQ,qBAAoB,cAAa,aAAY,YAAW,eAAc,eAAc,eAAc,aAAY,gBAAe,iBAAgB,gBAAe,iBAAgB,cAAa,SAAQ,QAAO,UAAS,SAAQ,UAAS,UAAS,WAAU,YAAW,QAAO,UAAS,gBAAe,UAAS,QAAO,oBAAmB,oBAAmB,SAAQ,SAAQ,QAAQ,CAAC;AAAE,WAAS,EAAEN,IAAED,IAAE;AAAC,WAAOA,MAAG,GAAGA,EAAC,KAAK,GAAG,MAAIC,GAAE,YAAU;AAAA,EAAG;AAAC,WAAS,EAAEA,IAAE;AAAC,UAAMD,KAAE,EAAEC,EAAC;AAAE,WAAOA,GAAE,WAASD,IAAE,EAAEA,EAAC,IAAE,EAAEA,EAAC,KAAG,CAAC,GAAE,EAAEA,EAAC;AAAA,EAAC;AAAC,WAAS,EAAEC,IAAED,IAAEI,KAAE,MAAK;AAAC,WAAO,OAAO,OAAOH,EAAC,EAAE,KAAM,CAAAA,OAAGA,GAAE,aAAWD,MAAGC,GAAE,uBAAqBG,EAAE;AAAA,EAAC;AAAC,WAAS,EAAEH,IAAED,IAAEI,IAAE;AAAC,UAAMF,KAAE,YAAU,OAAOF,IAAEO,KAAEL,KAAEE,KAAEJ,MAAGI;AAAE,QAAIC,KAAE,EAAEJ,EAAC;AAAE,WAAO,EAAE,IAAII,EAAC,MAAIA,KAAEJ,KAAG,CAACC,IAAEK,IAAEF,EAAC;AAAA,EAAC;AAAC,WAAS,EAAEJ,IAAED,IAAEI,IAAEF,IAAEK,IAAE;AAAC,QAAG,YAAU,OAAOP,MAAG,CAACC,GAAE;AAAO,QAAG,CAACI,IAAEF,IAAEG,EAAC,IAAE,EAAEN,IAAEI,IAAEF,EAAC;AAAE,QAAGF,MAAK,GAAE;AAAC,YAAMC,KAAE,CAAAA,OAAG,SAASD,IAAE;AAAC,YAAG,CAACA,GAAE,iBAAeA,GAAE,kBAAgBA,GAAE,kBAAgB,CAACA,GAAE,eAAe,SAASA,GAAE,aAAa,EAAE,QAAOC,GAAE,KAAK,MAAKD,EAAC;AAAA,MAAC;AAAE,MAAAG,KAAEF,GAAEE,EAAC;AAAA,IAAC;AAAC,UAAMM,KAAE,EAAER,EAAC,GAAEkB,KAAEV,GAAEH,EAAC,MAAIG,GAAEH,EAAC,IAAE,CAAC,IAAGc,KAAE,EAAED,IAAEhB,IAAEE,KAAED,KAAE,IAAI;AAAE,QAAGgB,GAAE,QAAO,MAAKA,GAAE,SAAOA,GAAE,UAAQb;AAAG,UAAMO,KAAE,EAAEX,IAAEH,GAAE,QAAQ,GAAE,EAAE,CAAC,GAAEQ,KAAEH,KAAE,yBAASJ,IAAED,IAAEI,IAAE;AAAC,aAAO,SAASF,GAAEK,IAAE;AAAC,cAAMF,KAAEJ,GAAE,iBAAiBD,EAAC;AAAE,iBAAO,EAAC,QAAOG,GAAC,IAAEI,IAAEJ,MAAGA,OAAI,MAAKA,KAAEA,GAAE,WAAW,YAAUG,MAAKD,GAAE,KAAGC,OAAIH,GAAE,QAAO,EAAEI,IAAE,EAAC,gBAAeJ,GAAC,CAAC,GAAED,GAAE,UAAQ,EAAE,IAAID,IAAEM,GAAE,MAAKP,IAAEI,EAAC,GAAEA,GAAE,MAAMD,IAAE,CAACI,EAAC,CAAC;AAAA,MAAC;AAAA,IAAC,EAAEN,IAAEG,IAAED,EAAC,IAAE,yBAASF,IAAED,IAAE;AAAC,aAAO,SAASI,GAAEF,IAAE;AAAC,eAAO,EAAEA,IAAE,EAAC,gBAAeD,GAAC,CAAC,GAAEG,GAAE,UAAQ,EAAE,IAAIH,IAAEC,GAAE,MAAKF,EAAC,GAAEA,GAAE,MAAMC,IAAE,CAACC,EAAC,CAAC;AAAA,MAAC;AAAA,IAAC,EAAED,IAAEE,EAAC;AAAE,IAAAK,GAAE,qBAAmBH,KAAED,KAAE,MAAKI,GAAE,WAASL,IAAEK,GAAE,SAAOD,IAAEC,GAAE,WAASM,IAAEK,GAAEL,EAAC,IAAEN,IAAEP,GAAE,iBAAiBK,IAAEE,IAAEH,EAAC;AAAA,EAAC;AAAC,WAAS,EAAEJ,IAAED,IAAEI,IAAEF,IAAEK,IAAE;AAAC,UAAMF,KAAE,EAAEL,GAAEI,EAAC,GAAEF,IAAEK,EAAC;AAAE,IAAAF,OAAIJ,GAAE,oBAAoBG,IAAEC,IAAE,QAAQE,EAAC,CAAC,GAAE,OAAOP,GAAEI,EAAC,EAAEC,GAAE,QAAQ;AAAA,EAAE;AAAC,WAAS,EAAEJ,IAAED,IAAEI,IAAEF,IAAE;AAAC,UAAMK,KAAEP,GAAEI,EAAC,KAAG,CAAC;AAAE,eAAS,CAACC,IAAEF,EAAC,KAAI,OAAO,QAAQI,EAAC,EAAE,CAAAF,GAAE,SAASH,EAAC,KAAG,EAAED,IAAED,IAAEI,IAAED,GAAE,UAASA,GAAE,kBAAkB;AAAA,EAAC;AAAC,WAAS,EAAEF,IAAE;AAAC,WAAOA,KAAEA,GAAE,QAAQ,GAAE,EAAE,GAAE,EAAEA,EAAC,KAAGA;AAAA,EAAC;AAAC,QAAM,IAAE,EAAC,GAAGA,IAAED,IAAEI,IAAEF,IAAE;AAAC,MAAED,IAAED,IAAEI,IAAEF,IAAE,KAAE;AAAA,EAAC,GAAE,IAAID,IAAED,IAAEI,IAAEF,IAAE;AAAC,MAAED,IAAED,IAAEI,IAAEF,IAAE,IAAE;AAAA,EAAC,GAAE,IAAID,IAAED,IAAEI,IAAEF,IAAE;AAAC,QAAG,YAAU,OAAOF,MAAG,CAACC,GAAE;AAAO,UAAK,CAACM,IAAEF,IAAEF,EAAC,IAAE,EAAEH,IAAEI,IAAEF,EAAC,GAAEI,KAAEH,OAAIH,IAAES,KAAE,EAAER,EAAC,GAAEkB,KAAEV,GAAEN,EAAC,KAAG,CAAC,GAAEiB,KAAEpB,GAAE,WAAW,GAAG;AAAE,QAAG,WAASK,IAAE;AAAC,UAAGe,GAAE,YAAUhB,MAAK,OAAO,KAAKK,EAAC,EAAE,GAAER,IAAEQ,IAAEL,IAAEJ,GAAE,MAAM,CAAC,CAAC;AAAE,iBAAS,CAACI,IAAEF,EAAC,KAAI,OAAO,QAAQiB,EAAC,GAAE;AAAC,cAAMZ,KAAEH,GAAE,QAAQ,GAAE,EAAE;AAAE,QAAAE,MAAG,CAACN,GAAE,SAASO,EAAC,KAAG,EAAEN,IAAEQ,IAAEN,IAAED,GAAE,UAASA,GAAE,kBAAkB;AAAA,MAAC;AAAA,IAAC,OAAK;AAAC,UAAG,CAAC,OAAO,KAAKiB,EAAC,EAAE,OAAO;AAAO,QAAElB,IAAEQ,IAAEN,IAAEE,IAAEE,KAAEH,KAAE,IAAI;AAAA,IAAC;AAAA,EAAC,GAAE,QAAQH,IAAED,IAAEI,IAAE;AAAC,QAAG,YAAU,OAAOJ,MAAG,CAACC,GAAE,QAAO;AAAK,UAAMC,KAAE,EAAE;AAAE,QAAIK,KAAE,MAAKF,KAAE,MAAGF,KAAE,MAAGG,KAAE;AAAG,IAAAN,OAAI,EAAEA,EAAC,KAAGE,OAAIK,KAAEL,GAAE,MAAMF,IAAEI,EAAC,GAAEF,GAAED,EAAC,EAAE,QAAQM,EAAC,GAAEF,KAAE,CAACE,GAAE,qBAAqB,GAAEJ,KAAE,CAACI,GAAE,8BAA8B,GAAED,KAAEC,GAAE,mBAAmB;AAAG,UAAME,KAAE,EAAE,IAAI,MAAMT,IAAE,EAAC,SAAQK,IAAE,YAAW,KAAE,CAAC,GAAED,EAAC;AAAE,WAAOE,MAAGG,GAAE,eAAe,GAAEN,MAAGF,GAAE,cAAcQ,EAAC,GAAEA,GAAE,oBAAkBF,MAAGA,GAAE,eAAe,GAAEE;AAAA,EAAC,EAAC;AAAE,WAAS,EAAER,IAAED,KAAE,CAAC,GAAE;AAAC,eAAS,CAACI,IAAEF,EAAC,KAAI,OAAO,QAAQF,EAAC,EAAE,KAAG;AAAC,MAAAC,GAAEG,EAAC,IAAEF;AAAA,IAAC,SAAOF,IAAE;AAAC,aAAO,eAAeC,IAAEG,IAAE,EAAC,cAAa,MAAG,KAAI,MAAIF,GAAC,CAAC;AAAA,IAAC;AAAC,WAAOD;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,QAAG,WAASA,GAAE,QAAM;AAAG,QAAG,YAAUA,GAAE,QAAM;AAAG,QAAGA,OAAI,OAAOA,EAAC,EAAE,SAAS,EAAE,QAAO,OAAOA,EAAC;AAAE,QAAG,OAAKA,MAAG,WAASA,GAAE,QAAO;AAAK,QAAG,YAAU,OAAOA,GAAE,QAAOA;AAAE,QAAG;AAAC,aAAO,KAAK,MAAM,mBAAmBA,EAAC,CAAC;AAAA,IAAC,SAAOD,IAAE;AAAC,aAAOC;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,WAAOA,GAAE,QAAQ,UAAU,CAAAA,OAAG,IAAIA,GAAE,YAAY,CAAC,EAAG;AAAA,EAAC;AAAC,QAAM,IAAE,EAAC,iBAAiBA,IAAED,IAAEI,IAAE;AAAC,IAAAH,GAAE,aAAa,WAAW,EAAED,EAAC,CAAC,IAAGI,EAAC;AAAA,EAAC,GAAE,oBAAoBH,IAAED,IAAE;AAAC,IAAAC,GAAE,gBAAgB,WAAW,EAAED,EAAC,CAAC,EAAE;AAAA,EAAC,GAAE,kBAAkBC,IAAE;AAAC,QAAG,CAACA,GAAE,QAAM,CAAC;AAAE,UAAMD,KAAE,CAAC,GAAEI,KAAE,OAAO,KAAKH,GAAE,OAAO,EAAE,OAAQ,CAAAA,OAAGA,GAAE,WAAW,IAAI,KAAG,CAACA,GAAE,WAAW,UAAU,CAAE;AAAE,eAAUC,MAAKE,IAAE;AAAC,UAAIA,KAAEF,GAAE,QAAQ,OAAM,EAAE;AAAE,MAAAE,KAAEA,GAAE,OAAO,CAAC,EAAE,YAAY,IAAEA,GAAE,MAAM,CAAC,GAAEJ,GAAEI,EAAC,IAAE,EAAEH,GAAE,QAAQC,EAAC,CAAC;AAAA,IAAC;AAAC,WAAOF;AAAA,EAAC,GAAE,kBAAiB,CAACC,IAAED,OAAI,EAAEC,GAAE,aAAa,WAAW,EAAED,EAAC,CAAC,EAAE,CAAC,EAAC;AAAA,EAAE,MAAM,EAAC;AAAA,IAAC,WAAW,UAAS;AAAC,aAAM,CAAC;AAAA,IAAC;AAAA,IAAC,WAAW,cAAa;AAAC,aAAM,CAAC;AAAA,IAAC;AAAA,IAAC,WAAW,OAAM;AAAC,YAAM,IAAI,MAAM,qEAAqE;AAAA,IAAC;AAAA,IAAC,WAAWC,IAAE;AAAC,aAAOA,KAAE,KAAK,gBAAgBA,EAAC,GAAEA,KAAE,KAAK,kBAAkBA,EAAC,GAAE,KAAK,iBAAiBA,EAAC,GAAEA;AAAA,IAAC;AAAA,IAAC,kBAAkBA,IAAE;AAAC,aAAOA;AAAA,IAAC;AAAA,IAAC,gBAAgBA,IAAED,IAAE;AAAC,YAAMI,KAAE,EAAEJ,EAAC,IAAE,EAAE,iBAAiBA,IAAE,QAAQ,IAAE,CAAC;AAAE,aAAM,EAAC,GAAG,KAAK,YAAY,SAAQ,GAAG,YAAU,OAAOI,KAAEA,KAAE,CAAC,GAAE,GAAG,EAAEJ,EAAC,IAAE,EAAE,kBAAkBA,EAAC,IAAE,CAAC,GAAE,GAAG,YAAU,OAAOC,KAAEA,KAAE,CAAC,EAAC;AAAA,IAAC;AAAA,IAAC,iBAAiBA,IAAED,KAAE,KAAK,YAAY,aAAY;AAAC,iBAAS,CAACE,IAAEK,EAAC,KAAI,OAAO,QAAQP,EAAC,GAAE;AAAC,cAAMA,KAAEC,GAAEC,EAAC,GAAEC,KAAE,EAAEH,EAAC,IAAE,YAAU,SAAOI,KAAEJ,MAAG,GAAGI,EAAC,KAAG,OAAO,UAAU,SAAS,KAAKA,EAAC,EAAE,MAAM,aAAa,EAAE,CAAC,EAAE,YAAY;AAAE,YAAG,CAAC,IAAI,OAAOG,EAAC,EAAE,KAAKJ,EAAC,EAAE,OAAM,IAAI,UAAU,GAAG,KAAK,YAAY,KAAK,YAAY,CAAC,aAAaD,EAAC,oBAAoBC,EAAC,wBAAwBI,EAAC,IAAI;AAAA,MAAC;AAAC,UAAIH;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,UAAU,EAAC;AAAA,IAAC,YAAYH,IAAEG,IAAE;AAAC,YAAM,IAAGH,KAAE,EAAEA,EAAC,OAAK,KAAK,WAASA,IAAE,KAAK,UAAQ,KAAK,WAAWG,EAAC,GAAE,EAAE,IAAI,KAAK,UAAS,KAAK,YAAY,UAAS,IAAI;AAAA,IAAE;AAAA,IAAC,UAAS;AAAC,QAAE,OAAO,KAAK,UAAS,KAAK,YAAY,QAAQ,GAAE,EAAE,IAAI,KAAK,UAAS,KAAK,YAAY,SAAS;AAAE,iBAAUH,MAAK,OAAO,oBAAoB,IAAI,EAAE,MAAKA,EAAC,IAAE;AAAA,IAAI;AAAA,IAAC,eAAeA,IAAED,IAAEI,KAAE,MAAG;AAAC,QAAEH,IAAED,IAAEI,EAAC;AAAA,IAAC;AAAA,IAAC,WAAWH,IAAE;AAAC,aAAOA,KAAE,KAAK,gBAAgBA,IAAE,KAAK,QAAQ,GAAEA,KAAE,KAAK,kBAAkBA,EAAC,GAAE,KAAK,iBAAiBA,EAAC,GAAEA;AAAA,IAAC;AAAA,IAAC,OAAO,YAAYA,IAAE;AAAC,aAAO,EAAE,IAAI,EAAEA,EAAC,GAAE,KAAK,QAAQ;AAAA,IAAC;AAAA,IAAC,OAAO,oBAAoBA,IAAED,KAAE,CAAC,GAAE;AAAC,aAAO,KAAK,YAAYC,EAAC,KAAG,IAAI,KAAKA,IAAE,YAAU,OAAOD,KAAEA,KAAE,IAAI;AAAA,IAAC;AAAA,IAAC,WAAW,UAAS;AAAC,aAAM;AAAA,IAAO;AAAA,IAAC,WAAW,WAAU;AAAC,aAAM,MAAM,KAAK,IAAI;AAAA,IAAE;AAAA,IAAC,WAAW,YAAW;AAAC,aAAM,IAAI,KAAK,QAAQ;AAAA,IAAE;AAAA,IAAC,OAAO,UAAUC,IAAE;AAAC,aAAM,GAAGA,EAAC,GAAG,KAAK,SAAS;AAAA,IAAE;AAAA,EAAC;AAAC,QAAM,IAAE,CAAAA,OAAG;AAAC,QAAID,KAAEC,GAAE,aAAa,gBAAgB;AAAE,QAAG,CAACD,MAAG,QAAMA,IAAE;AAAC,UAAII,KAAEH,GAAE,aAAa,MAAM;AAAE,UAAG,CAACG,MAAG,CAACA,GAAE,SAAS,GAAG,KAAG,CAACA,GAAE,WAAW,GAAG,EAAE,QAAO;AAAK,MAAAA,GAAE,SAAS,GAAG,KAAG,CAACA,GAAE,WAAW,GAAG,MAAIA,KAAE,IAAIA,GAAE,MAAM,GAAG,EAAE,CAAC,CAAC,KAAIJ,KAAEI,MAAG,QAAMA,KAAEA,GAAE,KAAK,IAAE;AAAA,IAAI;AAAC,WAAOJ,KAAEA,GAAE,MAAM,GAAG,EAAE,IAAK,CAAAC,OAAG,EAAEA,EAAC,CAAE,EAAE,KAAK,GAAG,IAAE;AAAA,EAAI,GAAE,IAAE,EAAC,MAAK,CAACA,IAAED,KAAE,SAAS,oBAAkB,CAAC,EAAE,OAAO,GAAG,QAAQ,UAAU,iBAAiB,KAAKA,IAAEC,EAAC,CAAC,GAAE,SAAQ,CAACA,IAAED,KAAE,SAAS,oBAAkB,QAAQ,UAAU,cAAc,KAAKA,IAAEC,EAAC,GAAE,UAAS,CAACA,IAAED,OAAI,CAAC,EAAE,OAAO,GAAGC,GAAE,QAAQ,EAAE,OAAQ,CAAAA,OAAGA,GAAE,QAAQD,EAAC,CAAE,GAAE,QAAQC,IAAED,IAAE;AAAC,UAAMI,KAAE,CAAC;AAAE,QAAIF,KAAED,GAAE,WAAW,QAAQD,EAAC;AAAE,WAAKE,KAAG,CAAAE,GAAE,KAAKF,EAAC,GAAEA,KAAEA,GAAE,WAAW,QAAQF,EAAC;AAAE,WAAOI;AAAA,EAAC,GAAE,KAAKH,IAAED,IAAE;AAAC,QAAII,KAAEH,GAAE;AAAuB,WAAKG,MAAG;AAAC,UAAGA,GAAE,QAAQJ,EAAC,EAAE,QAAM,CAACI,EAAC;AAAE,MAAAA,KAAEA,GAAE;AAAA,IAAsB;AAAC,WAAM,CAAC;AAAA,EAAC,GAAE,KAAKH,IAAED,IAAE;AAAC,QAAII,KAAEH,GAAE;AAAmB,WAAKG,MAAG;AAAC,UAAGA,GAAE,QAAQJ,EAAC,EAAE,QAAM,CAACI,EAAC;AAAE,MAAAA,KAAEA,GAAE;AAAA,IAAkB;AAAC,WAAM,CAAC;AAAA,EAAC,GAAE,kBAAkBH,IAAE;AAAC,UAAMD,KAAE,CAAC,KAAI,UAAS,SAAQ,YAAW,UAAS,WAAU,cAAa,0BAA0B,EAAE,IAAK,CAAAC,OAAG,GAAGA,EAAC,uBAAwB,EAAE,KAAK,GAAG;AAAE,WAAO,KAAK,KAAKD,IAAEC,EAAC,EAAE,OAAQ,CAAAA,OAAG,CAAC,EAAEA,EAAC,KAAG,EAAEA,EAAC,CAAE;AAAA,EAAC,GAAE,uBAAuBA,IAAE;AAAC,UAAMD,KAAE,EAAEC,EAAC;AAAE,WAAOD,MAAG,EAAE,QAAQA,EAAC,IAAEA,KAAE;AAAA,EAAI,GAAE,uBAAuBC,IAAE;AAAC,UAAMD,KAAE,EAAEC,EAAC;AAAE,WAAOD,KAAE,EAAE,QAAQA,EAAC,IAAE;AAAA,EAAI,GAAE,gCAAgCC,IAAE;AAAC,UAAMD,KAAE,EAAEC,EAAC;AAAE,WAAOD,KAAE,EAAE,KAAKA,EAAC,IAAE,CAAC;AAAA,EAAC,EAAC,GAAE,IAAE,CAACC,IAAED,KAAE,WAAS;AAAC,UAAMI,KAAE,gBAAgBH,GAAE,SAAS,IAAGC,KAAED,GAAE;AAAK,MAAE,GAAG,UAASG,IAAE,qBAAqBF,EAAC,MAAM,SAASE,IAAE;AAAC,UAAG,CAAC,KAAI,MAAM,EAAE,SAAS,KAAK,OAAO,KAAGA,GAAE,eAAe,GAAE,EAAE,IAAI,EAAE;AAAO,YAAMG,KAAE,EAAE,uBAAuB,IAAI,KAAG,KAAK,QAAQ,IAAIL,EAAC,EAAE;AAAE,MAAAD,GAAE,oBAAoBM,EAAC,EAAEP,EAAC,EAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,IAAE,aAAY,IAAE,QAAQ,CAAC,IAAG,IAAE,SAAS,CAAC;AAAA,EAAG,MAAM,UAAU,EAAC;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAO;AAAA,IAAC,QAAO;AAAC,UAAG,EAAE,QAAQ,KAAK,UAAS,CAAC,EAAE,iBAAiB;AAAO,WAAK,SAAS,UAAU,OAAO,MAAM;AAAE,YAAMC,KAAE,KAAK,SAAS,UAAU,SAAS,MAAM;AAAE,WAAK,eAAgB,MAAI,KAAK,gBAAgB,GAAG,KAAK,UAASA,EAAC;AAAA,IAAC;AAAA,IAAC,kBAAiB;AAAC,WAAK,SAAS,OAAO,GAAE,EAAE,QAAQ,KAAK,UAAS,CAAC,GAAE,KAAK,QAAQ;AAAA,IAAC;AAAA,IAAC,OAAO,gBAAgBA,IAAE;AAAC,aAAO,KAAK,KAAM,WAAU;AAAC,cAAMD,KAAE,EAAE,oBAAoB,IAAI;AAAE,YAAG,YAAU,OAAOC,IAAE;AAAC,cAAG,WAASD,GAAEC,EAAC,KAAGA,GAAE,WAAW,GAAG,KAAG,kBAAgBA,GAAE,OAAM,IAAI,UAAU,oBAAoBA,EAAC,GAAG;AAAE,UAAAD,GAAEC,EAAC,EAAE,IAAI;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAC,IAAE,GAAE,OAAO,GAAE,EAAE,CAAC;AAAE,QAAM,IAAE;AAAA,EAA4B,MAAM,UAAU,EAAC;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAQ;AAAA,IAAC,SAAQ;AAAC,WAAK,SAAS,aAAa,gBAAe,KAAK,SAAS,UAAU,OAAO,QAAQ,CAAC;AAAA,IAAC;AAAA,IAAC,OAAO,gBAAgBA,IAAE;AAAC,aAAO,KAAK,KAAM,WAAU;AAAC,cAAMD,KAAE,EAAE,oBAAoB,IAAI;AAAE,qBAAWC,MAAGD,GAAEC,EAAC,EAAE;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAC,IAAE,GAAG,UAAS,4BAA2B,GAAG,CAAAA,OAAG;AAAC,IAAAA,GAAE,eAAe;AAAE,UAAMD,KAAEC,GAAE,OAAO,QAAQ,CAAC;AAAE,MAAE,oBAAoBD,EAAC,EAAE,OAAO;AAAA,EAAC,CAAE,GAAE,EAAE,CAAC;AAAE,QAAM,IAAE,aAAY,IAAE,aAAa,CAAC,IAAG,IAAE,YAAY,CAAC,IAAG,IAAE,WAAW,CAAC,IAAG,KAAG,cAAc,CAAC,IAAG,KAAG,YAAY,CAAC,IAAG,KAAG,EAAC,aAAY,MAAK,cAAa,MAAK,eAAc,KAAI,GAAE,KAAG,EAAC,aAAY,mBAAkB,cAAa,mBAAkB,eAAc,kBAAiB;AAAA,EAAE,MAAM,WAAW,EAAC;AAAA,IAAC,YAAYC,IAAED,IAAE;AAAC,YAAM,GAAE,KAAK,WAASC,IAAEA,MAAG,GAAG,YAAY,MAAI,KAAK,UAAQ,KAAK,WAAWD,EAAC,GAAE,KAAK,UAAQ,GAAE,KAAK,wBAAsB,QAAQ,OAAO,YAAY,GAAE,KAAK,YAAY;AAAA,IAAE;AAAA,IAAC,WAAW,UAAS;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,cAAa;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAO;AAAA,IAAC,UAAS;AAAC,QAAE,IAAI,KAAK,UAAS,CAAC;AAAA,IAAC;AAAA,IAAC,OAAOC,IAAE;AAAC,WAAK,wBAAsB,KAAK,wBAAwBA,EAAC,MAAI,KAAK,UAAQA,GAAE,WAAS,KAAK,UAAQA,GAAE,QAAQ,CAAC,EAAE;AAAA,IAAO;AAAA,IAAC,KAAKA,IAAE;AAAC,WAAK,wBAAwBA,EAAC,MAAI,KAAK,UAAQA,GAAE,UAAQ,KAAK,UAAS,KAAK,aAAa,GAAE,EAAE,KAAK,QAAQ,WAAW;AAAA,IAAC;AAAA,IAAC,MAAMA,IAAE;AAAC,WAAK,UAAQA,GAAE,WAASA,GAAE,QAAQ,SAAO,IAAE,IAAEA,GAAE,QAAQ,CAAC,EAAE,UAAQ,KAAK;AAAA,IAAO;AAAA,IAAC,eAAc;AAAC,YAAMA,KAAE,KAAK,IAAI,KAAK,OAAO;AAAE,UAAGA,MAAG,GAAG;AAAO,YAAMD,KAAEC,KAAE,KAAK;AAAQ,WAAK,UAAQ,GAAED,MAAG,EAAEA,KAAE,IAAE,KAAK,QAAQ,gBAAc,KAAK,QAAQ,YAAY;AAAA,IAAC;AAAA,IAAC,cAAa;AAAC,WAAK,yBAAuB,EAAE,GAAG,KAAK,UAAS,IAAI,CAAAC,OAAG,KAAK,OAAOA,EAAC,CAAE,GAAE,EAAE,GAAG,KAAK,UAAS,IAAI,CAAAA,OAAG,KAAK,KAAKA,EAAC,CAAE,GAAE,KAAK,SAAS,UAAU,IAAI,eAAe,MAAI,EAAE,GAAG,KAAK,UAAS,GAAG,CAAAA,OAAG,KAAK,OAAOA,EAAC,CAAE,GAAE,EAAE,GAAG,KAAK,UAAS,GAAG,CAAAA,OAAG,KAAK,MAAMA,EAAC,CAAE,GAAE,EAAE,GAAG,KAAK,UAAS,GAAG,CAAAA,OAAG,KAAK,KAAKA,EAAC,CAAE;AAAA,IAAE;AAAA,IAAC,wBAAwBA,IAAE;AAAC,aAAO,KAAK,0BAAwB,UAAQA,GAAE,eAAa,YAAUA,GAAE;AAAA,IAAY;AAAA,IAAC,OAAO,cAAa;AAAC,aAAM,kBAAiB,SAAS,mBAAiB,UAAU,iBAAe;AAAA,IAAC;AAAA,EAAC;AAAC,QAAM,KAAG,gBAAe,KAAG,aAAY,KAAG,aAAY,KAAG,cAAa,KAAG,QAAO,KAAG,QAAO,KAAG,QAAO,KAAG,SAAQ,KAAG,QAAQ,EAAE,IAAG,KAAG,OAAO,EAAE,IAAG,KAAG,UAAU,EAAE,IAAG,KAAG,aAAa,EAAE,IAAG,KAAG,aAAa,EAAE,IAAG,KAAG,YAAY,EAAE,IAAG,KAAG,OAAO,EAAE,GAAG,EAAE,IAAG,KAAG,QAAQ,EAAE,GAAG,EAAE,IAAG,KAAG,YAAW,KAAG,UAAS,KAAG,WAAU,KAAG,kBAAiB,KAAG,KAAG,IAAG,KAAG,EAAC,CAAC,EAAE,GAAE,IAAG,CAAC,EAAE,GAAE,GAAE,GAAE,KAAG,EAAC,UAAS,KAAI,UAAS,MAAG,OAAM,SAAQ,MAAK,OAAG,OAAM,MAAG,MAAK,KAAE,GAAE,KAAG,EAAC,UAAS,oBAAmB,UAAS,WAAU,OAAM,oBAAmB,MAAK,oBAAmB,OAAM,WAAU,MAAK,UAAS;AAAA,EAAE,MAAM,WAAW,EAAC;AAAA,IAAC,YAAYA,IAAED,IAAE;AAAC,YAAMC,IAAED,EAAC,GAAE,KAAK,YAAU,MAAK,KAAK,iBAAe,MAAK,KAAK,aAAW,OAAG,KAAK,eAAa,MAAK,KAAK,eAAa,MAAK,KAAK,qBAAmB,EAAE,QAAQ,wBAAuB,KAAK,QAAQ,GAAE,KAAK,mBAAmB,GAAE,KAAK,QAAQ,SAAO,MAAI,KAAK,MAAM;AAAA,IAAC;AAAA,IAAC,WAAW,UAAS;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,cAAa;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAU;AAAA,IAAC,OAAM;AAAC,WAAK,OAAO,EAAE;AAAA,IAAC;AAAA,IAAC,kBAAiB;AAAC,OAAC,SAAS,UAAQ,EAAE,KAAK,QAAQ,KAAG,KAAK,KAAK;AAAA,IAAC;AAAA,IAAC,OAAM;AAAC,WAAK,OAAO,EAAE;AAAA,IAAC;AAAA,IAAC,QAAO;AAAC,WAAK,cAAY,EAAE,KAAK,QAAQ,GAAE,KAAK,eAAe;AAAA,IAAC;AAAA,IAAC,QAAO;AAAC,WAAK,eAAe,GAAE,KAAK,gBAAgB,GAAE,KAAK,YAAU,YAAa,MAAI,KAAK,gBAAgB,GAAG,KAAK,QAAQ,QAAQ;AAAA,IAAC;AAAA,IAAC,oBAAmB;AAAC,WAAK,QAAQ,SAAO,KAAK,aAAW,EAAE,IAAI,KAAK,UAAS,IAAI,MAAI,KAAK,MAAM,CAAE,IAAE,KAAK,MAAM;AAAA,IAAE;AAAA,IAAC,GAAGC,IAAE;AAAC,YAAMD,KAAE,KAAK,UAAU;AAAE,UAAGC,KAAED,GAAE,SAAO,KAAGC,KAAE,EAAE;AAAO,UAAG,KAAK,WAAW,QAAO,KAAK,EAAE,IAAI,KAAK,UAAS,IAAI,MAAI,KAAK,GAAGA,EAAC,CAAE;AAAE,YAAMG,KAAE,KAAK,cAAc,KAAK,WAAW,CAAC;AAAE,UAAGA,OAAIH,GAAE;AAAO,YAAMC,KAAED,KAAEG,KAAE,KAAG;AAAG,WAAK,OAAOF,IAAEF,GAAEC,EAAC,CAAC;AAAA,IAAC;AAAA,IAAC,UAAS;AAAC,WAAK,gBAAc,KAAK,aAAa,QAAQ,GAAE,MAAM,QAAQ;AAAA,IAAC;AAAA,IAAC,kBAAkBA,IAAE;AAAC,aAAOA,GAAE,kBAAgBA,GAAE,UAASA;AAAA,IAAC;AAAA,IAAC,qBAAoB;AAAC,WAAK,QAAQ,YAAU,EAAE,GAAG,KAAK,UAAS,IAAI,CAAAA,OAAG,KAAK,SAASA,EAAC,CAAE,GAAE,YAAU,KAAK,QAAQ,UAAQ,EAAE,GAAG,KAAK,UAAS,IAAI,MAAI,KAAK,MAAM,CAAE,GAAE,EAAE,GAAG,KAAK,UAAS,IAAI,MAAI,KAAK,kBAAkB,CAAE,IAAG,KAAK,QAAQ,SAAO,GAAG,YAAY,KAAG,KAAK,wBAAwB;AAAA,IAAC;AAAA,IAAC,0BAAyB;AAAC,iBAAUA,MAAK,EAAE,KAAK,sBAAqB,KAAK,QAAQ,EAAE,GAAE,GAAGA,IAAE,IAAI,CAAAA,OAAGA,GAAE,eAAe,CAAE;AAAE,YAAMA,KAAE,EAAC,cAAa,MAAI,KAAK,OAAO,KAAK,kBAAkB,EAAE,CAAC,GAAE,eAAc,MAAI,KAAK,OAAO,KAAK,kBAAkB,EAAE,CAAC,GAAE,aAAY,MAAI;AAAC,oBAAU,KAAK,QAAQ,UAAQ,KAAK,MAAM,GAAE,KAAK,gBAAc,aAAa,KAAK,YAAY,GAAE,KAAK,eAAa,WAAY,MAAI,KAAK,kBAAkB,GAAG,MAAI,KAAK,QAAQ,QAAQ;AAAA,MAAE,EAAC;AAAE,WAAK,eAAa,IAAI,GAAG,KAAK,UAASA,EAAC;AAAA,IAAC;AAAA,IAAC,SAASA,IAAE;AAAC,UAAG,kBAAkB,KAAKA,GAAE,OAAO,OAAO,EAAE;AAAO,YAAMD,KAAE,GAAGC,GAAE,GAAG;AAAE,MAAAD,OAAIC,GAAE,eAAe,GAAE,KAAK,OAAO,KAAK,kBAAkBD,EAAC,CAAC;AAAA,IAAE;AAAA,IAAC,cAAcC,IAAE;AAAC,aAAO,KAAK,UAAU,EAAE,QAAQA,EAAC;AAAA,IAAC;AAAA,IAAC,2BAA2BA,IAAE;AAAC,UAAG,CAAC,KAAK,mBAAmB;AAAO,YAAMD,KAAE,EAAE,QAAQ,IAAG,KAAK,kBAAkB;AAAE,MAAAA,GAAE,UAAU,OAAO,EAAE,GAAEA,GAAE,gBAAgB,cAAc;AAAE,YAAMI,KAAE,EAAE,QAAQ,sBAAsBH,EAAC,MAAK,KAAK,kBAAkB;AAAE,MAAAG,OAAIA,GAAE,UAAU,IAAI,EAAE,GAAEA,GAAE,aAAa,gBAAe,MAAM;AAAA,IAAE;AAAA,IAAC,kBAAiB;AAAC,YAAMH,KAAE,KAAK,kBAAgB,KAAK,WAAW;AAAE,UAAG,CAACA,GAAE;AAAO,YAAMD,KAAE,OAAO,SAASC,GAAE,aAAa,kBAAkB,GAAE,EAAE;AAAE,WAAK,QAAQ,WAASD,MAAG,KAAK,QAAQ;AAAA,IAAe;AAAA,IAAC,OAAOC,IAAED,KAAE,MAAK;AAAC,UAAG,KAAK,WAAW;AAAO,YAAMI,KAAE,KAAK,WAAW,GAAEF,KAAED,OAAI,IAAGM,KAAEP,MAAG,EAAE,KAAK,UAAU,GAAEI,IAAEF,IAAE,KAAK,QAAQ,IAAI;AAAE,UAAGK,OAAIH,GAAE;AAAO,YAAMC,KAAE,KAAK,cAAcE,EAAC,GAAEJ,KAAE,CAAAH,OAAG,EAAE,QAAQ,KAAK,UAASA,IAAE,EAAC,eAAcO,IAAE,WAAU,KAAK,kBAAkBN,EAAC,GAAE,MAAK,KAAK,cAAcG,EAAC,GAAE,IAAGC,GAAC,CAAC;AAAE,UAAGF,GAAE,EAAE,EAAE,iBAAiB;AAAO,UAAG,CAACC,MAAG,CAACG,GAAE;AAAO,YAAMD,KAAE,QAAQ,KAAK,SAAS;AAAE,WAAK,MAAM,GAAE,KAAK,aAAW,MAAG,KAAK,2BAA2BD,EAAC,GAAE,KAAK,iBAAeE;AAAE,YAAME,KAAEP,KAAE,wBAAsB,qBAAoBiB,KAAEjB,KAAE,uBAAqB;AAAqB,MAAAK,GAAE,UAAU,IAAIY,EAAC,GAAE,EAAEZ,EAAC,GAAEH,GAAE,UAAU,IAAIK,EAAC,GAAEF,GAAE,UAAU,IAAIE,EAAC,GAAE,KAAK,eAAgB,MAAI;AAAC,QAAAF,GAAE,UAAU,OAAOE,IAAEU,EAAC,GAAEZ,GAAE,UAAU,IAAI,EAAE,GAAEH,GAAE,UAAU,OAAO,IAAGe,IAAEV,EAAC,GAAE,KAAK,aAAW,OAAGN,GAAE,EAAE;AAAA,MAAC,GAAGC,IAAE,KAAK,YAAY,CAAC,GAAEE,MAAG,KAAK,MAAM;AAAA,IAAC;AAAA,IAAC,cAAa;AAAC,aAAO,KAAK,SAAS,UAAU,SAAS,OAAO;AAAA,IAAC;AAAA,IAAC,aAAY;AAAC,aAAO,EAAE,QAAQ,IAAG,KAAK,QAAQ;AAAA,IAAC;AAAA,IAAC,YAAW;AAAC,aAAO,EAAE,KAAK,IAAG,KAAK,QAAQ;AAAA,IAAC;AAAA,IAAC,iBAAgB;AAAC,WAAK,cAAY,cAAc,KAAK,SAAS,GAAE,KAAK,YAAU;AAAA,IAAK;AAAA,IAAC,kBAAkBL,IAAE;AAAC,aAAO,EAAE,IAAEA,OAAI,KAAG,KAAG,KAAGA,OAAI,KAAG,KAAG;AAAA,IAAE;AAAA,IAAC,kBAAkBA,IAAE;AAAC,aAAO,EAAE,IAAEA,OAAI,KAAG,KAAG,KAAGA,OAAI,KAAG,KAAG;AAAA,IAAE;AAAA,IAAC,OAAO,gBAAgBA,IAAE;AAAC,aAAO,KAAK,KAAM,WAAU;AAAC,cAAMD,KAAE,GAAG,oBAAoB,MAAKC,EAAC;AAAE,YAAG,YAAU,OAAOA,IAAE;AAAC,cAAG,YAAU,OAAOA,IAAE;AAAC,gBAAG,WAASD,GAAEC,EAAC,KAAGA,GAAE,WAAW,GAAG,KAAG,kBAAgBA,GAAE,OAAM,IAAI,UAAU,oBAAoBA,EAAC,GAAG;AAAE,YAAAD,GAAEC,EAAC,EAAE;AAAA,UAAC;AAAA,QAAC,MAAM,CAAAD,GAAE,GAAGC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAC,IAAE,GAAG,UAAS,IAAG,uCAAuC,SAASA,IAAE;AAAC,UAAMD,KAAE,EAAE,uBAAuB,IAAI;AAAE,QAAG,CAACA,MAAG,CAACA,GAAE,UAAU,SAAS,EAAE,EAAE;AAAO,IAAAC,GAAE,eAAe;AAAE,UAAMG,KAAE,GAAG,oBAAoBJ,EAAC,GAAEE,KAAE,KAAK,aAAa,kBAAkB;AAAE,WAAOA,MAAGE,GAAE,GAAGF,EAAC,GAAE,KAAKE,GAAE,kBAAkB,KAAG,WAAS,EAAE,iBAAiB,MAAK,OAAO,KAAGA,GAAE,KAAK,GAAE,KAAKA,GAAE,kBAAkB,MAAIA,GAAE,KAAK,GAAE,KAAKA,GAAE,kBAAkB;AAAA,EAAE,CAAE,GAAE,EAAE,GAAG,QAAO,IAAI,MAAI;AAAC,UAAMH,KAAE,EAAE,KAAK,2BAA2B;AAAE,eAAUD,MAAKC,GAAE,IAAG,oBAAoBD,EAAC;AAAA,EAAC,CAAE,GAAE,EAAE,EAAE;AAAE,QAAM,KAAG,gBAAe,KAAG,OAAO,EAAE,IAAG,KAAG,QAAQ,EAAE,IAAG,KAAG,OAAO,EAAE,IAAG,KAAG,SAAS,EAAE,IAAG,KAAG,QAAQ,EAAE,aAAY,KAAG,QAAO,KAAG,YAAW,KAAG,cAAa,KAAG,WAAW,EAAE,KAAK,EAAE,IAAG,KAAG,+BAA8B,KAAG,EAAC,QAAO,MAAK,QAAO,KAAE,GAAE,KAAG,EAAC,QAAO,kBAAiB,QAAO,UAAS;AAAA,EAAE,MAAM,WAAW,EAAC;AAAA,IAAC,YAAYC,IAAED,IAAE;AAAC,YAAMC,IAAED,EAAC,GAAE,KAAK,mBAAiB,OAAG,KAAK,gBAAc,CAAC;AAAE,YAAMI,KAAE,EAAE,KAAK,EAAE;AAAE,iBAAUH,MAAKG,IAAE;AAAC,cAAMJ,KAAE,EAAE,uBAAuBC,EAAC,GAAEG,KAAE,EAAE,KAAKJ,EAAC,EAAE,OAAQ,CAAAC,OAAGA,OAAI,KAAK,QAAS;AAAE,iBAAOD,MAAGI,GAAE,UAAQ,KAAK,cAAc,KAAKH,EAAC;AAAA,MAAC;AAAC,WAAK,oBAAoB,GAAE,KAAK,QAAQ,UAAQ,KAAK,0BAA0B,KAAK,eAAc,KAAK,SAAS,CAAC,GAAE,KAAK,QAAQ,UAAQ,KAAK,OAAO;AAAA,IAAC;AAAA,IAAC,WAAW,UAAS;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,cAAa;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAU;AAAA,IAAC,SAAQ;AAAC,WAAK,SAAS,IAAE,KAAK,KAAK,IAAE,KAAK,KAAK;AAAA,IAAC;AAAA,IAAC,OAAM;AAAC,UAAG,KAAK,oBAAkB,KAAK,SAAS,EAAE;AAAO,UAAIA,KAAE,CAAC;AAAE,UAAG,KAAK,QAAQ,WAASA,KAAE,KAAK,uBAAuB,sCAAsC,EAAE,OAAQ,CAAAA,OAAGA,OAAI,KAAK,QAAS,EAAE,IAAK,CAAAA,OAAG,GAAG,oBAAoBA,IAAE,EAAC,QAAO,MAAE,CAAC,CAAE,IAAGA,GAAE,UAAQA,GAAE,CAAC,EAAE,iBAAiB;AAAO,UAAG,EAAE,QAAQ,KAAK,UAAS,EAAE,EAAE,iBAAiB;AAAO,iBAAUD,MAAKC,GAAE,CAAAD,GAAE,KAAK;AAAE,YAAMA,KAAE,KAAK,cAAc;AAAE,WAAK,SAAS,UAAU,OAAO,EAAE,GAAE,KAAK,SAAS,UAAU,IAAI,EAAE,GAAE,KAAK,SAAS,MAAMA,EAAC,IAAE,GAAE,KAAK,0BAA0B,KAAK,eAAc,IAAE,GAAE,KAAK,mBAAiB;AAAG,YAAMI,KAAE,SAASJ,GAAE,CAAC,EAAE,YAAY,IAAEA,GAAE,MAAM,CAAC,CAAC;AAAG,WAAK,eAAgB,MAAI;AAAC,aAAK,mBAAiB,OAAG,KAAK,SAAS,UAAU,OAAO,EAAE,GAAE,KAAK,SAAS,UAAU,IAAI,IAAG,EAAE,GAAE,KAAK,SAAS,MAAMA,EAAC,IAAE,IAAG,EAAE,QAAQ,KAAK,UAAS,EAAE;AAAA,MAAC,GAAG,KAAK,UAAS,IAAE,GAAE,KAAK,SAAS,MAAMA,EAAC,IAAE,GAAG,KAAK,SAASI,EAAC,CAAC;AAAA,IAAI;AAAA,IAAC,OAAM;AAAC,UAAG,KAAK,oBAAkB,CAAC,KAAK,SAAS,EAAE;AAAO,UAAG,EAAE,QAAQ,KAAK,UAAS,EAAE,EAAE,iBAAiB;AAAO,YAAMH,KAAE,KAAK,cAAc;AAAE,WAAK,SAAS,MAAMA,EAAC,IAAE,GAAG,KAAK,SAAS,sBAAsB,EAAEA,EAAC,CAAC,MAAK,EAAE,KAAK,QAAQ,GAAE,KAAK,SAAS,UAAU,IAAI,EAAE,GAAE,KAAK,SAAS,UAAU,OAAO,IAAG,EAAE;AAAE,iBAAUA,MAAK,KAAK,eAAc;AAAC,cAAMD,KAAE,EAAE,uBAAuBC,EAAC;AAAE,QAAAD,MAAG,CAAC,KAAK,SAASA,EAAC,KAAG,KAAK,0BAA0B,CAACC,EAAC,GAAE,KAAE;AAAA,MAAC;AAAC,WAAK,mBAAiB,MAAG,KAAK,SAAS,MAAMA,EAAC,IAAE,IAAG,KAAK,eAAgB,MAAI;AAAC,aAAK,mBAAiB,OAAG,KAAK,SAAS,UAAU,OAAO,EAAE,GAAE,KAAK,SAAS,UAAU,IAAI,EAAE,GAAE,EAAE,QAAQ,KAAK,UAAS,EAAE;AAAA,MAAC,GAAG,KAAK,UAAS,IAAE;AAAA,IAAC;AAAA,IAAC,SAASA,KAAE,KAAK,UAAS;AAAC,aAAOA,GAAE,UAAU,SAAS,EAAE;AAAA,IAAC;AAAA,IAAC,kBAAkBA,IAAE;AAAC,aAAOA,GAAE,SAAO,QAAQA,GAAE,MAAM,GAAEA,GAAE,SAAO,EAAEA,GAAE,MAAM,GAAEA;AAAA,IAAC;AAAA,IAAC,gBAAe;AAAC,aAAO,KAAK,SAAS,UAAU,SAAS,qBAAqB,IAAE,UAAQ;AAAA,IAAQ;AAAA,IAAC,sBAAqB;AAAC,UAAG,CAAC,KAAK,QAAQ,OAAO;AAAO,YAAMA,KAAE,KAAK,uBAAuB,EAAE;AAAE,iBAAUD,MAAKC,IAAE;AAAC,cAAMA,KAAE,EAAE,uBAAuBD,EAAC;AAAE,QAAAC,MAAG,KAAK,0BAA0B,CAACD,EAAC,GAAE,KAAK,SAASC,EAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,IAAC,uBAAuBA,IAAE;AAAC,YAAMD,KAAE,EAAE,KAAK,IAAG,KAAK,QAAQ,MAAM;AAAE,aAAO,EAAE,KAAKC,IAAE,KAAK,QAAQ,MAAM,EAAE,OAAQ,CAAAA,OAAG,CAACD,GAAE,SAASC,EAAC,CAAE;AAAA,IAAC;AAAA,IAAC,0BAA0BA,IAAED,IAAE;AAAC,UAAGC,GAAE,OAAO,YAAUG,MAAKH,GAAE,CAAAG,GAAE,UAAU,OAAO,aAAY,CAACJ,EAAC,GAAEI,GAAE,aAAa,iBAAgBJ,EAAC;AAAA,IAAC;AAAA,IAAC,OAAO,gBAAgBC,IAAE;AAAC,YAAMD,KAAE,CAAC;AAAE,aAAM,YAAU,OAAOC,MAAG,YAAY,KAAKA,EAAC,MAAID,GAAE,SAAO,QAAI,KAAK,KAAM,WAAU;AAAC,cAAMI,KAAE,GAAG,oBAAoB,MAAKJ,EAAC;AAAE,YAAG,YAAU,OAAOC,IAAE;AAAC,cAAG,WAASG,GAAEH,EAAC,EAAE,OAAM,IAAI,UAAU,oBAAoBA,EAAC,GAAG;AAAE,UAAAG,GAAEH,EAAC,EAAE;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAC,IAAE,GAAG,UAAS,IAAG,IAAI,SAASA,IAAE;AAAC,KAAC,QAAMA,GAAE,OAAO,WAASA,GAAE,kBAAgB,QAAMA,GAAE,eAAe,YAAUA,GAAE,eAAe;AAAE,eAAUA,MAAK,EAAE,gCAAgC,IAAI,EAAE,IAAG,oBAAoBA,IAAE,EAAC,QAAO,MAAE,CAAC,EAAE,OAAO;AAAA,EAAC,CAAE,GAAE,EAAE,EAAE;AAAE,MAAI,KAAG,OAAM,KAAG,UAAS,KAAG,SAAQ,KAAG,QAAO,KAAG,QAAO,KAAG,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,KAAG,SAAQ,KAAG,OAAM,KAAG,mBAAkB,KAAG,YAAW,KAAG,UAAS,KAAG,aAAY,KAAG,GAAG,OAAQ,SAASA,IAAED,IAAE;AAAC,WAAOC,GAAE,OAAO,CAACD,KAAE,MAAI,IAAGA,KAAE,MAAI,EAAE,CAAC;AAAA,EAAC,GAAG,CAAC,CAAC,GAAE,KAAG,CAAC,EAAE,OAAO,IAAG,CAAC,EAAE,CAAC,EAAE,OAAQ,SAASC,IAAED,IAAE;AAAC,WAAOC,GAAE,OAAO,CAACD,IAAEA,KAAE,MAAI,IAAGA,KAAE,MAAI,EAAE,CAAC;AAAA,EAAC,GAAG,CAAC,CAAC,GAAE,KAAG,cAAa,KAAG,QAAO,KAAG,aAAY,KAAG,cAAa,KAAG,QAAO,KAAG,aAAY,KAAG,eAAc,KAAG,SAAQ,KAAG,cAAa,KAAG,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AAAE,WAAS,GAAGC,IAAE;AAAC,WAAOA,MAAGA,GAAE,YAAU,IAAI,YAAY,IAAE;AAAA,EAAI;AAAC,WAAS,GAAGA,IAAE;AAAC,QAAG,QAAMA,GAAE,QAAO;AAAO,QAAG,sBAAoBA,GAAE,SAAS,GAAE;AAAC,UAAID,KAAEC,GAAE;AAAc,aAAOD,MAAGA,GAAE,eAAa;AAAA,IAAM;AAAC,WAAOC;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAE;AAAC,WAAOA,cAAa,GAAGA,EAAC,EAAE,WAASA,cAAa;AAAA,EAAO;AAAC,WAAS,GAAGA,IAAE;AAAC,WAAOA,cAAa,GAAGA,EAAC,EAAE,eAAaA,cAAa;AAAA,EAAW;AAAC,WAAS,GAAGA,IAAE;AAAC,WAAM,eAAa,OAAO,eAAaA,cAAa,GAAGA,EAAC,EAAE,cAAYA,cAAa;AAAA,EAAW;AAAC,QAAM,KAAG,EAAC,MAAK,eAAc,SAAQ,MAAG,OAAM,SAAQ,IAAG,SAASA,IAAE;AAAC,QAAID,KAAEC,GAAE;AAAM,WAAO,KAAKD,GAAE,QAAQ,EAAE,QAAS,SAASC,IAAE;AAAC,UAAIG,KAAEJ,GAAE,OAAOC,EAAC,KAAG,CAAC,GAAEC,KAAEF,GAAE,WAAWC,EAAC,KAAG,CAAC,GAAEM,KAAEP,GAAE,SAASC,EAAC;AAAE,SAAGM,EAAC,KAAG,GAAGA,EAAC,MAAI,OAAO,OAAOA,GAAE,OAAMH,EAAC,GAAE,OAAO,KAAKF,EAAC,EAAE,QAAS,SAASD,IAAE;AAAC,YAAID,KAAEE,GAAED,EAAC;AAAE,kBAAKD,KAAEO,GAAE,gBAAgBN,EAAC,IAAEM,GAAE,aAAaN,IAAE,SAAKD,KAAE,KAAGA,EAAC;AAAA,MAAC,CAAE;AAAA,IAAE,CAAE;AAAA,EAAC,GAAE,QAAO,SAASC,IAAE;AAAC,QAAID,KAAEC,GAAE,OAAMG,KAAE,EAAC,QAAO,EAAC,UAASJ,GAAE,QAAQ,UAAS,MAAK,KAAI,KAAI,KAAI,QAAO,IAAG,GAAE,OAAM,EAAC,UAAS,WAAU,GAAE,WAAU,CAAC,EAAC;AAAE,WAAO,OAAO,OAAOA,GAAE,SAAS,OAAO,OAAMI,GAAE,MAAM,GAAEJ,GAAE,SAAOI,IAAEJ,GAAE,SAAS,SAAO,OAAO,OAAOA,GAAE,SAAS,MAAM,OAAMI,GAAE,KAAK,GAAE,WAAU;AAAC,aAAO,KAAKJ,GAAE,QAAQ,EAAE,QAAS,SAASC,IAAE;AAAC,YAAIC,KAAEF,GAAE,SAASC,EAAC,GAAEM,KAAEP,GAAE,WAAWC,EAAC,KAAG,CAAC,GAAEI,KAAE,OAAO,KAAKL,GAAE,OAAO,eAAeC,EAAC,IAAED,GAAE,OAAOC,EAAC,IAAEG,GAAEH,EAAC,CAAC,EAAE,OAAQ,SAASA,IAAED,IAAE;AAAC,iBAAOC,GAAED,EAAC,IAAE,IAAGC;AAAA,QAAC,GAAG,CAAC,CAAC;AAAE,WAAGC,EAAC,KAAG,GAAGA,EAAC,MAAI,OAAO,OAAOA,GAAE,OAAMG,EAAC,GAAE,OAAO,KAAKE,EAAC,EAAE,QAAS,SAASN,IAAE;AAAC,UAAAC,GAAE,gBAAgBD,EAAC;AAAA,QAAC,CAAE;AAAA,MAAE,CAAE;AAAA,IAAC;AAAA,EAAC,GAAE,UAAS,CAAC,eAAe,EAAC;AAAE,WAAS,GAAGA,IAAE;AAAC,WAAOA,GAAE,MAAM,GAAG,EAAE,CAAC;AAAA,EAAC;AAAC,MAAI,KAAG,KAAK,KAAI,KAAG,KAAK,KAAI,KAAG,KAAK;AAAM,WAAS,KAAI;AAAC,QAAIA,KAAE,UAAU;AAAc,WAAO,QAAMA,MAAGA,GAAE,UAAQ,MAAM,QAAQA,GAAE,MAAM,IAAEA,GAAE,OAAO,IAAK,SAASA,IAAE;AAAC,aAAOA,GAAE,QAAM,MAAIA,GAAE;AAAA,IAAO,CAAE,EAAE,KAAK,GAAG,IAAE,UAAU;AAAA,EAAS;AAAC,WAAS,KAAI;AAAC,WAAM,CAAC,iCAAiC,KAAK,GAAG,CAAC;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAED,IAAEI,IAAE;AAAC,eAASJ,OAAIA,KAAE,QAAI,WAASI,OAAIA,KAAE;AAAI,QAAIF,KAAED,GAAE,sBAAsB,GAAEM,KAAE,GAAEF,KAAE;AAAE,IAAAL,MAAG,GAAGC,EAAC,MAAIM,KAAEN,GAAE,cAAY,KAAG,GAAGC,GAAE,KAAK,IAAED,GAAE,eAAa,GAAEI,KAAEJ,GAAE,eAAa,KAAG,GAAGC,GAAE,MAAM,IAAED,GAAE,gBAAc;AAAG,QAAIE,MAAG,GAAGF,EAAC,IAAE,GAAGA,EAAC,IAAE,QAAQ,gBAAeK,KAAE,CAAC,GAAG,KAAGF,IAAEK,MAAGP,GAAE,QAAMI,MAAGH,KAAEA,GAAE,aAAW,MAAII,IAAEY,MAAGjB,GAAE,OAAKI,MAAGH,KAAEA,GAAE,YAAU,MAAIE,IAAEe,KAAElB,GAAE,QAAMK,IAAEO,KAAEZ,GAAE,SAAOG;AAAE,WAAM,EAAC,OAAMe,IAAE,QAAON,IAAE,KAAIK,IAAE,OAAMV,KAAEW,IAAE,QAAOD,KAAEL,IAAE,MAAKL,IAAE,GAAEA,IAAE,GAAEU,GAAC;AAAA,EAAC;AAAC,WAAS,GAAGlB,IAAE;AAAC,QAAID,KAAE,GAAGC,EAAC,GAAEG,KAAEH,GAAE,aAAYC,KAAED,GAAE;AAAa,WAAO,KAAK,IAAID,GAAE,QAAMI,EAAC,KAAG,MAAIA,KAAEJ,GAAE,QAAO,KAAK,IAAIA,GAAE,SAAOE,EAAC,KAAG,MAAIA,KAAEF,GAAE,SAAQ,EAAC,GAAEC,GAAE,YAAW,GAAEA,GAAE,WAAU,OAAMG,IAAE,QAAOF,GAAC;AAAA,EAAC;AAAC,WAAS,GAAGD,IAAED,IAAE;AAAC,QAAII,KAAEJ,GAAE,eAAaA,GAAE,YAAY;AAAE,QAAGC,GAAE,SAASD,EAAC,EAAE,QAAM;AAAG,QAAGI,MAAG,GAAGA,EAAC,GAAE;AAAC,UAAIF,KAAEF;AAAE,SAAE;AAAC,YAAGE,MAAGD,GAAE,WAAWC,EAAC,EAAE,QAAM;AAAG,QAAAA,KAAEA,GAAE,cAAYA,GAAE;AAAA,MAAI,SAAOA;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAC,WAAS,GAAGD,IAAE;AAAC,WAAO,GAAGA,EAAC,EAAE,iBAAiBA,EAAC;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAE;AAAC,WAAM,CAAC,SAAQ,MAAK,IAAI,EAAE,QAAQ,GAAGA,EAAC,CAAC,KAAG;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAE;AAAC,aAAQ,GAAGA,EAAC,IAAEA,GAAE,gBAAcA,GAAE,aAAW,OAAO,UAAU;AAAA,EAAe;AAAC,WAAS,GAAGA,IAAE;AAAC,WAAM,WAAS,GAAGA,EAAC,IAAEA,KAAEA,GAAE,gBAAcA,GAAE,eAAa,GAAGA,EAAC,IAAEA,GAAE,OAAK,SAAO,GAAGA,EAAC;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAE;AAAC,WAAO,GAAGA,EAAC,KAAG,YAAU,GAAGA,EAAC,EAAE,WAASA,GAAE,eAAa;AAAA,EAAI;AAAC,WAAS,GAAGA,IAAE;AAAC,aAAQD,KAAE,GAAGC,EAAC,GAAEG,KAAE,GAAGH,EAAC,GAAEG,MAAG,GAAGA,EAAC,KAAG,aAAW,GAAGA,EAAC,EAAE,WAAU,CAAAA,KAAE,GAAGA,EAAC;AAAE,WAAOA,OAAI,WAAS,GAAGA,EAAC,KAAG,WAAS,GAAGA,EAAC,KAAG,aAAW,GAAGA,EAAC,EAAE,YAAUJ,KAAEI,MAAG,SAASH,IAAE;AAAC,UAAID,KAAE,WAAW,KAAK,GAAG,CAAC;AAAE,UAAG,WAAW,KAAK,GAAG,CAAC,KAAG,GAAGC,EAAC,KAAG,YAAU,GAAGA,EAAC,EAAE,SAAS,QAAO;AAAK,UAAIG,KAAE,GAAGH,EAAC;AAAE,WAAI,GAAGG,EAAC,MAAIA,KAAEA,GAAE,OAAM,GAAGA,EAAC,KAAG,CAAC,QAAO,MAAM,EAAE,QAAQ,GAAGA,EAAC,CAAC,IAAE,KAAG;AAAC,YAAIF,KAAE,GAAGE,EAAC;AAAE,YAAG,WAASF,GAAE,aAAW,WAASA,GAAE,eAAa,YAAUA,GAAE,WAAS,OAAK,CAAC,aAAY,aAAa,EAAE,QAAQA,GAAE,UAAU,KAAGF,MAAG,aAAWE,GAAE,cAAYF,MAAGE,GAAE,UAAQ,WAASA,GAAE,OAAO,QAAOE;AAAE,QAAAA,KAAEA,GAAE;AAAA,MAAU;AAAC,aAAO;AAAA,IAAI,EAAEH,EAAC,KAAGD;AAAA,EAAC;AAAC,WAAS,GAAGC,IAAE;AAAC,WAAM,CAAC,OAAM,QAAQ,EAAE,QAAQA,EAAC,KAAG,IAAE,MAAI;AAAA,EAAG;AAAC,WAAS,GAAGA,IAAED,IAAEI,IAAE;AAAC,WAAO,GAAGH,IAAE,GAAGD,IAAEI,EAAC,CAAC;AAAA,EAAC;AAAC,WAAS,GAAGH,IAAE;AAAC,WAAO,OAAO,OAAO,CAAC,GAAE,EAAC,KAAI,GAAE,OAAM,GAAE,QAAO,GAAE,MAAK,EAAC,GAAEA,EAAC;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAED,IAAE;AAAC,WAAOA,GAAE,OAAQ,SAASA,IAAEI,IAAE;AAAC,aAAOJ,GAAEI,EAAC,IAAEH,IAAED;AAAA,IAAC,GAAG,CAAC,CAAC;AAAA,EAAC;AAAC,QAAM,KAAG,EAAC,MAAK,SAAQ,SAAQ,MAAG,OAAM,QAAO,IAAG,SAASC,IAAE;AAAC,QAAID,IAAEI,KAAEH,GAAE,OAAMC,KAAED,GAAE,MAAKM,KAAEN,GAAE,SAAQI,KAAED,GAAE,SAAS,OAAMD,KAAEC,GAAE,cAAc,eAAcE,KAAE,GAAGF,GAAE,SAAS,GAAEK,KAAE,GAAGH,EAAC,GAAEa,KAAE,CAAC,IAAG,EAAE,EAAE,QAAQb,EAAC,KAAG,IAAE,WAAS;AAAQ,QAAGD,MAAGF,IAAE;AAAC,UAAIiB,KAAE,SAASnB,IAAED,IAAE;AAAC,eAAO,GAAG,YAAU,QAAOC,KAAE,cAAY,OAAOA,KAAEA,GAAE,OAAO,OAAO,CAAC,GAAED,GAAE,OAAM,EAAC,WAAUA,GAAE,UAAS,CAAC,CAAC,IAAEC,MAAGA,KAAE,GAAGA,IAAE,EAAE,CAAC;AAAA,MAAC,EAAEM,GAAE,SAAQH,EAAC,GAAEU,KAAE,GAAGT,EAAC,GAAEG,KAAE,QAAMC,KAAE,KAAG,IAAGY,KAAE,QAAMZ,KAAE,KAAG,IAAGa,KAAElB,GAAE,MAAM,UAAUe,EAAC,IAAEf,GAAE,MAAM,UAAUK,EAAC,IAAEN,GAAEM,EAAC,IAAEL,GAAE,MAAM,OAAOe,EAAC,GAAEO,KAAEvB,GAAEM,EAAC,IAAEL,GAAE,MAAM,UAAUK,EAAC,GAAEc,KAAE,GAAGlB,EAAC,GAAE4C,KAAE1B,KAAE,QAAMd,KAAEc,GAAE,gBAAc,IAAEA,GAAE,eAAa,IAAE,GAAEb,KAAEY,KAAE,IAAEI,KAAE,GAAEF,KAAEJ,GAAEZ,EAAC,GAAEiB,KAAEwB,KAAEnC,GAAEK,EAAC,IAAEC,GAAEC,EAAC,GAAEV,KAAEsC,KAAE,IAAEnC,GAAEK,EAAC,IAAE,IAAET,IAAEmB,KAAE,GAAGL,IAAEb,IAAEc,EAAC,GAAER,KAAER;AAAE,MAAAL,GAAE,cAAcF,EAAC,MAAIF,KAAE,CAAC,GAAGiB,EAAC,IAAEY,IAAE7B,GAAE,eAAa6B,KAAElB,IAAEX;AAAA,IAAE;AAAA,EAAC,GAAE,QAAO,SAASC,IAAE;AAAC,QAAID,KAAEC,GAAE,OAAMG,KAAEH,GAAE,QAAQ,SAAQC,KAAE,WAASE,KAAE,wBAAsBA;AAAE,YAAMF,OAAI,YAAU,OAAOA,OAAIA,KAAEF,GAAE,SAAS,OAAO,cAAcE,EAAC,OAAK,GAAGF,GAAE,SAAS,QAAOE,EAAC,MAAIF,GAAE,SAAS,QAAME;AAAA,EAAE,GAAE,UAAS,CAAC,eAAe,GAAE,kBAAiB,CAAC,iBAAiB,EAAC;AAAE,WAAS,GAAGD,IAAE;AAAC,WAAOA,GAAE,MAAM,GAAG,EAAE,CAAC;AAAA,EAAC;AAAC,MAAI,KAAG,EAAC,KAAI,QAAO,OAAM,QAAO,QAAO,QAAO,MAAK,OAAM;AAAE,WAAS,GAAGA,IAAE;AAAC,QAAID,IAAEI,KAAEH,GAAE,QAAOC,KAAED,GAAE,YAAWM,KAAEN,GAAE,WAAUI,KAAEJ,GAAE,WAAUE,KAAEF,GAAE,SAAQK,KAAEL,GAAE,UAASQ,KAAER,GAAE,iBAAgBkB,KAAElB,GAAE,UAASmB,KAAEnB,GAAE,cAAaa,KAAEb,GAAE,SAAQO,KAAEL,GAAE,GAAEkB,KAAE,WAASb,KAAE,IAAEA,IAAEc,KAAEnB,GAAE,GAAEuB,KAAE,WAASJ,KAAE,IAAEA,IAAEC,KAAE,cAAY,OAAOH,KAAEA,GAAE,EAAC,GAAEC,IAAE,GAAEK,GAAC,CAAC,IAAE,EAAC,GAAEL,IAAE,GAAEK,GAAC;AAAE,IAAAL,KAAEE,GAAE,GAAEG,KAAEH,GAAE;AAAE,QAAI0B,KAAE9C,GAAE,eAAe,GAAG,GAAEO,KAAEP,GAAE,eAAe,GAAG,GAAEqB,KAAE,IAAGC,KAAE,IAAGd,KAAE;AAAO,QAAGQ,IAAE;AAAC,UAAIU,KAAE,GAAGzB,EAAC,GAAEa,KAAE,gBAAeL,KAAE;AAAc,MAAAiB,OAAI,GAAGzB,EAAC,KAAG,aAAW,GAAGyB,KAAE,GAAGzB,EAAC,CAAC,EAAE,YAAU,eAAaE,OAAIW,KAAE,gBAAeL,KAAE,iBAAgBL,OAAI,OAAKA,OAAI,MAAIA,OAAI,OAAKF,OAAI,QAAMoB,KAAE,IAAGC,OAAIZ,MAAGe,OAAIlB,MAAGA,GAAE,iBAAeA,GAAE,eAAe,SAAOkB,GAAEZ,EAAC,KAAGf,GAAE,QAAOwB,MAAGjB,KAAE,IAAE,KAAIF,OAAI,OAAKA,OAAI,MAAIA,OAAI,MAAIF,OAAI,QAAMmB,KAAE,IAAGH,OAAIP,MAAGe,OAAIlB,MAAGA,GAAE,iBAAeA,GAAE,eAAe,QAAMkB,GAAEjB,EAAC,KAAGV,GAAE,OAAMmB,MAAGZ,KAAE,IAAE;AAAA,IAAG;AAAC,QAAII,IAAEsB,KAAE,OAAO,OAAO,EAAC,UAAS7B,GAAC,GAAEa,MAAG,EAAE,GAAEQ,KAAE,SAAKP,KAAE,SAASnB,IAAED,IAAE;AAAC,UAAII,KAAEH,GAAE,GAAEC,KAAED,GAAE,GAAEM,KAAEP,GAAE,oBAAkB;AAAE,aAAM,EAAC,GAAE,GAAGI,KAAEG,EAAC,IAAEA,MAAG,GAAE,GAAE,GAAGL,KAAEK,EAAC,IAAEA,MAAG,EAAC;AAAA,IAAC,EAAE,EAAC,GAAEc,IAAE,GAAEK,GAAC,GAAE,GAAGtB,EAAC,CAAC,IAAE,EAAC,GAAEiB,IAAE,GAAEK,GAAC;AAAE,WAAOL,KAAEM,GAAE,GAAED,KAAEC,GAAE,GAAElB,KAAE,OAAO,OAAO,CAAC,GAAE0B,MAAItB,KAAE,CAAC,GAAGY,EAAC,IAAEf,KAAE,MAAI,IAAGG,GAAEW,EAAC,IAAEyB,KAAE,MAAI,IAAGpC,GAAE,aAAWF,GAAE,oBAAkB,MAAI,IAAE,eAAaU,KAAE,SAAOK,KAAE,QAAM,iBAAeL,KAAE,SAAOK,KAAE,UAASb,GAAE,IAAE,OAAO,OAAO,CAAC,GAAEsB,MAAInC,KAAE,CAAC,GAAGyB,EAAC,IAAEf,KAAEgB,KAAE,OAAK,IAAG1B,GAAEwB,EAAC,IAAEyB,KAAE5B,KAAE,OAAK,IAAGrB,GAAE,YAAU,IAAGA,GAAE;AAAA,EAAC;AAAC,QAAM,KAAG,EAAC,MAAK,iBAAgB,SAAQ,MAAG,OAAM,eAAc,IAAG,SAASC,IAAE;AAAC,QAAID,KAAEC,GAAE,OAAMG,KAAEH,GAAE,SAAQC,KAAEE,GAAE,iBAAgBG,KAAE,WAASL,MAAGA,IAAEG,KAAED,GAAE,UAASD,KAAE,WAASE,MAAGA,IAAEC,KAAEF,GAAE,cAAaK,KAAE,WAASH,MAAGA,IAAEa,KAAE,EAAC,WAAU,GAAGnB,GAAE,SAAS,GAAE,WAAU,GAAGA,GAAE,SAAS,GAAE,QAAOA,GAAE,SAAS,QAAO,YAAWA,GAAE,MAAM,QAAO,iBAAgBO,IAAE,SAAQ,YAAUP,GAAE,QAAQ,SAAQ;AAAE,YAAMA,GAAE,cAAc,kBAAgBA,GAAE,OAAO,SAAO,OAAO,OAAO,CAAC,GAAEA,GAAE,OAAO,QAAO,GAAG,OAAO,OAAO,CAAC,GAAEmB,IAAE,EAAC,SAAQnB,GAAE,cAAc,eAAc,UAASA,GAAE,QAAQ,UAAS,UAASG,IAAE,cAAaM,GAAC,CAAC,CAAC,CAAC,IAAG,QAAMT,GAAE,cAAc,UAAQA,GAAE,OAAO,QAAM,OAAO,OAAO,CAAC,GAAEA,GAAE,OAAO,OAAM,GAAG,OAAO,OAAO,CAAC,GAAEmB,IAAE,EAAC,SAAQnB,GAAE,cAAc,OAAM,UAAS,YAAW,UAAS,OAAG,cAAaS,GAAC,CAAC,CAAC,CAAC,IAAGT,GAAE,WAAW,SAAO,OAAO,OAAO,CAAC,GAAEA,GAAE,WAAW,QAAO,EAAC,yBAAwBA,GAAE,UAAS,CAAC;AAAA,EAAC,GAAE,MAAK,CAAC,EAAC;AAAE,MAAI,KAAG,EAAC,SAAQ,KAAE;AAAE,QAAM,KAAG,EAAC,MAAK,kBAAiB,SAAQ,MAAG,OAAM,SAAQ,IAAG,WAAU;AAAA,EAAC,GAAE,QAAO,SAASC,IAAE;AAAC,QAAID,KAAEC,GAAE,OAAMG,KAAEH,GAAE,UAASC,KAAED,GAAE,SAAQM,KAAEL,GAAE,QAAOG,KAAE,WAASE,MAAGA,IAAEJ,KAAED,GAAE,QAAOI,KAAE,WAASH,MAAGA,IAAEM,KAAE,GAAGT,GAAE,SAAS,MAAM,GAAEmB,KAAE,CAAC,EAAE,OAAOnB,GAAE,cAAc,WAAUA,GAAE,cAAc,MAAM;AAAE,WAAOK,MAAGc,GAAE,QAAS,SAASlB,IAAE;AAAC,MAAAA,GAAE,iBAAiB,UAASG,GAAE,QAAO,EAAE;AAAA,IAAC,CAAE,GAAEE,MAAGG,GAAE,iBAAiB,UAASL,GAAE,QAAO,EAAE,GAAE,WAAU;AAAC,MAAAC,MAAGc,GAAE,QAAS,SAASlB,IAAE;AAAC,QAAAA,GAAE,oBAAoB,UAASG,GAAE,QAAO,EAAE;AAAA,MAAC,CAAE,GAAEE,MAAGG,GAAE,oBAAoB,UAASL,GAAE,QAAO,EAAE;AAAA,IAAC;AAAA,EAAC,GAAE,MAAK,CAAC,EAAC;AAAE,MAAI,KAAG,EAAC,MAAK,SAAQ,OAAM,QAAO,QAAO,OAAM,KAAI,SAAQ;AAAE,WAAS,GAAGH,IAAE;AAAC,WAAOA,GAAE,QAAQ,0BAA0B,SAASA,IAAE;AAAC,aAAO,GAAGA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,MAAI,KAAG,EAAC,OAAM,OAAM,KAAI,QAAO;AAAE,WAAS,GAAGA,IAAE;AAAC,WAAOA,GAAE,QAAQ,cAAc,SAASA,IAAE;AAAC,aAAO,GAAGA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAE;AAAC,QAAID,KAAE,GAAGC,EAAC;AAAE,WAAM,EAAC,YAAWD,GAAE,aAAY,WAAUA,GAAE,YAAW;AAAA,EAAC;AAAC,WAAS,GAAGC,IAAE;AAAC,WAAO,GAAG,GAAGA,EAAC,CAAC,EAAE,OAAK,GAAGA,EAAC,EAAE;AAAA,EAAU;AAAC,WAAS,GAAGA,IAAE;AAAC,QAAID,KAAE,GAAGC,EAAC,GAAEG,KAAEJ,GAAE,UAASE,KAAEF,GAAE,WAAUO,KAAEP,GAAE;AAAU,WAAM,6BAA6B,KAAKI,KAAEG,KAAEL,EAAC;AAAA,EAAC;AAAC,WAAS,GAAGD,IAAE;AAAC,WAAM,CAAC,QAAO,QAAO,WAAW,EAAE,QAAQ,GAAGA,EAAC,CAAC,KAAG,IAAEA,GAAE,cAAc,OAAK,GAAGA,EAAC,KAAG,GAAGA,EAAC,IAAEA,KAAE,GAAG,GAAGA,EAAC,CAAC;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAED,IAAE;AAAC,QAAII;AAAE,eAASJ,OAAIA,KAAE,CAAC;AAAG,QAAIE,KAAE,GAAGD,EAAC,GAAEM,KAAEL,QAAK,SAAOE,KAAEH,GAAE,iBAAe,SAAOG,GAAE,OAAMC,KAAE,GAAGH,EAAC,GAAEC,KAAEI,KAAE,CAACF,EAAC,EAAE,OAAOA,GAAE,kBAAgB,CAAC,GAAE,GAAGH,EAAC,IAAEA,KAAE,CAAC,CAAC,IAAEA,IAAEI,KAAEN,GAAE,OAAOG,EAAC;AAAE,WAAOI,KAAED,KAAEA,GAAE,OAAO,GAAG,GAAGH,EAAC,CAAC,CAAC;AAAA,EAAC;AAAC,WAAS,GAAGF,IAAE;AAAC,WAAO,OAAO,OAAO,CAAC,GAAEA,IAAE,EAAC,MAAKA,GAAE,GAAE,KAAIA,GAAE,GAAE,OAAMA,GAAE,IAAEA,GAAE,OAAM,QAAOA,GAAE,IAAEA,GAAE,OAAM,CAAC;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAED,IAAEI,IAAE;AAAC,WAAOJ,OAAI,KAAG,GAAG,SAASC,IAAED,IAAE;AAAC,UAAII,KAAE,GAAGH,EAAC,GAAEC,KAAE,GAAGD,EAAC,GAAEM,KAAEH,GAAE,gBAAeC,KAAEH,GAAE,aAAYC,KAAED,GAAE,cAAaI,KAAE,GAAEG,KAAE;AAAE,UAAGF,IAAE;AAAC,QAAAF,KAAEE,GAAE,OAAMJ,KAAEI,GAAE;AAAO,YAAIY,KAAE,GAAG;AAAE,SAACA,MAAG,CAACA,MAAG,YAAUnB,QAAKM,KAAEC,GAAE,YAAWE,KAAEF,GAAE;AAAA,MAAU;AAAC,aAAM,EAAC,OAAMF,IAAE,QAAOF,IAAE,GAAEG,KAAE,GAAGL,EAAC,GAAE,GAAEQ,GAAC;AAAA,IAAC,EAAER,IAAEG,EAAC,CAAC,IAAE,GAAGJ,EAAC,IAAE,SAASC,IAAED,IAAE;AAAC,UAAII,KAAE,GAAGH,IAAE,OAAG,YAAUD,EAAC;AAAE,aAAOI,GAAE,MAAIA,GAAE,MAAIH,GAAE,WAAUG,GAAE,OAAKA,GAAE,OAAKH,GAAE,YAAWG,GAAE,SAAOA,GAAE,MAAIH,GAAE,cAAaG,GAAE,QAAMA,GAAE,OAAKH,GAAE,aAAYG,GAAE,QAAMH,GAAE,aAAYG,GAAE,SAAOH,GAAE,cAAaG,GAAE,IAAEA,GAAE,MAAKA,GAAE,IAAEA,GAAE,KAAIA;AAAA,IAAC,EAAEJ,IAAEI,EAAC,IAAE,GAAG,SAASH,IAAE;AAAC,UAAID,IAAEI,KAAE,GAAGH,EAAC,GAAEC,KAAE,GAAGD,EAAC,GAAEM,KAAE,SAAOP,KAAEC,GAAE,iBAAe,SAAOD,GAAE,MAAKK,KAAE,GAAGD,GAAE,aAAYA,GAAE,aAAYG,KAAEA,GAAE,cAAY,GAAEA,KAAEA,GAAE,cAAY,CAAC,GAAEJ,KAAE,GAAGC,GAAE,cAAaA,GAAE,cAAaG,KAAEA,GAAE,eAAa,GAAEA,KAAEA,GAAE,eAAa,CAAC,GAAED,KAAE,CAACJ,GAAE,aAAW,GAAGD,EAAC,GAAEQ,KAAE,CAACP,GAAE;AAAU,aAAM,UAAQ,GAAGK,MAAGH,EAAC,EAAE,cAAYE,MAAG,GAAGF,GAAE,aAAYG,KAAEA,GAAE,cAAY,CAAC,IAAEF,KAAG,EAAC,OAAMA,IAAE,QAAOF,IAAE,GAAEG,IAAE,GAAEG,GAAC;AAAA,IAAC,EAAE,GAAGR,EAAC,CAAC,CAAC;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAE;AAAC,QAAID,IAAEI,KAAEH,GAAE,WAAUC,KAAED,GAAE,SAAQM,KAAEN,GAAE,WAAUI,KAAEE,KAAE,GAAGA,EAAC,IAAE,MAAKJ,KAAEI,KAAE,GAAGA,EAAC,IAAE,MAAKD,KAAEF,GAAE,IAAEA,GAAE,QAAM,IAAEF,GAAE,QAAM,GAAEO,KAAEL,GAAE,IAAEA,GAAE,SAAO,IAAEF,GAAE,SAAO;AAAE,YAAOG,IAAE;AAAA,MAAC,KAAK;AAAG,QAAAL,KAAE,EAAC,GAAEM,IAAE,GAAEF,GAAE,IAAEF,GAAE,OAAM;AAAE;AAAA,MAAM,KAAK;AAAG,QAAAF,KAAE,EAAC,GAAEM,IAAE,GAAEF,GAAE,IAAEA,GAAE,OAAM;AAAE;AAAA,MAAM,KAAK;AAAG,QAAAJ,KAAE,EAAC,GAAEI,GAAE,IAAEA,GAAE,OAAM,GAAEK,GAAC;AAAE;AAAA,MAAM,KAAK;AAAG,QAAAT,KAAE,EAAC,GAAEI,GAAE,IAAEF,GAAE,OAAM,GAAEO,GAAC;AAAE;AAAA,MAAM;AAAQ,QAAAT,KAAE,EAAC,GAAEI,GAAE,GAAE,GAAEA,GAAE,EAAC;AAAA,IAAC;AAAC,QAAIe,KAAEd,KAAE,GAAGA,EAAC,IAAE;AAAK,QAAG,QAAMc,IAAE;AAAC,UAAIC,KAAE,QAAMD,KAAE,WAAS;AAAQ,cAAOhB,IAAE;AAAA,QAAC,KAAK;AAAG,UAAAH,GAAEmB,EAAC,IAAEnB,GAAEmB,EAAC,KAAGf,GAAEgB,EAAC,IAAE,IAAElB,GAAEkB,EAAC,IAAE;AAAG;AAAA,QAAM,KAAK;AAAG,UAAApB,GAAEmB,EAAC,IAAEnB,GAAEmB,EAAC,KAAGf,GAAEgB,EAAC,IAAE,IAAElB,GAAEkB,EAAC,IAAE;AAAA,MAAE;AAAA,IAAC;AAAC,WAAOpB;AAAA,EAAC;AAAC,WAAS,GAAGC,IAAED,IAAE;AAAC,eAASA,OAAIA,KAAE,CAAC;AAAG,QAAII,KAAEJ,IAAEE,KAAEE,GAAE,WAAUG,KAAE,WAASL,KAAED,GAAE,YAAUC,IAAEG,KAAED,GAAE,UAASD,KAAE,WAASE,KAAEJ,GAAE,WAASI,IAAEC,KAAEF,GAAE,UAASK,KAAE,WAASH,KAAE,KAAGA,IAAEa,KAAEf,GAAE,cAAagB,KAAE,WAASD,KAAE,KAAGA,IAAEL,KAAEV,GAAE,gBAAeI,KAAE,WAASM,KAAE,KAAGA,IAAEO,KAAEjB,GAAE,aAAYkB,KAAE,WAASD,MAAGA,IAAEK,KAAEtB,GAAE,SAAQmB,KAAE,WAASG,KAAE,IAAEA,IAAEuB,KAAE,GAAG,YAAU,OAAO1B,KAAEA,KAAE,GAAGA,IAAE,EAAE,CAAC,GAAEb,KAAEF,OAAI,KAAG,KAAG,IAAGgB,KAAEvB,GAAE,MAAM,QAAOwB,KAAExB,GAAE,SAASqB,KAAEZ,KAAEF,EAAC,GAAEG,KAAE,SAASV,IAAED,IAAEI,IAAEF,IAAE;AAAC,UAAIK,KAAE,sBAAoBP,KAAE,SAASC,IAAE;AAAC,YAAID,KAAE,GAAG,GAAGC,EAAC,CAAC,GAAEG,KAAE,CAAC,YAAW,OAAO,EAAE,QAAQ,GAAGH,EAAC,EAAE,QAAQ,KAAG,KAAG,GAAGA,EAAC,IAAE,GAAGA,EAAC,IAAEA;AAAE,eAAO,GAAGG,EAAC,IAAEJ,GAAE,OAAQ,SAASC,IAAE;AAAC,iBAAO,GAAGA,EAAC,KAAG,GAAGA,IAAEG,EAAC,KAAG,WAAS,GAAGH,EAAC;AAAA,QAAC,CAAE,IAAE,CAAC;AAAA,MAAC,EAAEA,EAAC,IAAE,CAAC,EAAE,OAAOD,EAAC,GAAEK,KAAE,CAAC,EAAE,OAAOE,IAAE,CAACH,EAAC,CAAC,GAAED,KAAEE,GAAE,CAAC,GAAEC,KAAED,GAAE,OAAQ,SAASL,IAAEI,IAAE;AAAC,YAAIG,KAAE,GAAGN,IAAEG,IAAEF,EAAC;AAAE,eAAOF,GAAE,MAAI,GAAGO,GAAE,KAAIP,GAAE,GAAG,GAAEA,GAAE,QAAM,GAAGO,GAAE,OAAMP,GAAE,KAAK,GAAEA,GAAE,SAAO,GAAGO,GAAE,QAAOP,GAAE,MAAM,GAAEA,GAAE,OAAK,GAAGO,GAAE,MAAKP,GAAE,IAAI,GAAEA;AAAA,MAAC,GAAG,GAAGC,IAAEE,IAAED,EAAC,CAAC;AAAE,aAAOI,GAAE,QAAMA,GAAE,QAAMA,GAAE,MAAKA,GAAE,SAAOA,GAAE,SAAOA,GAAE,KAAIA,GAAE,IAAEA,GAAE,MAAKA,GAAE,IAAEA,GAAE,KAAIA;AAAA,IAAC,EAAE,GAAGmB,EAAC,IAAEA,KAAEA,GAAE,kBAAgB,GAAGxB,GAAE,SAAS,MAAM,GAAEQ,IAAEW,IAAEjB,EAAC,GAAE0B,KAAE,GAAG5B,GAAE,SAAS,SAAS,GAAEgB,KAAE,GAAG,EAAC,WAAUY,IAAE,SAAQL,IAAE,WAAUjB,GAAC,CAAC,GAAEK,KAAE,GAAG,OAAO,OAAO,CAAC,GAAEY,IAAEP,EAAC,CAAC,GAAEJ,KAAEL,OAAI,KAAGI,KAAEiB,IAAEM,KAAE,EAAC,KAAIxB,GAAE,MAAIE,GAAE,MAAIoC,GAAE,KAAI,QAAOpC,GAAE,SAAOF,GAAE,SAAOsC,GAAE,QAAO,MAAKtC,GAAE,OAAKE,GAAE,OAAKoC,GAAE,MAAK,OAAMpC,GAAE,QAAMF,GAAE,QAAMsC,GAAE,MAAK,GAAEtB,KAAE1B,GAAE,cAAc;AAAO,QAAGO,OAAI,MAAImB,IAAE;AAAC,UAAIZ,KAAEY,GAAEpB,EAAC;AAAE,aAAO,KAAK4B,EAAC,EAAE,QAAS,SAASlC,IAAE;AAAC,YAAID,KAAE,CAAC,IAAG,EAAE,EAAE,QAAQC,EAAC,KAAG,IAAE,IAAE,IAAGG,KAAE,CAAC,IAAG,EAAE,EAAE,QAAQH,EAAC,KAAG,IAAE,MAAI;AAAI,QAAAkC,GAAElC,EAAC,KAAGc,GAAEX,EAAC,IAAEJ;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,WAAOmC;AAAA,EAAC;AAAC,WAAS,GAAGlC,IAAED,IAAE;AAAC,eAASA,OAAIA,KAAE,CAAC;AAAG,QAAII,KAAEJ,IAAEE,KAAEE,GAAE,WAAUG,KAAEH,GAAE,UAASC,KAAED,GAAE,cAAaD,KAAEC,GAAE,SAAQE,KAAEF,GAAE,gBAAeK,KAAEL,GAAE,uBAAsBe,KAAE,WAASV,KAAE,KAAGA,IAAEW,KAAE,GAAGlB,EAAC,GAAEY,KAAEM,KAAEd,KAAE,KAAG,GAAG,OAAQ,SAASL,IAAE;AAAC,aAAO,GAAGA,EAAC,MAAImB;AAAA,IAAC,CAAE,IAAE,IAAGZ,KAAEM,GAAE,OAAQ,SAASb,IAAE;AAAC,aAAOkB,GAAE,QAAQlB,EAAC,KAAG;AAAA,IAAC,CAAE;AAAE,UAAIO,GAAE,WAASA,KAAEM;AAAG,QAAIO,KAAEb,GAAE,OAAQ,SAASR,IAAEI,IAAE;AAAC,aAAOJ,GAAEI,EAAC,IAAE,GAAGH,IAAE,EAAC,WAAUG,IAAE,UAASG,IAAE,cAAaF,IAAE,SAAQF,GAAC,CAAC,EAAE,GAAGC,EAAC,CAAC,GAAEJ;AAAA,IAAC,GAAG,CAAC,CAAC;AAAE,WAAO,OAAO,KAAKqB,EAAC,EAAE,KAAM,SAASpB,IAAED,IAAE;AAAC,aAAOqB,GAAEpB,EAAC,IAAEoB,GAAErB,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,QAAM,KAAG,EAAC,MAAK,QAAO,SAAQ,MAAG,OAAM,QAAO,IAAG,SAASC,IAAE;AAAC,QAAID,KAAEC,GAAE,OAAMG,KAAEH,GAAE,SAAQC,KAAED,GAAE;AAAK,QAAG,CAACD,GAAE,cAAcE,EAAC,EAAE,OAAM;AAAC,eAAQK,KAAEH,GAAE,UAASC,KAAE,WAASE,MAAGA,IAAEJ,KAAEC,GAAE,SAAQE,KAAE,WAASH,MAAGA,IAAEM,KAAEL,GAAE,oBAAmBe,KAAEf,GAAE,SAAQgB,KAAEhB,GAAE,UAASU,KAAEV,GAAE,cAAaI,KAAEJ,GAAE,aAAYiB,KAAEjB,GAAE,gBAAekB,KAAE,WAASD,MAAGA,IAAEK,KAAEtB,GAAE,uBAAsBmB,KAAEvB,GAAE,QAAQ,WAAUiD,KAAE,GAAG1B,EAAC,GAAEb,KAAED,OAAIwC,OAAI1B,MAAGD,KAAE,SAASrB,IAAE;AAAC,YAAG,GAAGA,EAAC,MAAI,GAAG,QAAM,CAAC;AAAE,YAAID,KAAE,GAAGC,EAAC;AAAE,eAAM,CAAC,GAAGA,EAAC,GAAED,IAAE,GAAGA,EAAC,CAAC;AAAA,MAAC,EAAEuB,EAAC,IAAE,CAAC,GAAGA,EAAC,CAAC,IAAGC,KAAE,CAACD,EAAC,EAAE,OAAOb,EAAC,EAAE,OAAQ,SAAST,IAAEG,IAAE;AAAC,eAAOH,GAAE,OAAO,GAAGG,EAAC,MAAI,KAAG,GAAGJ,IAAE,EAAC,WAAUI,IAAE,UAASgB,IAAE,cAAaN,IAAE,SAAQK,IAAE,gBAAeG,IAAE,uBAAsBI,GAAC,CAAC,IAAEtB,EAAC;AAAA,MAAC,GAAG,CAAC,CAAC,GAAEqB,KAAEzB,GAAE,MAAM,WAAUW,KAAEX,GAAE,MAAM,QAAO6B,KAAE,oBAAI,OAAIZ,KAAE,MAAGL,KAAEY,GAAE,CAAC,GAAEX,KAAE,GAAEA,KAAEW,GAAE,QAAOX,MAAI;AAAC,YAAIsB,KAAEX,GAAEX,EAAC,GAAEc,KAAE,GAAGQ,EAAC,GAAEpB,KAAE,GAAGoB,EAAC,MAAI,IAAGF,KAAE,CAAC,IAAG,EAAE,EAAE,QAAQN,EAAC,KAAG,GAAEX,KAAEiB,KAAE,UAAQ,UAASH,KAAE,GAAG9B,IAAE,EAAC,WAAUmC,IAAE,UAASf,IAAE,cAAaN,IAAE,aAAYN,IAAE,SAAQW,GAAC,CAAC,GAAE4B,KAAEd,KAAElB,KAAE,KAAG,KAAGA,KAAE,KAAG;AAAG,QAAAU,GAAET,EAAC,IAAEL,GAAEK,EAAC,MAAI+B,KAAE,GAAGA,EAAC;AAAG,YAAIN,KAAE,GAAGM,EAAC,GAAEhB,KAAE,CAAC;AAAE,YAAG1B,MAAG0B,GAAE,KAAKD,GAAEH,EAAC,KAAG,CAAC,GAAErB,MAAGyB,GAAE,KAAKD,GAAEiB,EAAC,KAAG,GAAEjB,GAAEW,EAAC,KAAG,CAAC,GAAEV,GAAE,MAAO,SAAS9B,IAAE;AAAC,iBAAOA;AAAA,QAAC,CAAE,GAAE;AAAC,UAAAW,KAAEuB,IAAElB,KAAE;AAAG;AAAA,QAAK;AAAC,QAAAY,GAAE,IAAIM,IAAEJ,EAAC;AAAA,MAAC;AAAC,UAAGd,GAAE,UAAQmB,KAAE,SAASnC,IAAE;AAAC,YAAID,KAAEwB,GAAE,KAAM,SAASxB,IAAE;AAAC,cAAII,KAAEyB,GAAE,IAAI7B,EAAC;AAAE,cAAGI,GAAE,QAAOA,GAAE,MAAM,GAAEH,EAAC,EAAE,MAAO,SAASA,IAAE;AAAC,mBAAOA;AAAA,UAAC,CAAE;AAAA,QAAC,CAAE;AAAE,YAAGD,GAAE,QAAOY,KAAEZ,IAAE;AAAA,MAAO,GAAE4B,KAAEN,KAAE,IAAE,GAAEM,KAAE,KAAG,YAAUQ,GAAER,EAAC,GAAEA,KAAI;AAAC,MAAA5B,GAAE,cAAYY,OAAIZ,GAAE,cAAcE,EAAC,EAAE,QAAM,MAAGF,GAAE,YAAUY,IAAEZ,GAAE,QAAM;AAAA,IAAG;AAAA,EAAC,GAAE,kBAAiB,CAAC,QAAQ,GAAE,MAAK,EAAC,OAAM,MAAE,EAAC;AAAE,WAAS,GAAGC,IAAED,IAAEI,IAAE;AAAC,WAAO,WAASA,OAAIA,KAAE,EAAC,GAAE,GAAE,GAAE,EAAC,IAAG,EAAC,KAAIH,GAAE,MAAID,GAAE,SAAOI,GAAE,GAAE,OAAMH,GAAE,QAAMD,GAAE,QAAMI,GAAE,GAAE,QAAOH,GAAE,SAAOD,GAAE,SAAOI,GAAE,GAAE,MAAKH,GAAE,OAAKD,GAAE,QAAMI,GAAE,EAAC;AAAA,EAAC;AAAC,WAAS,GAAGH,IAAE;AAAC,WAAM,CAAC,IAAG,IAAG,IAAG,EAAE,EAAE,KAAM,SAASD,IAAE;AAAC,aAAOC,GAAED,EAAC,KAAG;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,QAAM,KAAG,EAAC,MAAK,QAAO,SAAQ,MAAG,OAAM,QAAO,kBAAiB,CAAC,iBAAiB,GAAE,IAAG,SAASC,IAAE;AAAC,QAAID,KAAEC,GAAE,OAAMG,KAAEH,GAAE,MAAKC,KAAEF,GAAE,MAAM,WAAUO,KAAEP,GAAE,MAAM,QAAOK,KAAEL,GAAE,cAAc,iBAAgBG,KAAE,GAAGH,IAAE,EAAC,gBAAe,YAAW,CAAC,GAAEM,KAAE,GAAGN,IAAE,EAAC,aAAY,KAAE,CAAC,GAAES,KAAE,GAAGN,IAAED,EAAC,GAAEiB,KAAE,GAAGb,IAAEC,IAAEF,EAAC,GAAEe,KAAE,GAAGX,EAAC,GAAEK,KAAE,GAAGK,EAAC;AAAE,IAAAnB,GAAE,cAAcI,EAAC,IAAE,EAAC,0BAAyBK,IAAE,qBAAoBU,IAAE,mBAAkBC,IAAE,kBAAiBN,GAAC,GAAEd,GAAE,WAAW,SAAO,OAAO,OAAO,CAAC,GAAEA,GAAE,WAAW,QAAO,EAAC,gCAA+BoB,IAAE,uBAAsBN,GAAC,CAAC;AAAA,EAAC,EAAC,GAAE,KAAG,EAAC,MAAK,UAAS,SAAQ,MAAG,OAAM,QAAO,UAAS,CAAC,eAAe,GAAE,IAAG,SAASb,IAAE;AAAC,QAAID,KAAEC,GAAE,OAAMG,KAAEH,GAAE,SAAQC,KAAED,GAAE,MAAKM,KAAEH,GAAE,QAAOC,KAAE,WAASE,KAAE,CAAC,GAAE,CAAC,IAAEA,IAAEJ,KAAE,GAAG,OAAQ,SAASF,IAAEG,IAAE;AAAC,aAAOH,GAAEG,EAAC,IAAE,SAASH,IAAED,IAAEI,IAAE;AAAC,YAAIF,KAAE,GAAGD,EAAC,GAAEM,KAAE,CAAC,IAAG,EAAE,EAAE,QAAQL,EAAC,KAAG,IAAE,KAAG,GAAEG,KAAE,cAAY,OAAOD,KAAEA,GAAE,OAAO,OAAO,CAAC,GAAEJ,IAAE,EAAC,WAAUC,GAAC,CAAC,CAAC,IAAEG,IAAED,KAAEE,GAAE,CAAC,GAAEC,KAAED,GAAE,CAAC;AAAE,eAAOF,KAAEA,MAAG,GAAEG,MAAGA,MAAG,KAAGC,IAAE,CAAC,IAAG,EAAE,EAAE,QAAQL,EAAC,KAAG,IAAE,EAAC,GAAEI,IAAE,GAAEH,GAAC,IAAE,EAAC,GAAEA,IAAE,GAAEG,GAAC;AAAA,MAAC,EAAEF,IAAEJ,GAAE,OAAMK,EAAC,GAAEJ;AAAA,IAAC,GAAG,CAAC,CAAC,GAAEK,KAAEH,GAAEH,GAAE,SAAS,GAAES,KAAEH,GAAE,GAAEa,KAAEb,GAAE;AAAE,YAAMN,GAAE,cAAc,kBAAgBA,GAAE,cAAc,cAAc,KAAGS,IAAET,GAAE,cAAc,cAAc,KAAGmB,KAAGnB,GAAE,cAAcE,EAAC,IAAEC;AAAA,EAAC,EAAC,GAAE,KAAG,EAAC,MAAK,iBAAgB,SAAQ,MAAG,OAAM,QAAO,IAAG,SAASF,IAAE;AAAC,QAAID,KAAEC,GAAE,OAAMG,KAAEH,GAAE;AAAK,IAAAD,GAAE,cAAcI,EAAC,IAAE,GAAG,EAAC,WAAUJ,GAAE,MAAM,WAAU,SAAQA,GAAE,MAAM,QAAO,WAAUA,GAAE,UAAS,CAAC;AAAA,EAAC,GAAE,MAAK,CAAC,EAAC,GAAE,KAAG,EAAC,MAAK,mBAAkB,SAAQ,MAAG,OAAM,QAAO,IAAG,SAASC,IAAE;AAAC,QAAID,KAAEC,GAAE,OAAMG,KAAEH,GAAE,SAAQC,KAAED,GAAE,MAAKM,KAAEH,GAAE,UAASC,KAAE,WAASE,MAAGA,IAAEJ,KAAEC,GAAE,SAAQE,KAAE,WAASH,MAAGA,IAAEM,KAAEL,GAAE,UAASe,KAAEf,GAAE,cAAagB,KAAEhB,GAAE,aAAYU,KAAEV,GAAE,SAAQI,KAAEJ,GAAE,QAAOiB,KAAE,WAASb,MAAGA,IAAEc,KAAElB,GAAE,cAAasB,KAAE,WAASJ,KAAE,IAAEA,IAAEC,KAAE,GAAGvB,IAAE,EAAC,UAASS,IAAE,cAAaU,IAAE,SAAQL,IAAE,aAAYM,GAAC,CAAC,GAAE6B,KAAE,GAAGjD,GAAE,SAAS,GAAEU,KAAE,GAAGV,GAAE,SAAS,GAAEwB,KAAE,CAACd,IAAEe,KAAE,GAAGwB,EAAC,GAAEtC,KAAE,QAAMc,KAAE,MAAI,KAAII,KAAE7B,GAAE,cAAc,eAAciB,KAAEjB,GAAE,MAAM,WAAUY,KAAEZ,GAAE,MAAM,QAAOa,KAAE,cAAY,OAAOa,KAAEA,GAAE,OAAO,OAAO,CAAC,GAAE1B,GAAE,OAAM,EAAC,WAAUA,GAAE,UAAS,CAAC,CAAC,IAAE0B,IAAES,KAAE,YAAU,OAAOtB,KAAE,EAAC,UAASA,IAAE,SAAQA,GAAC,IAAE,OAAO,OAAO,EAAC,UAAS,GAAE,SAAQ,EAAC,GAAEA,EAAC,GAAEc,KAAE3B,GAAE,cAAc,SAAOA,GAAE,cAAc,OAAOA,GAAE,SAAS,IAAE,MAAKe,KAAE,EAAC,GAAE,GAAE,GAAE,EAAC;AAAE,QAAGc,IAAE;AAAC,UAAGxB,IAAE;AAAC,YAAI4B,IAAEjB,KAAE,QAAMS,KAAE,KAAG,IAAGK,KAAE,QAAML,KAAE,KAAG,IAAGsB,KAAE,QAAMtB,KAAE,WAAS,SAAQgB,KAAEZ,GAAEJ,EAAC,GAAEM,KAAEU,KAAElB,GAAEP,EAAC,GAAEoB,KAAEK,KAAElB,GAAEO,EAAC,GAAEF,KAAEP,KAAE,CAACT,GAAEmC,EAAC,IAAE,IAAE,GAAEV,KAAE3B,OAAI,KAAGO,GAAE8B,EAAC,IAAEnC,GAAEmC,EAAC,GAAED,KAAEpC,OAAI,KAAG,CAACE,GAAEmC,EAAC,IAAE,CAAC9B,GAAE8B,EAAC,GAAEb,KAAElC,GAAE,SAAS,OAAMkB,KAAEG,MAAGa,KAAE,GAAGA,EAAC,IAAE,EAAC,OAAM,GAAE,QAAO,EAAC,GAAEc,KAAEhD,GAAE,cAAc,kBAAkB,IAAEA,GAAE,cAAc,kBAAkB,EAAE,UAAQ,EAAC,KAAI,GAAE,OAAM,GAAE,QAAO,GAAE,MAAK,EAAC,GAAEkD,KAAEF,GAAEhC,EAAC,GAAEuB,KAAES,GAAElB,EAAC,GAAEE,KAAE,GAAG,GAAEf,GAAE8B,EAAC,GAAE7B,GAAE6B,EAAC,CAAC,GAAET,KAAEd,KAAEP,GAAE8B,EAAC,IAAE,IAAEnB,KAAEI,KAAEkB,KAAEf,GAAE,WAASE,KAAEL,KAAEkB,KAAEf,GAAE,UAASmB,KAAE9B,KAAE,CAACP,GAAE8B,EAAC,IAAE,IAAEnB,KAAEI,KAAEO,KAAEJ,GAAE,WAASW,KAAEd,KAAEO,KAAEJ,GAAE,UAASS,KAAE5C,GAAE,SAAS,SAAO,GAAGA,GAAE,SAAS,KAAK,GAAEmD,KAAEP,KAAE,QAAMnB,KAAEmB,GAAE,aAAW,IAAEA,GAAE,cAAY,IAAE,GAAED,KAAE,SAAOV,KAAE,QAAMN,KAAE,SAAOA,GAAEF,EAAC,KAAGQ,KAAE,GAAES,KAAED,KAAEa,KAAEX,IAAEU,KAAE,GAAGhC,KAAE,GAAGU,IAAEU,KAAEH,KAAEK,KAAEQ,EAAC,IAAEpB,IAAEU,IAAEpB,KAAE,GAAGe,IAAEM,EAAC,IAAEN,EAAC;AAAE,QAAAP,GAAEJ,EAAC,IAAE4B,IAAEtC,GAAEU,EAAC,IAAE4B,KAAEZ;AAAA,MAAC;AAAC,UAAGnC,IAAE;AAAC,YAAIkC,IAAEe,KAAE,QAAM9B,KAAE,KAAG,IAAGiC,MAAG,QAAMjC,KAAE,KAAG,IAAGkC,MAAG9B,GAAElB,EAAC,GAAEiD,MAAG,QAAMjD,KAAE,WAAS,SAAQkD,MAAGF,MAAGpC,GAAEgC,EAAC,GAAEO,MAAGH,MAAGpC,GAAEmC,GAAE,GAAEK,MAAG,OAAK,CAAC,IAAG,EAAE,EAAE,QAAQd,EAAC,GAAEe,MAAG,SAAOxB,KAAE,QAAMb,KAAE,SAAOA,GAAEhB,EAAC,KAAG6B,KAAE,GAAEyB,MAAGF,MAAGF,MAAGF,MAAG1C,GAAE2C,GAAE,IAAEhD,GAAEgD,GAAE,IAAEI,MAAG7B,GAAE,SAAQ+B,MAAGH,MAAGJ,MAAG1C,GAAE2C,GAAE,IAAEhD,GAAEgD,GAAE,IAAEI,MAAG7B,GAAE,UAAQ2B,KAAGK,MAAG9C,MAAG0C,MAAG,SAAS9D,IAAED,IAAEI,IAAE;AAAC,cAAIF,KAAE,GAAGD,IAAED,IAAEI,EAAC;AAAE,iBAAOF,KAAEE,KAAEA,KAAEF;AAAA,QAAC,EAAE+D,KAAGN,KAAGO,GAAE,IAAE,GAAG7C,KAAE4C,MAAGJ,KAAGF,KAAGtC,KAAE6C,MAAGJ,GAAE;AAAE,QAAAjC,GAAElB,EAAC,IAAEwD,KAAGpD,GAAEJ,EAAC,IAAEwD,MAAGR;AAAA,MAAE;AAAC,MAAA3D,GAAE,cAAcE,EAAC,IAAEa;AAAA,IAAC;AAAA,EAAC,GAAE,kBAAiB,CAAC,QAAQ,EAAC;AAAE,WAAS,GAAGd,IAAED,IAAEI,IAAE;AAAC,eAASA,OAAIA,KAAE;AAAI,QAAIF,IAAEK,IAAEF,KAAE,GAAGL,EAAC,GAAEG,KAAE,GAAGH,EAAC,KAAG,SAASC,IAAE;AAAC,UAAID,KAAEC,GAAE,sBAAsB,GAAEG,KAAE,GAAGJ,GAAE,KAAK,IAAEC,GAAE,eAAa,GAAEC,KAAE,GAAGF,GAAE,MAAM,IAAEC,GAAE,gBAAc;AAAE,aAAO,MAAIG,MAAG,MAAIF;AAAA,IAAC,EAAEF,EAAC,GAAEM,KAAE,GAAGN,EAAC,GAAES,KAAE,GAAGR,IAAEE,IAAEC,EAAC,GAAEe,KAAE,EAAC,YAAW,GAAE,WAAU,EAAC,GAAEC,KAAE,EAAC,GAAE,GAAE,GAAE,EAAC;AAAE,YAAOf,MAAG,CAACA,MAAG,CAACD,SAAM,WAAS,GAAGJ,EAAC,KAAG,GAAGM,EAAC,OAAKa,MAAGjB,KAAEF,QAAK,GAAGE,EAAC,KAAG,GAAGA,EAAC,IAAE,EAAC,aAAYK,KAAEL,IAAG,YAAW,WAAUK,GAAE,UAAS,IAAE,GAAGL,EAAC,IAAG,GAAGF,EAAC,MAAIoB,KAAE,GAAGpB,IAAE,IAAE,GAAG,KAAGA,GAAE,YAAWoB,GAAE,KAAGpB,GAAE,aAAWM,OAAIc,GAAE,IAAE,GAAGd,EAAC,KAAI,EAAC,GAAEG,GAAE,OAAKU,GAAE,aAAWC,GAAE,GAAE,GAAEX,GAAE,MAAIU,GAAE,YAAUC,GAAE,GAAE,OAAMX,GAAE,OAAM,QAAOA,GAAE,OAAM;AAAA,EAAC;AAAC,WAAS,GAAGR,IAAE;AAAC,QAAID,KAAE,oBAAI,OAAII,KAAE,oBAAI,OAAIF,KAAE,CAAC;AAAE,aAASK,GAAEN,IAAE;AAAC,MAAAG,GAAE,IAAIH,GAAE,IAAI,GAAE,CAAC,EAAE,OAAOA,GAAE,YAAU,CAAC,GAAEA,GAAE,oBAAkB,CAAC,CAAC,EAAE,QAAS,SAASA,IAAE;AAAC,YAAG,CAACG,GAAE,IAAIH,EAAC,GAAE;AAAC,cAAIC,KAAEF,GAAE,IAAIC,EAAC;AAAE,UAAAC,MAAGK,GAAEL,EAAC;AAAA,QAAC;AAAA,MAAC,CAAE,GAAEA,GAAE,KAAKD,EAAC;AAAA,IAAC;AAAC,WAAOA,GAAE,QAAS,SAASA,IAAE;AAAC,MAAAD,GAAE,IAAIC,GAAE,MAAKA,EAAC;AAAA,IAAC,CAAE,GAAEA,GAAE,QAAS,SAASA,IAAE;AAAC,MAAAG,GAAE,IAAIH,GAAE,IAAI,KAAGM,GAAEN,EAAC;AAAA,IAAC,CAAE,GAAEC;AAAA,EAAC;AAAC,MAAI,KAAG,EAAC,WAAU,UAAS,WAAU,CAAC,GAAE,UAAS,WAAU;AAAE,WAAS,KAAI;AAAC,aAAQD,KAAE,UAAU,QAAOD,KAAE,IAAI,MAAMC,EAAC,GAAEG,KAAE,GAAEA,KAAEH,IAAEG,KAAI,CAAAJ,GAAEI,EAAC,IAAE,UAAUA,EAAC;AAAE,WAAM,CAACJ,GAAE,KAAM,SAASC,IAAE;AAAC,aAAM,EAAEA,MAAG,cAAY,OAAOA,GAAE;AAAA,IAAsB,CAAE;AAAA,EAAC;AAAC,WAAS,GAAGA,IAAE;AAAC,eAASA,OAAIA,KAAE,CAAC;AAAG,QAAID,KAAEC,IAAEG,KAAEJ,GAAE,kBAAiBE,KAAE,WAASE,KAAE,CAAC,IAAEA,IAAEG,KAAEP,GAAE,gBAAeK,KAAE,WAASE,KAAE,KAAGA;AAAE,WAAO,SAASN,IAAED,IAAEI,IAAE;AAAC,iBAASA,OAAIA,KAAEC;AAAG,UAAIE,IAAEJ,IAAEG,KAAE,EAAC,WAAU,UAAS,kBAAiB,CAAC,GAAE,SAAQ,OAAO,OAAO,CAAC,GAAE,IAAGD,EAAC,GAAE,eAAc,CAAC,GAAE,UAAS,EAAC,WAAUJ,IAAE,QAAOD,GAAC,GAAE,YAAW,CAAC,GAAE,QAAO,CAAC,EAAC,GAAES,KAAE,CAAC,GAAEU,KAAE,OAAGC,KAAE,EAAC,OAAMd,IAAE,YAAW,SAASF,IAAE;AAAC,YAAIG,KAAE,cAAY,OAAOH,KAAEA,GAAEE,GAAE,OAAO,IAAEF;AAAE,QAAAU,GAAE,GAAER,GAAE,UAAQ,OAAO,OAAO,CAAC,GAAED,IAAEC,GAAE,SAAQC,EAAC,GAAED,GAAE,gBAAc,EAAC,WAAU,GAAGL,EAAC,IAAE,GAAGA,EAAC,IAAEA,GAAE,iBAAe,GAAGA,GAAE,cAAc,IAAE,CAAC,GAAE,QAAO,GAAGD,EAAC,EAAC;AAAE,YAAIG,IAAEgB,IAAEX,KAAE,SAASP,IAAE;AAAC,cAAID,KAAE,GAAGC,EAAC;AAAE,iBAAO,GAAG,OAAQ,SAASA,IAAEG,IAAE;AAAC,mBAAOH,GAAE,OAAOD,GAAE,OAAQ,SAASC,IAAE;AAAC,qBAAOA,GAAE,UAAQG;AAAA,YAAC,CAAE,CAAC;AAAA,UAAC,GAAG,CAAC,CAAC;AAAA,QAAC,GAAGD,KAAE,CAAC,EAAE,OAAOD,IAAEI,GAAE,QAAQ,SAAS,GAAEa,KAAEhB,GAAE,OAAQ,SAASF,IAAED,IAAE;AAAC,cAAII,KAAEH,GAAED,GAAE,IAAI;AAAE,iBAAOC,GAAED,GAAE,IAAI,IAAEI,KAAE,OAAO,OAAO,CAAC,GAAEA,IAAEJ,IAAE,EAAC,SAAQ,OAAO,OAAO,CAAC,GAAEI,GAAE,SAAQJ,GAAE,OAAO,GAAE,MAAK,OAAO,OAAO,CAAC,GAAEI,GAAE,MAAKJ,GAAE,IAAI,EAAC,CAAC,IAAEA,IAAEC;AAAA,QAAC,GAAG,CAAC,CAAC,GAAE,OAAO,KAAKkB,EAAC,EAAE,IAAK,SAASlB,IAAE;AAAC,iBAAOkB,GAAElB,EAAC;AAAA,QAAC,CAAE,EAAE;AAAE,eAAOK,GAAE,mBAAiBE,GAAE,OAAQ,SAASP,IAAE;AAAC,iBAAOA,GAAE;AAAA,QAAO,CAAE,GAAEK,GAAE,iBAAiB,QAAS,SAASL,IAAE;AAAC,cAAID,KAAEC,GAAE,MAAKG,KAAEH,GAAE,SAAQC,KAAE,WAASE,KAAE,CAAC,IAAEA,IAAEG,KAAEN,GAAE;AAAO,cAAG,cAAY,OAAOM,IAAE;AAAC,gBAAIF,KAAEE,GAAE,EAAC,OAAMD,IAAE,MAAKN,IAAE,UAASoB,IAAE,SAAQlB,GAAC,CAAC;AAAE,YAAAO,GAAE,KAAKJ,MAAG,WAAU;AAAA,YAAC,CAAC;AAAA,UAAC;AAAA,QAAC,CAAE,GAAEe,GAAE,OAAO;AAAA,MAAC,GAAE,aAAY,WAAU;AAAC,YAAG,CAACD,IAAE;AAAC,cAAIlB,KAAEK,GAAE,UAASN,KAAEC,GAAE,WAAUG,KAAEH,GAAE;AAAO,cAAG,GAAGD,IAAEI,EAAC,GAAE;AAAC,YAAAE,GAAE,QAAM,EAAC,WAAU,GAAGN,IAAE,GAAGI,EAAC,GAAE,YAAUE,GAAE,QAAQ,QAAQ,GAAE,QAAO,GAAGF,EAAC,EAAC,GAAEE,GAAE,QAAM,OAAGA,GAAE,YAAUA,GAAE,QAAQ,WAAUA,GAAE,iBAAiB,QAAS,SAASL,IAAE;AAAC,qBAAOK,GAAE,cAAcL,GAAE,IAAI,IAAE,OAAO,OAAO,CAAC,GAAEA,GAAE,IAAI;AAAA,YAAC,CAAE;AAAE,qBAAQC,KAAE,GAAEA,KAAEI,GAAE,iBAAiB,QAAOJ,KAAI,KAAG,SAAKI,GAAE,OAAM;AAAC,kBAAIC,KAAED,GAAE,iBAAiBJ,EAAC,GAAEG,KAAEE,GAAE,IAAGJ,KAAEI,GAAE,SAAQE,KAAE,WAASN,KAAE,CAAC,IAAEA,IAAEW,KAAEP,GAAE;AAAK,4BAAY,OAAOF,OAAIC,KAAED,GAAE,EAAC,OAAMC,IAAE,SAAQG,IAAE,MAAKK,IAAE,UAASM,GAAC,CAAC,KAAGd;AAAA,YAAE,MAAM,CAAAA,GAAE,QAAM,OAAGJ,KAAE;AAAA,UAAE;AAAA,QAAC;AAAA,MAAC,GAAE,SAAQK,KAAE,WAAU;AAAC,eAAO,IAAI,QAAS,SAASN,IAAE;AAAC,UAAAmB,GAAE,YAAY,GAAEnB,GAAEK,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC,GAAE,WAAU;AAAC,eAAOH,OAAIA,KAAE,IAAI,QAAS,SAASF,IAAE;AAAC,kBAAQ,QAAQ,EAAE,KAAM,WAAU;AAAC,YAAAE,KAAE,QAAOF,GAAEM,GAAE,CAAC;AAAA,UAAC,CAAE;AAAA,QAAC,CAAE,IAAGJ;AAAA,MAAC,IAAG,SAAQ,WAAU;AAAC,QAAAW,GAAE,GAAEK,KAAE;AAAA,MAAE,EAAC;AAAE,UAAG,CAAC,GAAGlB,IAAED,EAAC,EAAE,QAAOoB;AAAE,eAASN,KAAG;AAAC,QAAAL,GAAE,QAAS,SAASR,IAAE;AAAC,iBAAOA,GAAE;AAAA,QAAC,CAAE,GAAEQ,KAAE,CAAC;AAAA,MAAC;AAAC,aAAOW,GAAE,WAAWhB,EAAC,EAAE,KAAM,SAASH,IAAE;AAAC,SAACkB,MAAGf,GAAE,iBAAeA,GAAE,cAAcH,EAAC;AAAA,MAAC,CAAE,GAAEmB;AAAA,IAAC;AAAA,EAAC;AAAC,MAAI,KAAG,GAAG,GAAE,KAAG,GAAG,EAAC,kBAAiB,CAAC,IAAG,IAAG,IAAG,EAAE,EAAC,CAAC,GAAE,KAAG,GAAG,EAAC,kBAAiB,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,EAAC,CAAC;AAAE,QAAM,KAAG,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,WAAU,IAAG,WAAU,IAAG,YAAW,IAAG,aAAY,IAAG,OAAM,IAAG,MAAK,IAAG,gBAAe,IAAG,YAAW,IAAG,YAAW,IAAG,aAAY,IAAG,QAAO,IAAG,iBAAgB,IAAG,eAAc,IAAG,cAAa,IAAG,kBAAiB,IAAG,kBAAiB,IAAG,gBAAe,IAAG,KAAI,IAAG,gBAAe,IAAG,MAAK,IAAG,MAAK,IAAG,MAAK,IAAG,MAAK,IAAG,gBAAe,IAAG,QAAO,IAAG,YAAW,IAAG,QAAO,IAAG,iBAAgB,IAAG,eAAc,IAAG,iBAAgB,IAAG,MAAK,IAAG,WAAU,IAAG,OAAM,IAAG,OAAM,IAAG,KAAI,IAAG,qBAAoB,IAAG,UAAS,IAAG,OAAM,GAAE,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC,GAAE,KAAG,YAAW,KAAG,gBAAe,KAAG,aAAY,KAAG,WAAU,KAAG,aAAY,KAAG,OAAO,EAAE,IAAG,KAAG,SAAS,EAAE,IAAG,KAAG,OAAO,EAAE,IAAG,KAAG,QAAQ,EAAE,IAAG,KAAG,QAAQ,EAAE,GAAG,EAAE,IAAG,KAAG,UAAU,EAAE,GAAG,EAAE,IAAG,KAAG,QAAQ,EAAE,GAAG,EAAE,IAAG,KAAG,QAAO,KAAG,6DAA4D,KAAG,GAAG,EAAE,IAAI,EAAE,IAAG,KAAG,kBAAiB,KAAG,EAAE,IAAE,YAAU,aAAY,KAAG,EAAE,IAAE,cAAY,WAAU,KAAG,EAAE,IAAE,eAAa,gBAAe,KAAG,EAAE,IAAE,iBAAe,cAAa,KAAG,EAAE,IAAE,eAAa,eAAc,KAAG,EAAE,IAAE,gBAAc,cAAa,KAAG,EAAC,WAAU,MAAG,UAAS,mBAAkB,SAAQ,WAAU,QAAO,CAAC,GAAE,CAAC,GAAE,cAAa,MAAK,WAAU,SAAQ,GAAE,KAAG,EAAC,WAAU,oBAAmB,UAAS,oBAAmB,SAAQ,UAAS,QAAO,2BAA0B,cAAa,0BAAyB,WAAU,0BAAyB;AAAA,EAAE,MAAM,WAAW,EAAC;AAAA,IAAC,YAAYnB,IAAED,IAAE;AAAC,YAAMC,IAAED,EAAC,GAAE,KAAK,UAAQ,MAAK,KAAK,UAAQ,KAAK,SAAS,YAAW,KAAK,QAAM,EAAE,KAAK,KAAK,UAAS,EAAE,EAAE,CAAC,KAAG,EAAE,KAAK,KAAK,UAAS,EAAE,EAAE,CAAC,KAAG,EAAE,QAAQ,IAAG,KAAK,OAAO,GAAE,KAAK,YAAU,KAAK,cAAc;AAAA,IAAC;AAAA,IAAC,WAAW,UAAS;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,cAAa;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,SAAQ;AAAC,aAAO,KAAK,SAAS,IAAE,KAAK,KAAK,IAAE,KAAK,KAAK;AAAA,IAAC;AAAA,IAAC,OAAM;AAAC,UAAG,EAAE,KAAK,QAAQ,KAAG,KAAK,SAAS,EAAE;AAAO,YAAMC,KAAE,EAAC,eAAc,KAAK,SAAQ;AAAE,UAAG,CAAC,EAAE,QAAQ,KAAK,UAAS,IAAGA,EAAC,EAAE,kBAAiB;AAAC,YAAG,KAAK,cAAc,GAAE,kBAAiB,SAAS,mBAAiB,CAAC,KAAK,QAAQ,QAAQ,aAAa,EAAE,YAAUA,MAAI,CAAC,EAAE,OAAO,GAAG,SAAS,KAAK,QAAQ,EAAE,GAAE,GAAGA,IAAE,aAAY,CAAC;AAAE,aAAK,SAAS,MAAM,GAAE,KAAK,SAAS,aAAa,iBAAgB,IAAE,GAAE,KAAK,MAAM,UAAU,IAAI,EAAE,GAAE,KAAK,SAAS,UAAU,IAAI,EAAE,GAAE,EAAE,QAAQ,KAAK,UAAS,IAAGA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,IAAC,OAAM;AAAC,UAAG,EAAE,KAAK,QAAQ,KAAG,CAAC,KAAK,SAAS,EAAE;AAAO,YAAMA,KAAE,EAAC,eAAc,KAAK,SAAQ;AAAE,WAAK,cAAcA,EAAC;AAAA,IAAC;AAAA,IAAC,UAAS;AAAC,WAAK,WAAS,KAAK,QAAQ,QAAQ,GAAE,MAAM,QAAQ;AAAA,IAAC;AAAA,IAAC,SAAQ;AAAC,WAAK,YAAU,KAAK,cAAc,GAAE,KAAK,WAAS,KAAK,QAAQ,OAAO;AAAA,IAAC;AAAA,IAAC,cAAcA,IAAE;AAAC,UAAG,CAAC,EAAE,QAAQ,KAAK,UAAS,IAAGA,EAAC,EAAE,kBAAiB;AAAC,YAAG,kBAAiB,SAAS,gBAAgB,YAAUA,MAAI,CAAC,EAAE,OAAO,GAAG,SAAS,KAAK,QAAQ,EAAE,GAAE,IAAIA,IAAE,aAAY,CAAC;AAAE,aAAK,WAAS,KAAK,QAAQ,QAAQ,GAAE,KAAK,MAAM,UAAU,OAAO,EAAE,GAAE,KAAK,SAAS,UAAU,OAAO,EAAE,GAAE,KAAK,SAAS,aAAa,iBAAgB,OAAO,GAAE,EAAE,oBAAoB,KAAK,OAAM,QAAQ,GAAE,EAAE,QAAQ,KAAK,UAAS,IAAGA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,IAAC,WAAWA,IAAE;AAAC,UAAG,YAAU,QAAOA,KAAE,MAAM,WAAWA,EAAC,GAAG,aAAW,CAAC,EAAEA,GAAE,SAAS,KAAG,cAAY,OAAOA,GAAE,UAAU,sBAAsB,OAAM,IAAI,UAAU,GAAG,GAAG,YAAY,CAAC,gGAAgG;AAAE,aAAOA;AAAA,IAAC;AAAA,IAAC,gBAAe;AAAC,UAAG,WAAS,GAAG,OAAM,IAAI,UAAU,uEAAuE;AAAE,UAAIA,KAAE,KAAK;AAAS,mBAAW,KAAK,QAAQ,YAAUA,KAAE,KAAK,UAAQ,EAAE,KAAK,QAAQ,SAAS,IAAEA,KAAE,EAAE,KAAK,QAAQ,SAAS,IAAE,YAAU,OAAO,KAAK,QAAQ,cAAYA,KAAE,KAAK,QAAQ;AAAW,YAAMD,KAAE,KAAK,iBAAiB;AAAE,WAAK,UAAQ,GAAGC,IAAE,KAAK,OAAMD,EAAC;AAAA,IAAC;AAAA,IAAC,WAAU;AAAC,aAAO,KAAK,MAAM,UAAU,SAAS,EAAE;AAAA,IAAC;AAAA,IAAC,gBAAe;AAAC,YAAMC,KAAE,KAAK;AAAQ,UAAGA,GAAE,UAAU,SAAS,SAAS,EAAE,QAAO;AAAG,UAAGA,GAAE,UAAU,SAAS,WAAW,EAAE,QAAO;AAAG,UAAGA,GAAE,UAAU,SAAS,eAAe,EAAE,QAAM;AAAM,UAAGA,GAAE,UAAU,SAAS,iBAAiB,EAAE,QAAM;AAAS,YAAMD,KAAE,UAAQ,iBAAiB,KAAK,KAAK,EAAE,iBAAiB,eAAe,EAAE,KAAK;AAAE,aAAOC,GAAE,UAAU,SAAS,QAAQ,IAAED,KAAE,KAAG,KAAGA,KAAE,KAAG;AAAA,IAAE;AAAA,IAAC,gBAAe;AAAC,aAAO,SAAO,KAAK,SAAS,QAAQ,SAAS;AAAA,IAAC;AAAA,IAAC,aAAY;AAAC,YAAK,EAAC,QAAOC,GAAC,IAAE,KAAK;AAAQ,aAAM,YAAU,OAAOA,KAAEA,GAAE,MAAM,GAAG,EAAE,IAAK,CAAAA,OAAG,OAAO,SAASA,IAAE,EAAE,CAAE,IAAE,cAAY,OAAOA,KAAE,CAAAD,OAAGC,GAAED,IAAE,KAAK,QAAQ,IAAEC;AAAA,IAAC;AAAA,IAAC,mBAAkB;AAAC,YAAMA,KAAE,EAAC,WAAU,KAAK,cAAc,GAAE,WAAU,CAAC,EAAC,MAAK,mBAAkB,SAAQ,EAAC,UAAS,KAAK,QAAQ,SAAQ,EAAC,GAAE,EAAC,MAAK,UAAS,SAAQ,EAAC,QAAO,KAAK,WAAW,EAAC,EAAC,CAAC,EAAC;AAAE,cAAO,KAAK,aAAW,aAAW,KAAK,QAAQ,aAAW,EAAE,iBAAiB,KAAK,OAAM,UAAS,QAAQ,GAAEA,GAAE,YAAU,CAAC,EAAC,MAAK,eAAc,SAAQ,MAAE,CAAC,IAAG,EAAC,GAAGA,IAAE,GAAG,EAAE,KAAK,QAAQ,cAAa,CAAC,QAAOA,EAAC,CAAC,EAAC;AAAA,IAAC;AAAA,IAAC,gBAAgB,EAAC,KAAIA,IAAE,QAAOD,GAAC,GAAE;AAAC,YAAMI,KAAE,EAAE,KAAK,+DAA8D,KAAK,KAAK,EAAE,OAAQ,CAAAH,OAAG,EAAEA,EAAC,CAAE;AAAE,MAAAG,GAAE,UAAQ,EAAEA,IAAEJ,IAAEC,OAAI,IAAG,CAACG,GAAE,SAASJ,EAAC,CAAC,EAAE,MAAM;AAAA,IAAC;AAAA,IAAC,OAAO,gBAAgBC,IAAE;AAAC,aAAO,KAAK,KAAM,WAAU;AAAC,cAAMD,KAAE,GAAG,oBAAoB,MAAKC,EAAC;AAAE,YAAG,YAAU,OAAOA,IAAE;AAAC,cAAG,WAASD,GAAEC,EAAC,EAAE,OAAM,IAAI,UAAU,oBAAoBA,EAAC,GAAG;AAAE,UAAAD,GAAEC,EAAC,EAAE;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,IAAC,OAAO,WAAWA,IAAE;AAAC,UAAG,MAAIA,GAAE,UAAQ,YAAUA,GAAE,QAAM,UAAQA,GAAE,IAAI;AAAO,YAAMD,KAAE,EAAE,KAAK,EAAE;AAAE,iBAAUI,MAAKJ,IAAE;AAAC,cAAMA,KAAE,GAAG,YAAYI,EAAC;AAAE,YAAG,CAACJ,MAAG,UAAKA,GAAE,QAAQ,UAAU;AAAS,cAAME,KAAED,GAAE,aAAa,GAAEM,KAAEL,GAAE,SAASF,GAAE,KAAK;AAAE,YAAGE,GAAE,SAASF,GAAE,QAAQ,KAAG,aAAWA,GAAE,QAAQ,aAAW,CAACO,MAAG,cAAYP,GAAE,QAAQ,aAAWO,GAAE;AAAS,YAAGP,GAAE,MAAM,SAASC,GAAE,MAAM,MAAI,YAAUA,GAAE,QAAM,UAAQA,GAAE,OAAK,qCAAqC,KAAKA,GAAE,OAAO,OAAO,GAAG;AAAS,cAAMI,KAAE,EAAC,eAAcL,GAAE,SAAQ;AAAE,oBAAUC,GAAE,SAAOI,GAAE,aAAWJ,KAAGD,GAAE,cAAcK,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,IAAC,OAAO,sBAAsBJ,IAAE;AAAC,YAAMD,KAAE,kBAAkB,KAAKC,GAAE,OAAO,OAAO,GAAEG,KAAE,aAAWH,GAAE,KAAIC,KAAE,CAAC,IAAG,EAAE,EAAE,SAASD,GAAE,GAAG;AAAE,UAAG,CAACC,MAAG,CAACE,GAAE;AAAO,UAAGJ,MAAG,CAACI,GAAE;AAAO,MAAAH,GAAE,eAAe;AAAE,YAAMM,KAAE,KAAK,QAAQ,EAAE,IAAE,OAAK,EAAE,KAAK,MAAK,EAAE,EAAE,CAAC,KAAG,EAAE,KAAK,MAAK,EAAE,EAAE,CAAC,KAAG,EAAE,QAAQ,IAAGN,GAAE,eAAe,UAAU,GAAEI,KAAE,GAAG,oBAAoBE,EAAC;AAAE,UAAGL,GAAE,QAAOD,GAAE,gBAAgB,GAAEI,GAAE,KAAK,GAAE,KAAKA,GAAE,gBAAgBJ,EAAC;AAAE,MAAAI,GAAE,SAAS,MAAIJ,GAAE,gBAAgB,GAAEI,GAAE,KAAK,GAAEE,GAAE,MAAM;AAAA,IAAE;AAAA,EAAC;AAAC,IAAE,GAAG,UAAS,IAAG,IAAG,GAAG,qBAAqB,GAAE,EAAE,GAAG,UAAS,IAAG,IAAG,GAAG,qBAAqB,GAAE,EAAE,GAAG,UAAS,IAAG,GAAG,UAAU,GAAE,EAAE,GAAG,UAAS,IAAG,GAAG,UAAU,GAAE,EAAE,GAAG,UAAS,IAAG,IAAI,SAASN,IAAE;AAAC,IAAAA,GAAE,eAAe,GAAE,GAAG,oBAAoB,IAAI,EAAE,OAAO;AAAA,EAAC,CAAE,GAAE,EAAE,EAAE;AAAE,QAAM,KAAG,YAAW,KAAG,QAAO,KAAG,gBAAgB,EAAE,IAAG,KAAG,EAAC,WAAU,kBAAiB,eAAc,MAAK,YAAW,OAAG,WAAU,MAAG,aAAY,OAAM,GAAE,KAAG,EAAC,WAAU,UAAS,eAAc,mBAAkB,YAAW,WAAU,WAAU,WAAU,aAAY,mBAAkB;AAAA,EAAE,MAAM,WAAW,EAAC;AAAA,IAAC,YAAYA,IAAE;AAAC,YAAM,GAAE,KAAK,UAAQ,KAAK,WAAWA,EAAC,GAAE,KAAK,cAAY,OAAG,KAAK,WAAS;AAAA,IAAI;AAAA,IAAC,WAAW,UAAS;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,cAAa;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,KAAKA,IAAE;AAAC,UAAG,CAAC,KAAK,QAAQ,UAAU,QAAO,KAAK,EAAEA,EAAC;AAAE,WAAK,QAAQ;AAAE,YAAMD,KAAE,KAAK,YAAY;AAAE,WAAK,QAAQ,cAAY,EAAEA,EAAC,GAAEA,GAAE,UAAU,IAAI,EAAE,GAAE,KAAK,kBAAmB,MAAI;AAAC,UAAEC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,IAAC,KAAKA,IAAE;AAAC,WAAK,QAAQ,aAAW,KAAK,YAAY,EAAE,UAAU,OAAO,EAAE,GAAE,KAAK,kBAAmB,MAAI;AAAC,aAAK,QAAQ,GAAE,EAAEA,EAAC;AAAA,MAAC,CAAE,KAAG,EAAEA,EAAC;AAAA,IAAC;AAAA,IAAC,UAAS;AAAC,WAAK,gBAAc,EAAE,IAAI,KAAK,UAAS,EAAE,GAAE,KAAK,SAAS,OAAO,GAAE,KAAK,cAAY;AAAA,IAAG;AAAA,IAAC,cAAa;AAAC,UAAG,CAAC,KAAK,UAAS;AAAC,cAAMA,KAAE,SAAS,cAAc,KAAK;AAAE,QAAAA,GAAE,YAAU,KAAK,QAAQ,WAAU,KAAK,QAAQ,cAAYA,GAAE,UAAU,IAAI,MAAM,GAAE,KAAK,WAASA;AAAA,MAAC;AAAC,aAAO,KAAK;AAAA,IAAQ;AAAA,IAAC,kBAAkBA,IAAE;AAAC,aAAOA,GAAE,cAAY,EAAEA,GAAE,WAAW,GAAEA;AAAA,IAAC;AAAA,IAAC,UAAS;AAAC,UAAG,KAAK,YAAY;AAAO,YAAMA,KAAE,KAAK,YAAY;AAAE,WAAK,QAAQ,YAAY,OAAOA,EAAC,GAAE,EAAE,GAAGA,IAAE,IAAI,MAAI;AAAC,UAAE,KAAK,QAAQ,aAAa;AAAA,MAAC,CAAE,GAAE,KAAK,cAAY;AAAA,IAAE;AAAA,IAAC,kBAAkBA,IAAE;AAAC,QAAEA,IAAE,KAAK,YAAY,GAAE,KAAK,QAAQ,UAAU;AAAA,IAAC;AAAA,EAAC;AAAC,QAAM,KAAG,iBAAgB,KAAG,UAAU,EAAE,IAAG,KAAG,cAAc,EAAE,IAAG,KAAG,YAAW,KAAG,EAAC,WAAU,MAAG,aAAY,KAAI,GAAE,KAAG,EAAC,WAAU,WAAU,aAAY,UAAS;AAAA,EAAE,MAAM,WAAW,EAAC;AAAA,IAAC,YAAYA,IAAE;AAAC,YAAM,GAAE,KAAK,UAAQ,KAAK,WAAWA,EAAC,GAAE,KAAK,YAAU,OAAG,KAAK,uBAAqB;AAAA,IAAI;AAAA,IAAC,WAAW,UAAS;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,cAAa;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAW;AAAA,IAAC,WAAU;AAAC,WAAK,cAAY,KAAK,QAAQ,aAAW,KAAK,QAAQ,YAAY,MAAM,GAAE,EAAE,IAAI,UAAS,EAAE,GAAE,EAAE,GAAG,UAAS,IAAI,CAAAA,OAAG,KAAK,eAAeA,EAAC,CAAE,GAAE,EAAE,GAAG,UAAS,IAAI,CAAAA,OAAG,KAAK,eAAeA,EAAC,CAAE,GAAE,KAAK,YAAU;AAAA,IAAG;AAAA,IAAC,aAAY;AAAC,WAAK,cAAY,KAAK,YAAU,OAAG,EAAE,IAAI,UAAS,EAAE;AAAA,IAAE;AAAA,IAAC,eAAeA,IAAE;AAAC,YAAK,EAAC,aAAYD,GAAC,IAAE,KAAK;AAAQ,UAAGC,GAAE,WAAS,YAAUA,GAAE,WAASD,MAAGA,GAAE,SAASC,GAAE,MAAM,EAAE;AAAO,YAAMG,KAAE,EAAE,kBAAkBJ,EAAC;AAAE,YAAII,GAAE,SAAOJ,GAAE,MAAM,IAAE,KAAK,yBAAuB,KAAGI,GAAEA,GAAE,SAAO,CAAC,EAAE,MAAM,IAAEA,GAAE,CAAC,EAAE,MAAM;AAAA,IAAC;AAAA,IAAC,eAAeH,IAAE;AAAC,gBAAQA,GAAE,QAAM,KAAK,uBAAqBA,GAAE,WAAS,KAAG;AAAA,IAAU;AAAA,EAAC;AAAC,QAAM,KAAG,qDAAoD,KAAG,eAAc,KAAG,iBAAgB,KAAG;AAAA,EAAe,MAAM,GAAE;AAAA,IAAC,cAAa;AAAC,WAAK,WAAS,SAAS;AAAA,IAAI;AAAA,IAAC,WAAU;AAAC,YAAMA,KAAE,SAAS,gBAAgB;AAAY,aAAO,KAAK,IAAI,OAAO,aAAWA,EAAC;AAAA,IAAC;AAAA,IAAC,OAAM;AAAC,YAAMA,KAAE,KAAK,SAAS;AAAE,WAAK,iBAAiB,GAAE,KAAK,sBAAsB,KAAK,UAAS,IAAI,CAAAD,OAAGA,KAAEC,EAAE,GAAE,KAAK,sBAAsB,IAAG,IAAI,CAAAD,OAAGA,KAAEC,EAAE,GAAE,KAAK,sBAAsB,IAAG,IAAI,CAAAD,OAAGA,KAAEC,EAAE;AAAA,IAAC;AAAA,IAAC,QAAO;AAAC,WAAK,wBAAwB,KAAK,UAAS,UAAU,GAAE,KAAK,wBAAwB,KAAK,UAAS,EAAE,GAAE,KAAK,wBAAwB,IAAG,EAAE,GAAE,KAAK,wBAAwB,IAAG,EAAE;AAAA,IAAC;AAAA,IAAC,gBAAe;AAAC,aAAO,KAAK,SAAS,IAAE;AAAA,IAAC;AAAA,IAAC,mBAAkB;AAAC,WAAK,sBAAsB,KAAK,UAAS,UAAU,GAAE,KAAK,SAAS,MAAM,WAAS;AAAA,IAAQ;AAAA,IAAC,sBAAsBA,IAAED,IAAEI,IAAE;AAAC,YAAMF,KAAE,KAAK,SAAS;AAAE,WAAK,2BAA2BD,IAAG,CAAAA,OAAG;AAAC,YAAGA,OAAI,KAAK,YAAU,OAAO,aAAWA,GAAE,cAAYC,GAAE;AAAO,aAAK,sBAAsBD,IAAED,EAAC;AAAE,cAAMO,KAAE,OAAO,iBAAiBN,EAAC,EAAE,iBAAiBD,EAAC;AAAE,QAAAC,GAAE,MAAM,YAAYD,IAAE,GAAGI,GAAE,OAAO,WAAWG,EAAC,CAAC,CAAC,IAAI;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,IAAC,sBAAsBN,IAAED,IAAE;AAAC,YAAMI,KAAEH,GAAE,MAAM,iBAAiBD,EAAC;AAAE,MAAAI,MAAG,EAAE,iBAAiBH,IAAED,IAAEI,EAAC;AAAA,IAAC;AAAA,IAAC,wBAAwBH,IAAED,IAAE;AAAC,WAAK,2BAA2BC,IAAG,CAAAA,OAAG;AAAC,cAAMG,KAAE,EAAE,iBAAiBH,IAAED,EAAC;AAAE,iBAAOI,MAAG,EAAE,oBAAoBH,IAAED,EAAC,GAAEC,GAAE,MAAM,YAAYD,IAAEI,EAAC,KAAGH,GAAE,MAAM,eAAeD,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,IAAC,2BAA2BC,IAAED,IAAE;AAAC,UAAG,EAAEC,EAAC,EAAE,CAAAD,GAAEC,EAAC;AAAA,UAAO,YAAUG,MAAK,EAAE,KAAKH,IAAE,KAAK,QAAQ,EAAE,CAAAD,GAAEI,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,QAAM,KAAG,aAAY,KAAG,OAAO,EAAE,IAAG,KAAG,gBAAgB,EAAE,IAAG,KAAG,SAAS,EAAE,IAAG,KAAG,OAAO,EAAE,IAAG,KAAG,QAAQ,EAAE,IAAG,KAAG,SAAS,EAAE,IAAG,KAAG,gBAAgB,EAAE,IAAG,KAAG,oBAAoB,EAAE,IAAG,KAAG,kBAAkB,EAAE,IAAG,KAAG,QAAQ,EAAE,aAAY,KAAG,cAAa,KAAG,QAAO,KAAG,gBAAe,KAAG,EAAC,UAAS,MAAG,OAAM,MAAG,UAAS,KAAE,GAAE,KAAG,EAAC,UAAS,oBAAmB,OAAM,WAAU,UAAS,UAAS;AAAA,EAAE,MAAM,WAAW,EAAC;AAAA,IAAC,YAAYH,IAAED,IAAE;AAAC,YAAMC,IAAED,EAAC,GAAE,KAAK,UAAQ,EAAE,QAAQ,iBAAgB,KAAK,QAAQ,GAAE,KAAK,YAAU,KAAK,oBAAoB,GAAE,KAAK,aAAW,KAAK,qBAAqB,GAAE,KAAK,WAAS,OAAG,KAAK,mBAAiB,OAAG,KAAK,aAAW,IAAI,MAAG,KAAK,mBAAmB;AAAA,IAAC;AAAA,IAAC,WAAW,UAAS;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,cAAa;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAO;AAAA,IAAC,OAAOC,IAAE;AAAC,aAAO,KAAK,WAAS,KAAK,KAAK,IAAE,KAAK,KAAKA,EAAC;AAAA,IAAC;AAAA,IAAC,KAAKA,IAAE;AAAC,WAAK,YAAU,KAAK,oBAAkB,EAAE,QAAQ,KAAK,UAAS,IAAG,EAAC,eAAcA,GAAC,CAAC,EAAE,qBAAmB,KAAK,WAAS,MAAG,KAAK,mBAAiB,MAAG,KAAK,WAAW,KAAK,GAAE,SAAS,KAAK,UAAU,IAAI,EAAE,GAAE,KAAK,cAAc,GAAE,KAAK,UAAU,KAAM,MAAI,KAAK,aAAaA,EAAC,CAAE;AAAA,IAAE;AAAA,IAAC,OAAM;AAAC,WAAK,YAAU,CAAC,KAAK,qBAAmB,EAAE,QAAQ,KAAK,UAAS,EAAE,EAAE,qBAAmB,KAAK,WAAS,OAAG,KAAK,mBAAiB,MAAG,KAAK,WAAW,WAAW,GAAE,KAAK,SAAS,UAAU,OAAO,EAAE,GAAE,KAAK,eAAgB,MAAI,KAAK,WAAW,GAAG,KAAK,UAAS,KAAK,YAAY,CAAC;AAAA,IAAG;AAAA,IAAC,UAAS;AAAC,QAAE,IAAI,QAAO,EAAE,GAAE,EAAE,IAAI,KAAK,SAAQ,EAAE,GAAE,KAAK,UAAU,QAAQ,GAAE,KAAK,WAAW,WAAW,GAAE,MAAM,QAAQ;AAAA,IAAC;AAAA,IAAC,eAAc;AAAC,WAAK,cAAc;AAAA,IAAC;AAAA,IAAC,sBAAqB;AAAC,aAAO,IAAI,GAAG,EAAC,WAAU,QAAQ,KAAK,QAAQ,QAAQ,GAAE,YAAW,KAAK,YAAY,EAAC,CAAC;AAAA,IAAC;AAAA,IAAC,uBAAsB;AAAC,aAAO,IAAI,GAAG,EAAC,aAAY,KAAK,SAAQ,CAAC;AAAA,IAAC;AAAA,IAAC,aAAaA,IAAE;AAAC,eAAS,KAAK,SAAS,KAAK,QAAQ,KAAG,SAAS,KAAK,OAAO,KAAK,QAAQ,GAAE,KAAK,SAAS,MAAM,UAAQ,SAAQ,KAAK,SAAS,gBAAgB,aAAa,GAAE,KAAK,SAAS,aAAa,cAAa,IAAE,GAAE,KAAK,SAAS,aAAa,QAAO,QAAQ,GAAE,KAAK,SAAS,YAAU;AAAE,YAAMD,KAAE,EAAE,QAAQ,eAAc,KAAK,OAAO;AAAE,MAAAA,OAAIA,GAAE,YAAU,IAAG,EAAE,KAAK,QAAQ,GAAE,KAAK,SAAS,UAAU,IAAI,EAAE,GAAE,KAAK,eAAgB,MAAI;AAAC,aAAK,QAAQ,SAAO,KAAK,WAAW,SAAS,GAAE,KAAK,mBAAiB,OAAG,EAAE,QAAQ,KAAK,UAAS,IAAG,EAAC,eAAcC,GAAC,CAAC;AAAA,MAAC,GAAG,KAAK,SAAQ,KAAK,YAAY,CAAC;AAAA,IAAC;AAAA,IAAC,qBAAoB;AAAC,QAAE,GAAG,KAAK,UAAS,IAAI,CAAAA,OAAG;AAAC,qBAAWA,GAAE,QAAM,KAAK,QAAQ,WAAS,KAAK,KAAK,IAAE,KAAK,2BAA2B;AAAA,MAAE,CAAE,GAAE,EAAE,GAAG,QAAO,IAAI,MAAI;AAAC,aAAK,YAAU,CAAC,KAAK,oBAAkB,KAAK,cAAc;AAAA,MAAC,CAAE,GAAE,EAAE,GAAG,KAAK,UAAS,IAAI,CAAAA,OAAG;AAAC,UAAE,IAAI,KAAK,UAAS,IAAI,CAAAD,OAAG;AAAC,eAAK,aAAWC,GAAE,UAAQ,KAAK,aAAWD,GAAE,WAAS,aAAW,KAAK,QAAQ,WAAS,KAAK,QAAQ,YAAU,KAAK,KAAK,IAAE,KAAK,2BAA2B;AAAA,QAAE,CAAE;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,IAAC,aAAY;AAAC,WAAK,SAAS,MAAM,UAAQ,QAAO,KAAK,SAAS,aAAa,eAAc,IAAE,GAAE,KAAK,SAAS,gBAAgB,YAAY,GAAE,KAAK,SAAS,gBAAgB,MAAM,GAAE,KAAK,mBAAiB,OAAG,KAAK,UAAU,KAAM,MAAI;AAAC,iBAAS,KAAK,UAAU,OAAO,EAAE,GAAE,KAAK,kBAAkB,GAAE,KAAK,WAAW,MAAM,GAAE,EAAE,QAAQ,KAAK,UAAS,EAAE;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,IAAC,cAAa;AAAC,aAAO,KAAK,SAAS,UAAU,SAAS,MAAM;AAAA,IAAC;AAAA,IAAC,6BAA4B;AAAC,UAAG,EAAE,QAAQ,KAAK,UAAS,EAAE,EAAE,iBAAiB;AAAO,YAAMC,KAAE,KAAK,SAAS,eAAa,SAAS,gBAAgB,cAAaD,KAAE,KAAK,SAAS,MAAM;AAAU,mBAAWA,MAAG,KAAK,SAAS,UAAU,SAAS,EAAE,MAAIC,OAAI,KAAK,SAAS,MAAM,YAAU,WAAU,KAAK,SAAS,UAAU,IAAI,EAAE,GAAE,KAAK,eAAgB,MAAI;AAAC,aAAK,SAAS,UAAU,OAAO,EAAE,GAAE,KAAK,eAAgB,MAAI;AAAC,eAAK,SAAS,MAAM,YAAUD;AAAA,QAAC,GAAG,KAAK,OAAO;AAAA,MAAC,GAAG,KAAK,OAAO,GAAE,KAAK,SAAS,MAAM;AAAA,IAAE;AAAA,IAAC,gBAAe;AAAC,YAAMC,KAAE,KAAK,SAAS,eAAa,SAAS,gBAAgB,cAAaD,KAAE,KAAK,WAAW,SAAS,GAAEI,KAAEJ,KAAE;AAAE,UAAGI,MAAG,CAACH,IAAE;AAAC,cAAMA,KAAE,EAAE,IAAE,gBAAc;AAAe,aAAK,SAAS,MAAMA,EAAC,IAAE,GAAGD,EAAC;AAAA,MAAI;AAAC,UAAG,CAACI,MAAGH,IAAE;AAAC,cAAMA,KAAE,EAAE,IAAE,iBAAe;AAAc,aAAK,SAAS,MAAMA,EAAC,IAAE,GAAGD,EAAC;AAAA,MAAI;AAAA,IAAC;AAAA,IAAC,oBAAmB;AAAC,WAAK,SAAS,MAAM,cAAY,IAAG,KAAK,SAAS,MAAM,eAAa;AAAA,IAAE;AAAA,IAAC,OAAO,gBAAgBC,IAAED,IAAE;AAAC,aAAO,KAAK,KAAM,WAAU;AAAC,cAAMI,KAAE,GAAG,oBAAoB,MAAKH,EAAC;AAAE,YAAG,YAAU,OAAOA,IAAE;AAAC,cAAG,WAASG,GAAEH,EAAC,EAAE,OAAM,IAAI,UAAU,oBAAoBA,EAAC,GAAG;AAAE,UAAAG,GAAEH,EAAC,EAAED,EAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAC,IAAE,GAAG,UAAS,IAAG,4BAA4B,SAASC,IAAE;AAAC,UAAMD,KAAE,EAAE,uBAAuB,IAAI;AAAE,KAAC,KAAI,MAAM,EAAE,SAAS,KAAK,OAAO,KAAGC,GAAE,eAAe,GAAE,EAAE,IAAID,IAAE,IAAI,CAAAC,OAAG;AAAC,MAAAA,GAAE,oBAAkB,EAAE,IAAID,IAAE,IAAI,MAAI;AAAC,UAAE,IAAI,KAAG,KAAK,MAAM;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAE,UAAMI,KAAE,EAAE,QAAQ,aAAa;AAAE,IAAAA,MAAG,GAAG,YAAYA,EAAC,EAAE,KAAK,GAAE,GAAG,oBAAoBJ,EAAC,EAAE,OAAO,IAAI;AAAA,EAAC,CAAE,GAAE,EAAE,EAAE,GAAE,EAAE,EAAE;AAAE,QAAM,KAAG,iBAAgB,KAAG,aAAY,KAAG,OAAO,EAAE,GAAG,EAAE,IAAG,KAAG,QAAO,KAAG,WAAU,KAAG,UAAS,KAAG,mBAAkB,KAAG,OAAO,EAAE,IAAG,KAAG,QAAQ,EAAE,IAAG,KAAG,OAAO,EAAE,IAAG,KAAG,gBAAgB,EAAE,IAAG,KAAG,SAAS,EAAE,IAAG,KAAG,SAAS,EAAE,IAAG,KAAG,QAAQ,EAAE,GAAG,EAAE,IAAG,KAAG,kBAAkB,EAAE,IAAG,KAAG,EAAC,UAAS,MAAG,UAAS,MAAG,QAAO,MAAE,GAAE,KAAG,EAAC,UAAS,oBAAmB,UAAS,WAAU,QAAO,UAAS;AAAA,EAAE,MAAM,WAAW,EAAC;AAAA,IAAC,YAAYC,IAAED,IAAE;AAAC,YAAMC,IAAED,EAAC,GAAE,KAAK,WAAS,OAAG,KAAK,YAAU,KAAK,oBAAoB,GAAE,KAAK,aAAW,KAAK,qBAAqB,GAAE,KAAK,mBAAmB;AAAA,IAAC;AAAA,IAAC,WAAW,UAAS;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,cAAa;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAW;AAAA,IAAC,OAAOC,IAAE;AAAC,aAAO,KAAK,WAAS,KAAK,KAAK,IAAE,KAAK,KAAKA,EAAC;AAAA,IAAC;AAAA,IAAC,KAAKA,IAAE;AAAC,WAAK,YAAU,EAAE,QAAQ,KAAK,UAAS,IAAG,EAAC,eAAcA,GAAC,CAAC,EAAE,qBAAmB,KAAK,WAAS,MAAG,KAAK,UAAU,KAAK,GAAE,KAAK,QAAQ,UAAS,IAAI,KAAI,KAAK,GAAE,KAAK,SAAS,aAAa,cAAa,IAAE,GAAE,KAAK,SAAS,aAAa,QAAO,QAAQ,GAAE,KAAK,SAAS,UAAU,IAAI,EAAE,GAAE,KAAK,eAAgB,MAAI;AAAC,aAAK,QAAQ,UAAQ,CAAC,KAAK,QAAQ,YAAU,KAAK,WAAW,SAAS,GAAE,KAAK,SAAS,UAAU,IAAI,EAAE,GAAE,KAAK,SAAS,UAAU,OAAO,EAAE,GAAE,EAAE,QAAQ,KAAK,UAAS,IAAG,EAAC,eAAcA,GAAC,CAAC;AAAA,MAAC,GAAG,KAAK,UAAS,IAAE;AAAA,IAAE;AAAA,IAAC,OAAM;AAAC,WAAK,aAAW,EAAE,QAAQ,KAAK,UAAS,EAAE,EAAE,qBAAmB,KAAK,WAAW,WAAW,GAAE,KAAK,SAAS,KAAK,GAAE,KAAK,WAAS,OAAG,KAAK,SAAS,UAAU,IAAI,EAAE,GAAE,KAAK,UAAU,KAAK,GAAE,KAAK,eAAgB,MAAI;AAAC,aAAK,SAAS,UAAU,OAAO,IAAG,EAAE,GAAE,KAAK,SAAS,gBAAgB,YAAY,GAAE,KAAK,SAAS,gBAAgB,MAAM,GAAE,KAAK,QAAQ,UAAS,IAAI,KAAI,MAAM,GAAE,EAAE,QAAQ,KAAK,UAAS,EAAE;AAAA,MAAC,GAAG,KAAK,UAAS,IAAE;AAAA,IAAG;AAAA,IAAC,UAAS;AAAC,WAAK,UAAU,QAAQ,GAAE,KAAK,WAAW,WAAW,GAAE,MAAM,QAAQ;AAAA,IAAC;AAAA,IAAC,sBAAqB;AAAC,YAAMA,KAAE,QAAQ,KAAK,QAAQ,QAAQ;AAAE,aAAO,IAAI,GAAG,EAAC,WAAU,sBAAqB,WAAUA,IAAE,YAAW,MAAG,aAAY,KAAK,SAAS,YAAW,eAAcA,KAAE,MAAI;AAAC,qBAAW,KAAK,QAAQ,WAAS,KAAK,KAAK,IAAE,EAAE,QAAQ,KAAK,UAAS,EAAE;AAAA,MAAC,IAAE,KAAI,CAAC;AAAA,IAAC;AAAA,IAAC,uBAAsB;AAAC,aAAO,IAAI,GAAG,EAAC,aAAY,KAAK,SAAQ,CAAC;AAAA,IAAC;AAAA,IAAC,qBAAoB;AAAC,QAAE,GAAG,KAAK,UAAS,IAAI,CAAAA,OAAG;AAAC,qBAAWA,GAAE,QAAM,KAAK,QAAQ,WAAS,KAAK,KAAK,IAAE,EAAE,QAAQ,KAAK,UAAS,EAAE;AAAA,MAAE,CAAE;AAAA,IAAC;AAAA,IAAC,OAAO,gBAAgBA,IAAE;AAAC,aAAO,KAAK,KAAM,WAAU;AAAC,cAAMD,KAAE,GAAG,oBAAoB,MAAKC,EAAC;AAAE,YAAG,YAAU,OAAOA,IAAE;AAAC,cAAG,WAASD,GAAEC,EAAC,KAAGA,GAAE,WAAW,GAAG,KAAG,kBAAgBA,GAAE,OAAM,IAAI,UAAU,oBAAoBA,EAAC,GAAG;AAAE,UAAAD,GAAEC,EAAC,EAAE,IAAI;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAC,IAAE,GAAG,UAAS,IAAG,gCAAgC,SAASA,IAAE;AAAC,UAAMD,KAAE,EAAE,uBAAuB,IAAI;AAAE,QAAG,CAAC,KAAI,MAAM,EAAE,SAAS,KAAK,OAAO,KAAGC,GAAE,eAAe,GAAE,EAAE,IAAI,EAAE;AAAO,MAAE,IAAID,IAAE,IAAI,MAAI;AAAC,QAAE,IAAI,KAAG,KAAK,MAAM;AAAA,IAAC,CAAE;AAAE,UAAMI,KAAE,EAAE,QAAQ,EAAE;AAAE,IAAAA,MAAGA,OAAIJ,MAAG,GAAG,YAAYI,EAAC,EAAE,KAAK,GAAE,GAAG,oBAAoBJ,EAAC,EAAE,OAAO,IAAI;AAAA,EAAC,CAAE,GAAE,EAAE,GAAG,QAAO,IAAI,MAAI;AAAC,eAAUC,MAAK,EAAE,KAAK,EAAE,EAAE,IAAG,oBAAoBA,EAAC,EAAE,KAAK;AAAA,EAAC,CAAE,GAAE,EAAE,GAAG,QAAO,IAAI,MAAI;AAAC,eAAUA,MAAK,EAAE,KAAK,8CAA8C,EAAE,aAAU,iBAAiBA,EAAC,EAAE,YAAU,GAAG,oBAAoBA,EAAC,EAAE,KAAK;AAAA,EAAC,CAAE,GAAE,EAAE,EAAE,GAAE,EAAE,EAAE;AAAE,QAAM,KAAG,EAAC,KAAI,CAAC,SAAQ,OAAM,MAAK,QAAO,QAAO,gBAAgB,GAAE,GAAE,CAAC,UAAS,QAAO,SAAQ,KAAK,GAAE,MAAK,CAAC,GAAE,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,KAAI,CAAC,GAAE,MAAK,CAAC,GAAE,IAAG,CAAC,GAAE,KAAI,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,GAAE,CAAC,GAAE,KAAI,CAAC,OAAM,UAAS,OAAM,SAAQ,SAAQ,QAAQ,GAAE,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,GAAE,CAAC,GAAE,KAAI,CAAC,GAAE,GAAE,CAAC,GAAE,OAAM,CAAC,GAAE,MAAK,CAAC,GAAE,KAAI,CAAC,GAAE,KAAI,CAAC,GAAE,QAAO,CAAC,GAAE,GAAE,CAAC,GAAE,IAAG,CAAC,EAAC,GAAE,KAAG,oBAAI,IAAI,CAAC,cAAa,QAAO,QAAO,YAAW,YAAW,UAAS,OAAM,YAAY,CAAC,GAAE,KAAG,2DAA0D,KAAG,CAACA,IAAED,OAAI;AAAC,UAAMI,KAAEH,GAAE,SAAS,YAAY;AAAE,WAAOD,GAAE,SAASI,EAAC,IAAE,CAAC,GAAG,IAAIA,EAAC,KAAG,QAAQ,GAAG,KAAKH,GAAE,SAAS,CAAC,IAAED,GAAE,OAAQ,CAAAC,OAAGA,cAAa,MAAO,EAAE,KAAM,CAAAA,OAAGA,GAAE,KAAKG,EAAC,CAAE;AAAA,EAAC,GAAE,KAAG,EAAC,WAAU,IAAG,SAAQ,CAAC,GAAE,YAAW,IAAG,MAAK,OAAG,UAAS,MAAG,YAAW,MAAK,UAAS,cAAa,GAAE,KAAG,EAAC,WAAU,UAAS,SAAQ,UAAS,YAAW,qBAAoB,MAAK,WAAU,UAAS,WAAU,YAAW,mBAAkB,UAAS,SAAQ,GAAE,KAAG,EAAC,OAAM,kCAAiC,UAAS,mBAAkB;AAAA,EAAE,MAAM,WAAW,EAAC;AAAA,IAAC,YAAYH,IAAE;AAAC,YAAM,GAAE,KAAK,UAAQ,KAAK,WAAWA,EAAC;AAAA,IAAC;AAAA,IAAC,WAAW,UAAS;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,cAAa;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAiB;AAAA,IAAC,aAAY;AAAC,aAAO,OAAO,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAK,CAAAA,OAAG,KAAK,yBAAyBA,EAAC,CAAE,EAAE,OAAO,OAAO;AAAA,IAAC;AAAA,IAAC,aAAY;AAAC,aAAO,KAAK,WAAW,EAAE,SAAO;AAAA,IAAC;AAAA,IAAC,cAAcA,IAAE;AAAC,aAAO,KAAK,cAAcA,EAAC,GAAE,KAAK,QAAQ,UAAQ,EAAC,GAAG,KAAK,QAAQ,SAAQ,GAAGA,GAAC,GAAE;AAAA,IAAI;AAAA,IAAC,SAAQ;AAAC,YAAMA,KAAE,SAAS,cAAc,KAAK;AAAE,MAAAA,GAAE,YAAU,KAAK,eAAe,KAAK,QAAQ,QAAQ;AAAE,iBAAS,CAACD,IAAEI,EAAC,KAAI,OAAO,QAAQ,KAAK,QAAQ,OAAO,EAAE,MAAK,YAAYH,IAAEG,IAAEJ,EAAC;AAAE,YAAMA,KAAEC,GAAE,SAAS,CAAC,GAAEG,KAAE,KAAK,yBAAyB,KAAK,QAAQ,UAAU;AAAE,aAAOA,MAAGJ,GAAE,UAAU,IAAI,GAAGI,GAAE,MAAM,GAAG,CAAC,GAAEJ;AAAA,IAAC;AAAA,IAAC,iBAAiBC,IAAE;AAAC,YAAM,iBAAiBA,EAAC,GAAE,KAAK,cAAcA,GAAE,OAAO;AAAA,IAAC;AAAA,IAAC,cAAcA,IAAE;AAAC,iBAAS,CAACD,IAAEI,EAAC,KAAI,OAAO,QAAQH,EAAC,EAAE,OAAM,iBAAiB,EAAC,UAASD,IAAE,OAAMI,GAAC,GAAE,EAAE;AAAA,IAAC;AAAA,IAAC,YAAYH,IAAED,IAAEI,IAAE;AAAC,YAAMF,KAAE,EAAE,QAAQE,IAAEH,EAAC;AAAE,MAAAC,QAAKF,KAAE,KAAK,yBAAyBA,EAAC,KAAG,EAAEA,EAAC,IAAE,KAAK,sBAAsB,EAAEA,EAAC,GAAEE,EAAC,IAAE,KAAK,QAAQ,OAAKA,GAAE,YAAU,KAAK,eAAeF,EAAC,IAAEE,GAAE,cAAYF,KAAEE,GAAE,OAAO;AAAA,IAAE;AAAA,IAAC,eAAeD,IAAE;AAAC,aAAO,KAAK,QAAQ,WAAS,SAASA,IAAED,IAAEI,IAAE;AAAC,YAAG,CAACH,GAAE,OAAO,QAAOA;AAAE,YAAGG,MAAG,cAAY,OAAOA,GAAE,QAAOA,GAAEH,EAAC;AAAE,cAAMC,KAAG,IAAI,OAAO,YAAW,gBAAgBD,IAAE,WAAW,GAAEM,KAAE,CAAC,EAAE,OAAO,GAAGL,GAAE,KAAK,iBAAiB,GAAG,CAAC;AAAE,mBAAUD,MAAKM,IAAE;AAAC,gBAAMH,KAAEH,GAAE,SAAS,YAAY;AAAE,cAAG,CAAC,OAAO,KAAKD,EAAC,EAAE,SAASI,EAAC,GAAE;AAAC,YAAAH,GAAE,OAAO;AAAE;AAAA,UAAQ;AAAC,gBAAMC,KAAE,CAAC,EAAE,OAAO,GAAGD,GAAE,UAAU,GAAEM,KAAE,CAAC,EAAE,OAAOP,GAAE,GAAG,KAAG,CAAC,GAAEA,GAAEI,EAAC,KAAG,CAAC,CAAC;AAAE,qBAAUJ,MAAKE,GAAE,IAAGF,IAAEO,EAAC,KAAGN,GAAE,gBAAgBD,GAAE,QAAQ;AAAA,QAAC;AAAC,eAAOE,GAAE,KAAK;AAAA,MAAS,EAAED,IAAE,KAAK,QAAQ,WAAU,KAAK,QAAQ,UAAU,IAAEA;AAAA,IAAC;AAAA,IAAC,yBAAyBA,IAAE;AAAC,aAAO,EAAEA,IAAE,CAAC,QAAO,IAAI,CAAC;AAAA,IAAC;AAAA,IAAC,sBAAsBA,IAAED,IAAE;AAAC,UAAG,KAAK,QAAQ,KAAK,QAAOA,GAAE,YAAU,IAAG,KAAKA,GAAE,OAAOC,EAAC;AAAE,MAAAD,GAAE,cAAYC,GAAE;AAAA,IAAW;AAAA,EAAC;AAAC,QAAM,KAAG,oBAAI,IAAI,CAAC,YAAW,aAAY,YAAY,CAAC,GAAE,KAAG,QAAO,KAAG,QAAO,KAAG,kBAAiB,KAAG,UAAS,KAAG,iBAAgB,KAAG,SAAQ,KAAG,SAAQ,KAAG,EAAC,MAAK,QAAO,KAAI,OAAM,OAAM,EAAE,IAAE,SAAO,SAAQ,QAAO,UAAS,MAAK,EAAE,IAAE,UAAQ,OAAM,GAAE,KAAG,EAAC,WAAU,IAAG,WAAU,MAAG,UAAS,mBAAkB,WAAU,OAAG,aAAY,IAAG,OAAM,GAAE,oBAAmB,CAAC,OAAM,SAAQ,UAAS,MAAM,GAAE,MAAK,OAAG,QAAO,CAAC,GAAE,CAAC,GAAE,WAAU,OAAM,cAAa,MAAK,UAAS,MAAG,YAAW,MAAK,UAAS,OAAG,UAAS,gHAA+G,OAAM,IAAG,SAAQ,cAAa,GAAE,KAAG,EAAC,WAAU,UAAS,WAAU,WAAU,UAAS,oBAAmB,WAAU,4BAA2B,aAAY,qBAAoB,OAAM,mBAAkB,oBAAmB,SAAQ,MAAK,WAAU,QAAO,2BAA0B,WAAU,qBAAoB,cAAa,0BAAyB,UAAS,WAAU,YAAW,mBAAkB,UAAS,oBAAmB,UAAS,UAAS,OAAM,6BAA4B,SAAQ,SAAQ;AAAA,EAAE,MAAM,WAAW,EAAC;AAAA,IAAC,YAAYA,IAAED,IAAE;AAAC,UAAG,WAAS,GAAG,OAAM,IAAI,UAAU,sEAAsE;AAAE,YAAMC,IAAED,EAAC,GAAE,KAAK,aAAW,MAAG,KAAK,WAAS,GAAE,KAAK,aAAW,MAAK,KAAK,iBAAe,CAAC,GAAE,KAAK,UAAQ,MAAK,KAAK,mBAAiB,MAAK,KAAK,cAAY,MAAK,KAAK,MAAI,MAAK,KAAK,cAAc,GAAE,KAAK,QAAQ,YAAU,KAAK,UAAU;AAAA,IAAC;AAAA,IAAC,WAAW,UAAS;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,cAAa;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAS;AAAA,IAAC,SAAQ;AAAC,WAAK,aAAW;AAAA,IAAE;AAAA,IAAC,UAAS;AAAC,WAAK,aAAW;AAAA,IAAE;AAAA,IAAC,gBAAe;AAAC,WAAK,aAAW,CAAC,KAAK;AAAA,IAAU;AAAA,IAAC,SAAQ;AAAC,WAAK,eAAa,KAAK,SAAS,IAAE,KAAK,OAAO,IAAE,KAAK,OAAO;AAAA,IAAE;AAAA,IAAC,UAAS;AAAC,mBAAa,KAAK,QAAQ,GAAE,EAAE,IAAI,KAAK,SAAS,QAAQ,EAAE,GAAE,IAAG,KAAK,iBAAiB,GAAE,KAAK,SAAS,aAAa,wBAAwB,KAAG,KAAK,SAAS,aAAa,SAAQ,KAAK,SAAS,aAAa,wBAAwB,CAAC,GAAE,KAAK,eAAe,GAAE,MAAM,QAAQ;AAAA,IAAC;AAAA,IAAC,OAAM;AAAC,UAAG,WAAS,KAAK,SAAS,MAAM,QAAQ,OAAM,IAAI,MAAM,qCAAqC;AAAE,UAAG,CAAC,KAAK,eAAe,KAAG,CAAC,KAAK,WAAW;AAAO,YAAMC,KAAE,EAAE,QAAQ,KAAK,UAAS,KAAK,YAAY,UAAU,MAAM,CAAC,GAAED,MAAG,EAAE,KAAK,QAAQ,KAAG,KAAK,SAAS,cAAc,iBAAiB,SAAS,KAAK,QAAQ;AAAE,UAAGC,GAAE,oBAAkB,CAACD,GAAE;AAAO,WAAK,eAAe;AAAE,YAAMI,KAAE,KAAK,eAAe;AAAE,WAAK,SAAS,aAAa,oBAAmBA,GAAE,aAAa,IAAI,CAAC;AAAE,YAAK,EAAC,WAAUF,GAAC,IAAE,KAAK;AAAQ,UAAG,KAAK,SAAS,cAAc,gBAAgB,SAAS,KAAK,GAAG,MAAIA,GAAE,OAAOE,EAAC,GAAE,EAAE,QAAQ,KAAK,UAAS,KAAK,YAAY,UAAU,UAAU,CAAC,IAAG,KAAK,UAAQ,KAAK,cAAcA,EAAC,GAAEA,GAAE,UAAU,IAAI,EAAE,GAAE,kBAAiB,SAAS,gBAAgB,YAAUH,MAAI,CAAC,EAAE,OAAO,GAAG,SAAS,KAAK,QAAQ,EAAE,GAAE,GAAGA,IAAE,aAAY,CAAC;AAAE,WAAK,eAAgB,MAAI;AAAC,UAAE,QAAQ,KAAK,UAAS,KAAK,YAAY,UAAU,OAAO,CAAC,GAAE,UAAK,KAAK,cAAY,KAAK,OAAO,GAAE,KAAK,aAAW;AAAA,MAAE,GAAG,KAAK,KAAI,KAAK,YAAY,CAAC;AAAA,IAAC;AAAA,IAAC,OAAM;AAAC,UAAG,KAAK,SAAS,KAAG,CAAC,EAAE,QAAQ,KAAK,UAAS,KAAK,YAAY,UAAU,MAAM,CAAC,EAAE,kBAAiB;AAAC,YAAG,KAAK,eAAe,EAAE,UAAU,OAAO,EAAE,GAAE,kBAAiB,SAAS,gBAAgB,YAAUA,MAAI,CAAC,EAAE,OAAO,GAAG,SAAS,KAAK,QAAQ,EAAE,GAAE,IAAIA,IAAE,aAAY,CAAC;AAAE,aAAK,eAAe,QAAM,OAAG,KAAK,eAAe,EAAE,IAAE,OAAG,KAAK,eAAe,EAAE,IAAE,OAAG,KAAK,aAAW,MAAK,KAAK,eAAgB,MAAI;AAAC,eAAK,qBAAqB,MAAI,KAAK,cAAY,KAAK,eAAe,GAAE,KAAK,SAAS,gBAAgB,kBAAkB,GAAE,EAAE,QAAQ,KAAK,UAAS,KAAK,YAAY,UAAU,QAAQ,CAAC;AAAA,QAAE,GAAG,KAAK,KAAI,KAAK,YAAY,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,IAAC,SAAQ;AAAC,WAAK,WAAS,KAAK,QAAQ,OAAO;AAAA,IAAC;AAAA,IAAC,iBAAgB;AAAC,aAAO,QAAQ,KAAK,UAAU,CAAC;AAAA,IAAC;AAAA,IAAC,iBAAgB;AAAC,aAAO,KAAK,QAAM,KAAK,MAAI,KAAK,kBAAkB,KAAK,eAAa,KAAK,uBAAuB,CAAC,IAAG,KAAK;AAAA,IAAG;AAAA,IAAC,kBAAkBA,IAAE;AAAC,YAAMD,KAAE,KAAK,oBAAoBC,EAAC,EAAE,OAAO;AAAE,UAAG,CAACD,GAAE,QAAO;AAAK,MAAAA,GAAE,UAAU,OAAO,IAAG,EAAE,GAAEA,GAAE,UAAU,IAAI,MAAM,KAAK,YAAY,IAAI,OAAO;AAAE,YAAMI,MAAG,CAAAH,OAAG;AAAC,WAAE;AAAC,UAAAA,MAAG,KAAK,MAAM,MAAI,KAAK,OAAO,CAAC;AAAA,QAAC,SAAO,SAAS,eAAeA,EAAC;AAAG,eAAOA;AAAA,MAAC,GAAG,KAAK,YAAY,IAAI,EAAE,SAAS;AAAE,aAAOD,GAAE,aAAa,MAAKI,EAAC,GAAE,KAAK,YAAY,KAAGJ,GAAE,UAAU,IAAI,EAAE,GAAEA;AAAA,IAAC;AAAA,IAAC,WAAWC,IAAE;AAAC,WAAK,cAAYA,IAAE,KAAK,SAAS,MAAI,KAAK,eAAe,GAAE,KAAK,KAAK;AAAA,IAAE;AAAA,IAAC,oBAAoBA,IAAE;AAAC,aAAO,KAAK,mBAAiB,KAAK,iBAAiB,cAAcA,EAAC,IAAE,KAAK,mBAAiB,IAAI,GAAG,EAAC,GAAG,KAAK,SAAQ,SAAQA,IAAE,YAAW,KAAK,yBAAyB,KAAK,QAAQ,WAAW,EAAC,CAAC,GAAE,KAAK;AAAA,IAAgB;AAAA,IAAC,yBAAwB;AAAC,aAAM,EAAC,CAAC,EAAE,GAAE,KAAK,UAAU,EAAC;AAAA,IAAC;AAAA,IAAC,YAAW;AAAC,aAAO,KAAK,yBAAyB,KAAK,QAAQ,KAAK,KAAG,KAAK,SAAS,aAAa,wBAAwB;AAAA,IAAC;AAAA,IAAC,6BAA6BA,IAAE;AAAC,aAAO,KAAK,YAAY,oBAAoBA,GAAE,gBAAe,KAAK,mBAAmB,CAAC;AAAA,IAAC;AAAA,IAAC,cAAa;AAAC,aAAO,KAAK,QAAQ,aAAW,KAAK,OAAK,KAAK,IAAI,UAAU,SAAS,EAAE;AAAA,IAAC;AAAA,IAAC,WAAU;AAAC,aAAO,KAAK,OAAK,KAAK,IAAI,UAAU,SAAS,EAAE;AAAA,IAAC;AAAA,IAAC,cAAcA,IAAE;AAAC,YAAMD,KAAE,EAAE,KAAK,QAAQ,WAAU,CAAC,MAAKC,IAAE,KAAK,QAAQ,CAAC,GAAEG,KAAE,GAAGJ,GAAE,YAAY,CAAC;AAAE,aAAO,GAAG,KAAK,UAASC,IAAE,KAAK,iBAAiBG,EAAC,CAAC;AAAA,IAAC;AAAA,IAAC,aAAY;AAAC,YAAK,EAAC,QAAOH,GAAC,IAAE,KAAK;AAAQ,aAAM,YAAU,OAAOA,KAAEA,GAAE,MAAM,GAAG,EAAE,IAAK,CAAAA,OAAG,OAAO,SAASA,IAAE,EAAE,CAAE,IAAE,cAAY,OAAOA,KAAE,CAAAD,OAAGC,GAAED,IAAE,KAAK,QAAQ,IAAEC;AAAA,IAAC;AAAA,IAAC,yBAAyBA,IAAE;AAAC,aAAO,EAAEA,IAAE,CAAC,KAAK,UAAS,KAAK,QAAQ,CAAC;AAAA,IAAC;AAAA,IAAC,iBAAiBA,IAAE;AAAC,YAAMD,KAAE,EAAC,WAAUC,IAAE,WAAU,CAAC,EAAC,MAAK,QAAO,SAAQ,EAAC,oBAAmB,KAAK,QAAQ,mBAAkB,EAAC,GAAE,EAAC,MAAK,UAAS,SAAQ,EAAC,QAAO,KAAK,WAAW,EAAC,EAAC,GAAE,EAAC,MAAK,mBAAkB,SAAQ,EAAC,UAAS,KAAK,QAAQ,SAAQ,EAAC,GAAE,EAAC,MAAK,SAAQ,SAAQ,EAAC,SAAQ,IAAI,KAAK,YAAY,IAAI,SAAQ,EAAC,GAAE,EAAC,MAAK,mBAAkB,SAAQ,MAAG,OAAM,cAAa,IAAG,CAAAA,OAAG;AAAC,aAAK,eAAe,EAAE,aAAa,yBAAwBA,GAAE,MAAM,SAAS;AAAA,MAAC,EAAC,CAAC,EAAC;AAAE,aAAM,EAAC,GAAGD,IAAE,GAAG,EAAE,KAAK,QAAQ,cAAa,CAAC,QAAOA,EAAC,CAAC,EAAC;AAAA,IAAC;AAAA,IAAC,gBAAe;AAAC,YAAMC,KAAE,KAAK,QAAQ,QAAQ,MAAM,GAAG;AAAE,iBAAUD,MAAKC,GAAE,KAAG,YAAUD,GAAE,GAAE,GAAG,KAAK,UAAS,KAAK,YAAY,UAAU,OAAO,GAAE,KAAK,QAAQ,UAAU,CAAAC,OAAG;AAAC,aAAK,6BAA6BA,EAAC,EAAE,OAAO;AAAA,MAAC,CAAE;AAAA,eAAU,aAAWD,IAAE;AAAC,cAAMC,KAAED,OAAI,KAAG,KAAK,YAAY,UAAU,YAAY,IAAE,KAAK,YAAY,UAAU,SAAS,GAAEI,KAAEJ,OAAI,KAAG,KAAK,YAAY,UAAU,YAAY,IAAE,KAAK,YAAY,UAAU,UAAU;AAAE,UAAE,GAAG,KAAK,UAASC,IAAE,KAAK,QAAQ,UAAU,CAAAA,OAAG;AAAC,gBAAMD,KAAE,KAAK,6BAA6BC,EAAC;AAAE,UAAAD,GAAE,eAAe,cAAYC,GAAE,OAAK,KAAG,EAAE,IAAE,MAAGD,GAAE,OAAO;AAAA,QAAC,CAAE,GAAE,EAAE,GAAG,KAAK,UAASI,IAAE,KAAK,QAAQ,UAAU,CAAAH,OAAG;AAAC,gBAAMD,KAAE,KAAK,6BAA6BC,EAAC;AAAE,UAAAD,GAAE,eAAe,eAAaC,GAAE,OAAK,KAAG,EAAE,IAAED,GAAE,SAAS,SAASC,GAAE,aAAa,GAAED,GAAE,OAAO;AAAA,QAAC,CAAE;AAAA,MAAC;AAAC,WAAK,oBAAkB,MAAI;AAAC,aAAK,YAAU,KAAK,KAAK;AAAA,MAAC,GAAE,EAAE,GAAG,KAAK,SAAS,QAAQ,EAAE,GAAE,IAAG,KAAK,iBAAiB;AAAA,IAAC;AAAA,IAAC,YAAW;AAAC,YAAMC,KAAE,KAAK,SAAS,aAAa,OAAO;AAAE,MAAAA,OAAI,KAAK,SAAS,aAAa,YAAY,KAAG,KAAK,SAAS,YAAY,KAAK,KAAG,KAAK,SAAS,aAAa,cAAaA,EAAC,GAAE,KAAK,SAAS,aAAa,0BAAyBA,EAAC,GAAE,KAAK,SAAS,gBAAgB,OAAO;AAAA,IAAE;AAAA,IAAC,SAAQ;AAAC,WAAK,SAAS,KAAG,KAAK,aAAW,KAAK,aAAW,QAAI,KAAK,aAAW,MAAG,KAAK,YAAa,MAAI;AAAC,aAAK,cAAY,KAAK,KAAK;AAAA,MAAC,GAAG,KAAK,QAAQ,MAAM,IAAI;AAAA,IAAE;AAAA,IAAC,SAAQ;AAAC,WAAK,qBAAqB,MAAI,KAAK,aAAW,OAAG,KAAK,YAAa,MAAI;AAAC,aAAK,cAAY,KAAK,KAAK;AAAA,MAAC,GAAG,KAAK,QAAQ,MAAM,IAAI;AAAA,IAAE;AAAA,IAAC,YAAYA,IAAED,IAAE;AAAC,mBAAa,KAAK,QAAQ,GAAE,KAAK,WAAS,WAAWC,IAAED,EAAC;AAAA,IAAC;AAAA,IAAC,uBAAsB;AAAC,aAAO,OAAO,OAAO,KAAK,cAAc,EAAE,SAAS,IAAE;AAAA,IAAC;AAAA,IAAC,WAAWC,IAAE;AAAC,YAAMD,KAAE,EAAE,kBAAkB,KAAK,QAAQ;AAAE,iBAAUC,MAAK,OAAO,KAAKD,EAAC,EAAE,IAAG,IAAIC,EAAC,KAAG,OAAOD,GAAEC,EAAC;AAAE,aAAOA,KAAE,EAAC,GAAGD,IAAE,GAAG,YAAU,OAAOC,MAAGA,KAAEA,KAAE,CAAC,EAAC,GAAEA,KAAE,KAAK,gBAAgBA,EAAC,GAAEA,KAAE,KAAK,kBAAkBA,EAAC,GAAE,KAAK,iBAAiBA,EAAC,GAAEA;AAAA,IAAC;AAAA,IAAC,kBAAkBA,IAAE;AAAC,aAAOA,GAAE,YAAU,UAAKA,GAAE,YAAU,SAAS,OAAK,EAAEA,GAAE,SAAS,GAAE,YAAU,OAAOA,GAAE,UAAQA,GAAE,QAAM,EAAC,MAAKA,GAAE,OAAM,MAAKA,GAAE,MAAK,IAAG,YAAU,OAAOA,GAAE,UAAQA,GAAE,QAAMA,GAAE,MAAM,SAAS,IAAG,YAAU,OAAOA,GAAE,YAAUA,GAAE,UAAQA,GAAE,QAAQ,SAAS,IAAGA;AAAA,IAAC;AAAA,IAAC,qBAAoB;AAAC,YAAMA,KAAE,CAAC;AAAE,iBAAS,CAACD,IAAEI,EAAC,KAAI,OAAO,QAAQ,KAAK,OAAO,EAAE,MAAK,YAAY,QAAQJ,EAAC,MAAII,OAAIH,GAAED,EAAC,IAAEI;AAAG,aAAOH,GAAE,WAAS,OAAGA,GAAE,UAAQ,UAASA;AAAA,IAAC;AAAA,IAAC,iBAAgB;AAAC,WAAK,YAAU,KAAK,QAAQ,QAAQ,GAAE,KAAK,UAAQ,OAAM,KAAK,QAAM,KAAK,IAAI,OAAO,GAAE,KAAK,MAAI;AAAA,IAAK;AAAA,IAAC,OAAO,gBAAgBA,IAAE;AAAC,aAAO,KAAK,KAAM,WAAU;AAAC,cAAMD,KAAE,GAAG,oBAAoB,MAAKC,EAAC;AAAE,YAAG,YAAU,OAAOA,IAAE;AAAC,cAAG,WAASD,GAAEC,EAAC,EAAE,OAAM,IAAI,UAAU,oBAAoBA,EAAC,GAAG;AAAE,UAAAD,GAAEC,EAAC,EAAE;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAC,IAAE,EAAE;AAAE,QAAM,KAAG,mBAAkB,KAAG,iBAAgB,KAAG,EAAC,GAAG,GAAG,SAAQ,SAAQ,IAAG,QAAO,CAAC,GAAE,CAAC,GAAE,WAAU,SAAQ,UAAS,+IAA8I,SAAQ,QAAO,GAAE,KAAG,EAAC,GAAG,GAAG,aAAY,SAAQ,iCAAgC;AAAA,EAAE,MAAM,WAAW,GAAE;AAAA,IAAC,WAAW,UAAS;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,cAAa;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAS;AAAA,IAAC,iBAAgB;AAAC,aAAO,KAAK,UAAU,KAAG,KAAK,YAAY;AAAA,IAAC;AAAA,IAAC,yBAAwB;AAAC,aAAM,EAAC,CAAC,EAAE,GAAE,KAAK,UAAU,GAAE,CAAC,EAAE,GAAE,KAAK,YAAY,EAAC;AAAA,IAAC;AAAA,IAAC,cAAa;AAAC,aAAO,KAAK,yBAAyB,KAAK,QAAQ,OAAO;AAAA,IAAC;AAAA,IAAC,OAAO,gBAAgBA,IAAE;AAAC,aAAO,KAAK,KAAM,WAAU;AAAC,cAAMD,KAAE,GAAG,oBAAoB,MAAKC,EAAC;AAAE,YAAG,YAAU,OAAOA,IAAE;AAAC,cAAG,WAASD,GAAEC,EAAC,EAAE,OAAM,IAAI,UAAU,oBAAoBA,EAAC,GAAG;AAAE,UAAAD,GAAEC,EAAC,EAAE;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAC,IAAE,EAAE;AAAE,QAAM,KAAG,iBAAgB,KAAG,WAAW,EAAE,IAAG,KAAG,QAAQ,EAAE,IAAG,KAAG,OAAO,EAAE,aAAY,KAAG,UAAS,KAAG,UAAS,KAAG,aAAY,KAAG,GAAG,EAAE,iBAAiB,EAAE,sBAAqB,KAAG,EAAC,QAAO,MAAK,YAAW,gBAAe,cAAa,OAAG,QAAO,MAAK,WAAU,CAAC,KAAG,KAAG,CAAC,EAAC,GAAE,KAAG,EAAC,QAAO,iBAAgB,YAAW,UAAS,cAAa,WAAU,QAAO,WAAU,WAAU,QAAO;AAAA,EAAE,MAAM,WAAW,EAAC;AAAA,IAAC,YAAYA,IAAED,IAAE;AAAC,YAAMC,IAAED,EAAC,GAAE,KAAK,eAAa,oBAAI,OAAI,KAAK,sBAAoB,oBAAI,OAAI,KAAK,eAAa,cAAY,iBAAiB,KAAK,QAAQ,EAAE,YAAU,OAAK,KAAK,UAAS,KAAK,gBAAc,MAAK,KAAK,YAAU,MAAK,KAAK,sBAAoB,EAAC,iBAAgB,GAAE,iBAAgB,EAAC,GAAE,KAAK,QAAQ;AAAA,IAAC;AAAA,IAAC,WAAW,UAAS;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,cAAa;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAW;AAAA,IAAC,UAAS;AAAC,WAAK,iCAAiC,GAAE,KAAK,yBAAyB,GAAE,KAAK,YAAU,KAAK,UAAU,WAAW,IAAE,KAAK,YAAU,KAAK,gBAAgB;AAAE,iBAAUC,MAAK,KAAK,oBAAoB,OAAO,EAAE,MAAK,UAAU,QAAQA,EAAC;AAAA,IAAC;AAAA,IAAC,UAAS;AAAC,WAAK,UAAU,WAAW,GAAE,MAAM,QAAQ;AAAA,IAAC;AAAA,IAAC,kBAAkBA,IAAE;AAAC,aAAOA,GAAE,SAAO,EAAEA,GAAE,MAAM,KAAG,SAAS,MAAKA,GAAE,aAAWA,GAAE,SAAO,GAAGA,GAAE,MAAM,gBAAcA,GAAE,YAAW,YAAU,OAAOA,GAAE,cAAYA,GAAE,YAAUA,GAAE,UAAU,MAAM,GAAG,EAAE,IAAK,CAAAA,OAAG,OAAO,WAAWA,EAAC,CAAE,IAAGA;AAAA,IAAC;AAAA,IAAC,2BAA0B;AAAC,WAAK,QAAQ,iBAAe,EAAE,IAAI,KAAK,QAAQ,QAAO,EAAE,GAAE,EAAE,GAAG,KAAK,QAAQ,QAAO,IAAG,IAAI,CAAAA,OAAG;AAAC,cAAMD,KAAE,KAAK,oBAAoB,IAAIC,GAAE,OAAO,IAAI;AAAE,YAAGD,IAAE;AAAC,UAAAC,GAAE,eAAe;AAAE,gBAAMG,KAAE,KAAK,gBAAc,QAAOF,KAAEF,GAAE,YAAU,KAAK,SAAS;AAAU,cAAGI,GAAE,SAAS,QAAO,KAAKA,GAAE,SAAS,EAAC,KAAIF,IAAE,UAAS,SAAQ,CAAC;AAAE,UAAAE,GAAE,YAAUF;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAE;AAAA,IAAC,kBAAiB;AAAC,YAAMD,KAAE,EAAC,MAAK,KAAK,cAAa,WAAU,KAAK,QAAQ,WAAU,YAAW,KAAK,QAAQ,WAAU;AAAE,aAAO,IAAI,qBAAsB,CAAAA,OAAG,KAAK,kBAAkBA,EAAC,GAAGA,EAAC;AAAA,IAAC;AAAA,IAAC,kBAAkBA,IAAE;AAAC,YAAMD,KAAE,CAAAC,OAAG,KAAK,aAAa,IAAI,IAAIA,GAAE,OAAO,EAAE,EAAE,GAAEG,KAAE,CAAAH,OAAG;AAAC,aAAK,oBAAoB,kBAAgBA,GAAE,OAAO,WAAU,KAAK,SAASD,GAAEC,EAAC,CAAC;AAAA,MAAC,GAAEC,MAAG,KAAK,gBAAc,SAAS,iBAAiB,WAAUK,KAAEL,MAAG,KAAK,oBAAoB;AAAgB,WAAK,oBAAoB,kBAAgBA;AAAE,iBAAUG,MAAKJ,IAAE;AAAC,YAAG,CAACI,GAAE,gBAAe;AAAC,eAAK,gBAAc,MAAK,KAAK,kBAAkBL,GAAEK,EAAC,CAAC;AAAE;AAAA,QAAQ;AAAC,cAAMJ,KAAEI,GAAE,OAAO,aAAW,KAAK,oBAAoB;AAAgB,YAAGE,MAAGN,IAAE;AAAC,cAAGG,GAAEC,EAAC,GAAE,CAACH,GAAE;AAAA,QAAM,MAAM,CAAAK,MAAGN,MAAGG,GAAEC,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,IAAC,mCAAkC;AAAC,WAAK,eAAa,oBAAI,OAAI,KAAK,sBAAoB,oBAAI;AAAI,YAAMJ,KAAE,EAAE,KAAK,IAAG,KAAK,QAAQ,MAAM;AAAE,iBAAUD,MAAKC,IAAE;AAAC,YAAG,CAACD,GAAE,QAAM,EAAEA,EAAC,EAAE;AAAS,cAAMC,KAAE,EAAE,QAAQ,UAAUD,GAAE,IAAI,GAAE,KAAK,QAAQ;AAAE,UAAEC,EAAC,MAAI,KAAK,aAAa,IAAI,UAAUD,GAAE,IAAI,GAAEA,EAAC,GAAE,KAAK,oBAAoB,IAAIA,GAAE,MAAKC,EAAC;AAAA,MAAE;AAAA,IAAC;AAAA,IAAC,SAASA,IAAE;AAAC,WAAK,kBAAgBA,OAAI,KAAK,kBAAkB,KAAK,QAAQ,MAAM,GAAE,KAAK,gBAAcA,IAAEA,GAAE,UAAU,IAAI,EAAE,GAAE,KAAK,iBAAiBA,EAAC,GAAE,EAAE,QAAQ,KAAK,UAAS,IAAG,EAAC,eAAcA,GAAC,CAAC;AAAA,IAAE;AAAA,IAAC,iBAAiBA,IAAE;AAAC,UAAGA,GAAE,UAAU,SAAS,eAAe,EAAE,GAAE,QAAQ,oBAAmBA,GAAE,QAAQ,WAAW,CAAC,EAAE,UAAU,IAAI,EAAE;AAAA,UAAO,YAAUD,MAAK,EAAE,QAAQC,IAAE,mBAAmB,EAAE,YAAUA,MAAK,EAAE,KAAKD,IAAE,EAAE,EAAE,CAAAC,GAAE,UAAU,IAAI,EAAE;AAAA,IAAC;AAAA,IAAC,kBAAkBA,IAAE;AAAC,MAAAA,GAAE,UAAU,OAAO,EAAE;AAAE,YAAMD,KAAE,EAAE,KAAK,GAAG,EAAE,IAAI,EAAE,IAAGC,EAAC;AAAE,iBAAUA,MAAKD,GAAE,CAAAC,GAAE,UAAU,OAAO,EAAE;AAAA,IAAC;AAAA,IAAC,OAAO,gBAAgBA,IAAE;AAAC,aAAO,KAAK,KAAM,WAAU;AAAC,cAAMD,KAAE,GAAG,oBAAoB,MAAKC,EAAC;AAAE,YAAG,YAAU,OAAOA,IAAE;AAAC,cAAG,WAASD,GAAEC,EAAC,KAAGA,GAAE,WAAW,GAAG,KAAG,kBAAgBA,GAAE,OAAM,IAAI,UAAU,oBAAoBA,EAAC,GAAG;AAAE,UAAAD,GAAEC,EAAC,EAAE;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAC,IAAE,GAAG,QAAO,IAAI,MAAI;AAAC,eAAUA,MAAK,EAAE,KAAK,wBAAwB,EAAE,IAAG,oBAAoBA,EAAC;AAAA,EAAC,CAAE,GAAE,EAAE,EAAE;AAAE,QAAM,KAAG,WAAU,KAAG,OAAO,EAAE,IAAG,KAAG,SAAS,EAAE,IAAG,KAAG,OAAO,EAAE,IAAG,KAAG,QAAQ,EAAE,IAAG,KAAG,QAAQ,EAAE,IAAG,KAAG,UAAU,EAAE,IAAG,KAAG,OAAO,EAAE,IAAG,KAAG,aAAY,KAAG,cAAa,KAAG,WAAU,KAAG,aAAY,KAAG,QAAO,KAAG,OAAM,KAAG,UAAS,KAAG,QAAO,KAAG,QAAO,KAAG,oBAAmB,KAAG,QAAQ,EAAE,KAAI,KAAG,4EAA2E,KAAG,YAAY,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,KAAK,EAAE,IAAG,KAAG,IAAI,EAAE,4BAA4B,EAAE,6BAA6B,EAAE;AAAA,EAA0B,MAAM,WAAW,EAAC;AAAA,IAAC,YAAYA,IAAE;AAAC,YAAMA,EAAC,GAAE,KAAK,UAAQ,KAAK,SAAS,QAAQ,qCAAqC,GAAE,KAAK,YAAU,KAAK,sBAAsB,KAAK,SAAQ,KAAK,aAAa,CAAC,GAAE,EAAE,GAAG,KAAK,UAAS,IAAI,CAAAA,OAAG,KAAK,SAASA,EAAC,CAAE;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAK;AAAA,IAAC,OAAM;AAAC,YAAMA,KAAE,KAAK;AAAS,UAAG,KAAK,cAAcA,EAAC,EAAE;AAAO,YAAMD,KAAE,KAAK,eAAe,GAAEI,KAAEJ,KAAE,EAAE,QAAQA,IAAE,IAAG,EAAC,eAAcC,GAAC,CAAC,IAAE;AAAK,QAAE,QAAQA,IAAE,IAAG,EAAC,eAAcD,GAAC,CAAC,EAAE,oBAAkBI,MAAGA,GAAE,qBAAmB,KAAK,YAAYJ,IAAEC,EAAC,GAAE,KAAK,UAAUA,IAAED,EAAC;AAAA,IAAE;AAAA,IAAC,UAAUC,IAAED,IAAE;AAAC,MAAAC,OAAIA,GAAE,UAAU,IAAI,EAAE,GAAE,KAAK,UAAU,EAAE,uBAAuBA,EAAC,CAAC,GAAE,KAAK,eAAgB,MAAI;AAAC,kBAAQA,GAAE,aAAa,MAAM,KAAGA,GAAE,gBAAgB,UAAU,GAAEA,GAAE,aAAa,iBAAgB,IAAE,GAAE,KAAK,gBAAgBA,IAAE,IAAE,GAAE,EAAE,QAAQA,IAAE,IAAG,EAAC,eAAcD,GAAC,CAAC,KAAGC,GAAE,UAAU,IAAI,EAAE;AAAA,MAAC,GAAGA,IAAEA,GAAE,UAAU,SAAS,EAAE,CAAC;AAAA,IAAE;AAAA,IAAC,YAAYA,IAAED,IAAE;AAAC,MAAAC,OAAIA,GAAE,UAAU,OAAO,EAAE,GAAEA,GAAE,KAAK,GAAE,KAAK,YAAY,EAAE,uBAAuBA,EAAC,CAAC,GAAE,KAAK,eAAgB,MAAI;AAAC,kBAAQA,GAAE,aAAa,MAAM,KAAGA,GAAE,aAAa,iBAAgB,KAAE,GAAEA,GAAE,aAAa,YAAW,IAAI,GAAE,KAAK,gBAAgBA,IAAE,KAAE,GAAE,EAAE,QAAQA,IAAE,IAAG,EAAC,eAAcD,GAAC,CAAC,KAAGC,GAAE,UAAU,OAAO,EAAE;AAAA,MAAC,GAAGA,IAAEA,GAAE,UAAU,SAAS,EAAE,CAAC;AAAA,IAAE;AAAA,IAAC,SAASA,IAAE;AAAC,UAAG,CAAC,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,EAAE,SAASA,GAAE,GAAG,EAAE;AAAO,MAAAA,GAAE,gBAAgB,GAAEA,GAAE,eAAe;AAAE,YAAMD,KAAE,KAAK,aAAa,EAAE,OAAQ,CAAAC,OAAG,CAAC,EAAEA,EAAC,CAAE;AAAE,UAAIG;AAAE,UAAG,CAAC,IAAG,EAAE,EAAE,SAASH,GAAE,GAAG,EAAE,CAAAG,KAAEJ,GAAEC,GAAE,QAAM,KAAG,IAAED,GAAE,SAAO,CAAC;AAAA,WAAM;AAAC,cAAME,KAAE,CAAC,IAAG,EAAE,EAAE,SAASD,GAAE,GAAG;AAAE,QAAAG,KAAE,EAAEJ,IAAEC,GAAE,QAAOC,IAAE,IAAE;AAAA,MAAC;AAAC,MAAAE,OAAIA,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC,GAAE,GAAG,oBAAoBA,EAAC,EAAE,KAAK;AAAA,IAAE;AAAA,IAAC,eAAc;AAAC,aAAO,EAAE,KAAK,IAAG,KAAK,OAAO;AAAA,IAAC;AAAA,IAAC,iBAAgB;AAAC,aAAO,KAAK,aAAa,EAAE,KAAM,CAAAH,OAAG,KAAK,cAAcA,EAAC,CAAE,KAAG;AAAA,IAAI;AAAA,IAAC,sBAAsBA,IAAED,IAAE;AAAC,WAAK,yBAAyBC,IAAE,QAAO,SAAS;AAAE,iBAAUA,MAAKD,GAAE,MAAK,6BAA6BC,EAAC;AAAA,IAAC;AAAA,IAAC,6BAA6BA,IAAE;AAAC,MAAAA,KAAE,KAAK,iBAAiBA,EAAC;AAAE,YAAMD,KAAE,KAAK,cAAcC,EAAC,GAAEG,KAAE,KAAK,iBAAiBH,EAAC;AAAE,MAAAA,GAAE,aAAa,iBAAgBD,EAAC,GAAEI,OAAIH,MAAG,KAAK,yBAAyBG,IAAE,QAAO,cAAc,GAAEJ,MAAGC,GAAE,aAAa,YAAW,IAAI,GAAE,KAAK,yBAAyBA,IAAE,QAAO,KAAK,GAAE,KAAK,mCAAmCA,EAAC;AAAA,IAAC;AAAA,IAAC,mCAAmCA,IAAE;AAAC,YAAMD,KAAE,EAAE,uBAAuBC,EAAC;AAAE,MAAAD,OAAI,KAAK,yBAAyBA,IAAE,QAAO,UAAU,GAAEC,GAAE,MAAI,KAAK,yBAAyBD,IAAE,mBAAkB,GAAGC,GAAE,EAAE,EAAE;AAAA,IAAE;AAAA,IAAC,gBAAgBA,IAAED,IAAE;AAAC,YAAMI,KAAE,KAAK,iBAAiBH,EAAC;AAAE,UAAG,CAACG,GAAE,UAAU,SAAS,UAAU,EAAE;AAAO,YAAMF,KAAE,CAACD,IAAEC,OAAI;AAAC,cAAMK,KAAE,EAAE,QAAQN,IAAEG,EAAC;AAAE,QAAAG,MAAGA,GAAE,UAAU,OAAOL,IAAEF,EAAC;AAAA,MAAC;AAAE,MAAAE,GAAE,IAAG,EAAE,GAAEA,GAAE,kBAAiB,EAAE,GAAEE,GAAE,aAAa,iBAAgBJ,EAAC;AAAA,IAAC;AAAA,IAAC,yBAAyBC,IAAED,IAAEI,IAAE;AAAC,MAAAH,GAAE,aAAaD,EAAC,KAAGC,GAAE,aAAaD,IAAEI,EAAC;AAAA,IAAC;AAAA,IAAC,cAAcH,IAAE;AAAC,aAAOA,GAAE,UAAU,SAAS,EAAE;AAAA,IAAC;AAAA,IAAC,iBAAiBA,IAAE;AAAC,aAAOA,GAAE,QAAQ,EAAE,IAAEA,KAAE,EAAE,QAAQ,IAAGA,EAAC;AAAA,IAAC;AAAA,IAAC,iBAAiBA,IAAE;AAAC,aAAOA,GAAE,QAAQ,6BAA6B,KAAGA;AAAA,IAAC;AAAA,IAAC,OAAO,gBAAgBA,IAAE;AAAC,aAAO,KAAK,KAAM,WAAU;AAAC,cAAMD,KAAE,GAAG,oBAAoB,IAAI;AAAE,YAAG,YAAU,OAAOC,IAAE;AAAC,cAAG,WAASD,GAAEC,EAAC,KAAGA,GAAE,WAAW,GAAG,KAAG,kBAAgBA,GAAE,OAAM,IAAI,UAAU,oBAAoBA,EAAC,GAAG;AAAE,UAAAD,GAAEC,EAAC,EAAE;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAC,IAAE,GAAG,UAAS,IAAG,IAAI,SAASA,IAAE;AAAC,KAAC,KAAI,MAAM,EAAE,SAAS,KAAK,OAAO,KAAGA,GAAE,eAAe,GAAE,EAAE,IAAI,KAAG,GAAG,oBAAoB,IAAI,EAAE,KAAK;AAAA,EAAC,CAAE,GAAE,EAAE,GAAG,QAAO,IAAI,MAAI;AAAC,eAAUA,MAAK,EAAE,KAAK,EAAE,EAAE,IAAG,oBAAoBA,EAAC;AAAA,EAAC,CAAE,GAAE,EAAE,EAAE;AAAE,QAAM,KAAG,aAAY,KAAG,YAAY,EAAE,IAAG,KAAG,WAAW,EAAE,IAAG,KAAG,UAAU,EAAE,IAAG,KAAG,WAAW,EAAE,IAAG,KAAG,OAAO,EAAE,IAAG,KAAG,SAAS,EAAE,IAAG,KAAG,OAAO,EAAE,IAAG,KAAG,QAAQ,EAAE,IAAG,KAAG,QAAO,KAAG,QAAO,KAAG,WAAU,KAAG,EAAC,WAAU,WAAU,UAAS,WAAU,OAAM,SAAQ,GAAE,KAAG,EAAC,WAAU,MAAG,UAAS,MAAG,OAAM,IAAG;AAAA,EAAE,MAAM,WAAW,EAAC;AAAA,IAAC,YAAYA,IAAED,IAAE;AAAC,YAAMC,IAAED,EAAC,GAAE,KAAK,WAAS,MAAK,KAAK,uBAAqB,OAAG,KAAK,0BAAwB,OAAG,KAAK,cAAc;AAAA,IAAC;AAAA,IAAC,WAAW,UAAS;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,cAAa;AAAC,aAAO;AAAA,IAAE;AAAA,IAAC,WAAW,OAAM;AAAC,aAAM;AAAA,IAAO;AAAA,IAAC,OAAM;AAAC,QAAE,QAAQ,KAAK,UAAS,EAAE,EAAE,qBAAmB,KAAK,cAAc,GAAE,KAAK,QAAQ,aAAW,KAAK,SAAS,UAAU,IAAI,MAAM,GAAE,KAAK,SAAS,UAAU,OAAO,EAAE,GAAE,EAAE,KAAK,QAAQ,GAAE,KAAK,SAAS,UAAU,IAAI,IAAG,EAAE,GAAE,KAAK,eAAgB,MAAI;AAAC,aAAK,SAAS,UAAU,OAAO,EAAE,GAAE,EAAE,QAAQ,KAAK,UAAS,EAAE,GAAE,KAAK,mBAAmB;AAAA,MAAC,GAAG,KAAK,UAAS,KAAK,QAAQ,SAAS;AAAA,IAAE;AAAA,IAAC,OAAM;AAAC,WAAK,QAAQ,MAAI,EAAE,QAAQ,KAAK,UAAS,EAAE,EAAE,qBAAmB,KAAK,SAAS,UAAU,IAAI,EAAE,GAAE,KAAK,eAAgB,MAAI;AAAC,aAAK,SAAS,UAAU,IAAI,EAAE,GAAE,KAAK,SAAS,UAAU,OAAO,IAAG,EAAE,GAAE,EAAE,QAAQ,KAAK,UAAS,EAAE;AAAA,MAAC,GAAG,KAAK,UAAS,KAAK,QAAQ,SAAS;AAAA,IAAG;AAAA,IAAC,UAAS;AAAC,WAAK,cAAc,GAAE,KAAK,QAAQ,KAAG,KAAK,SAAS,UAAU,OAAO,EAAE,GAAE,MAAM,QAAQ;AAAA,IAAC;AAAA,IAAC,UAAS;AAAC,aAAO,KAAK,SAAS,UAAU,SAAS,EAAE;AAAA,IAAC;AAAA,IAAC,qBAAoB;AAAC,WAAK,QAAQ,aAAW,KAAK,wBAAsB,KAAK,4BAA0B,KAAK,WAAS,WAAY,MAAI;AAAC,aAAK,KAAK;AAAA,MAAC,GAAG,KAAK,QAAQ,KAAK;AAAA,IAAG;AAAA,IAAC,eAAeC,IAAED,IAAE;AAAC,cAAOC,GAAE,MAAK;AAAA,QAAC,KAAI;AAAA,QAAY,KAAI;AAAW,eAAK,uBAAqBD;AAAE;AAAA,QAAM,KAAI;AAAA,QAAU,KAAI;AAAW,eAAK,0BAAwBA;AAAA,MAAC;AAAC,UAAGA,GAAE,QAAO,KAAK,KAAK,cAAc;AAAE,YAAMI,KAAEH,GAAE;AAAc,WAAK,aAAWG,MAAG,KAAK,SAAS,SAASA,EAAC,KAAG,KAAK,mBAAmB;AAAA,IAAC;AAAA,IAAC,gBAAe;AAAC,QAAE,GAAG,KAAK,UAAS,IAAI,CAAAH,OAAG,KAAK,eAAeA,IAAE,IAAE,CAAE,GAAE,EAAE,GAAG,KAAK,UAAS,IAAI,CAAAA,OAAG,KAAK,eAAeA,IAAE,KAAE,CAAE,GAAE,EAAE,GAAG,KAAK,UAAS,IAAI,CAAAA,OAAG,KAAK,eAAeA,IAAE,IAAE,CAAE,GAAE,EAAE,GAAG,KAAK,UAAS,IAAI,CAAAA,OAAG,KAAK,eAAeA,IAAE,KAAE,CAAE;AAAA,IAAC;AAAA,IAAC,gBAAe;AAAC,mBAAa,KAAK,QAAQ,GAAE,KAAK,WAAS;AAAA,IAAI;AAAA,IAAC,OAAO,gBAAgBA,IAAE;AAAC,aAAO,KAAK,KAAM,WAAU;AAAC,cAAMD,KAAE,GAAG,oBAAoB,MAAKC,EAAC;AAAE,YAAG,YAAU,OAAOA,IAAE;AAAC,cAAG,WAASD,GAAEC,EAAC,EAAE,OAAM,IAAI,UAAU,oBAAoBA,EAAC,GAAG;AAAE,UAAAD,GAAEC,EAAC,EAAE,IAAI;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO,EAAE,EAAE,GAAE,EAAE,EAAE,GAAE,EAAC,OAAM,GAAE,QAAO,GAAE,UAAS,IAAG,UAAS,IAAG,UAAS,IAAG,OAAM,IAAG,WAAU,IAAG,SAAQ,IAAG,WAAU,IAAG,KAAI,IAAG,OAAM,IAAG,SAAQ,GAAE;AAAC,CAAE;", "names": ["e", "t", "n", "r", "i", "o", "a", "s", "u", "l", "b", "w", "T", "C", "d", "k", "S", "E", "W", "c", "h", "f", "p", "g", "v", "y", "m", "x", "j", "A", "D", "N", "q", "L", "H", "O", "P", "M", "V", "R", "J", "I", "U", "Y", "Q", "re", "F", "$", "B", "_", "z", "X", "ne", "G", "K", "Z", "ee", "te", "tt", "et", "it", "nt", "st", "ot", "rt", "at", "lt", "ct"]}