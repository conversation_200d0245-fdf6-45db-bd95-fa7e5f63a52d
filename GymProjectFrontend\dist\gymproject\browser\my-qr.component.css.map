{"version": 3, "sources": ["src/app/components/my-qr/my-qr.component.css"], "sourcesContent": ["/* QR Code Component Styles - Using project's design system */\r\n\r\n.gym-container {\r\n  max-width: 100%;\r\n  margin: auto;\r\n  padding: var(--spacing-lg);\r\n  background: linear-gradient(135deg, var(--primary), var(--primary-dark));\r\n  min-height: 100vh;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-family: 'Arial', sans-serif;\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.gym-panel {\r\n  width: 100%;\r\n  max-width: 380px;\r\n  background-color: var(--bg-primary);\r\n  border-radius: var(--border-radius-lg);\r\n  padding: var(--spacing-xl);\r\n  box-shadow: var(--shadow-lg);\r\n  display: flex;\r\n  flex-direction: column;\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n  animation: zoomIn 0.5s var(--transition-timing);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.gym-panel::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -50px;\r\n  right: -50px;\r\n  width: 100px;\r\n  height: 100px;\r\n  background: var(--primary);\r\n  opacity: 0.1;\r\n  border-radius: 50%;\r\n  z-index: 0;\r\n}\r\n\r\n.gym-panel::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -80px;\r\n  left: -80px;\r\n  width: 160px;\r\n  height: 160px;\r\n  background: var(--primary-dark);\r\n  opacity: 0.1;\r\n  border-radius: 50%;\r\n  z-index: 0;\r\n}\r\n\r\n.header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: var(--spacing-lg);\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.gym-icon {\r\n  width: 70px;\r\n  height: 70px;\r\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 32px;\r\n  color: white;\r\n  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);\r\n  margin: 0 auto;\r\n}\r\n\r\n.message-area {\r\n  padding: var(--spacing-sm) var(--spacing-md);\r\n  margin-bottom: var(--spacing-md);\r\n  border-radius: var(--border-radius-md);\r\n  font-size: 14px;\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 1;\r\n  animation: fadeIn 0.3s var(--transition-timing);\r\n}\r\n\r\n.message-area.error {\r\n  background-color: var(--danger-light);\r\n  color: var(--danger);\r\n}\r\n\r\n.error-help {\r\n  margin-top: 10px;\r\n  font-size: 12px;\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.loading-spinner {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20px 0;\r\n}\r\n\r\n.result-area {\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 1;\r\n  animation: fadeIn 0.5s var(--transition-timing);\r\n  padding: var(--spacing-md);\r\n  border-radius: var(--border-radius-md);\r\n  background-color: var(--bg-secondary);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.result-area h2 {\r\n  margin: 0 0 var(--spacing-md);\r\n  font-size: 22px;\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n}\r\n\r\n.membership-info {\r\n  margin-bottom: var(--spacing-md);\r\n}\r\n\r\n.remaining-days {\r\n  color: var(--success);\r\n  font-weight: bold;\r\n  margin: 8px 0;\r\n  padding: 8px;\r\n  border-radius: var(--border-radius-md);\r\n  background-color: var(--success-light);\r\n}\r\n\r\n.future-membership {\r\n  color: var(--warning);\r\n  font-weight: bold;\r\n  margin: 8px 0;\r\n  padding: 8px;\r\n  border-radius: var(--border-radius-md);\r\n  background-color: var(--warning-light);\r\n}\r\n\r\n.expired-membership, .expired-membership-special {\r\n  color: var(--danger);\r\n  font-weight: bold;\r\n  margin: 8px 0;\r\n  padding: 8px;\r\n  border-radius: var(--border-radius-md);\r\n  background-color: var(--danger-light);\r\n}\r\n\r\n.frozen-membership {\r\n  color: var(--primary);\r\n  font-weight: bold;\r\n  margin: 8px 0;\r\n  padding: 8px;\r\n  border-radius: var(--border-radius-md);\r\n  background-color: var(--primary-light);\r\n}\r\n\r\n.no-qr-message {\r\n  color: var(--danger);\r\n  font-weight: bold;\r\n  margin: 15px 0;\r\n  padding: 12px;\r\n  border-radius: var(--border-radius-md);\r\n  background-color: var(--danger-light);\r\n  text-align: center;\r\n}\r\n\r\n.qr-code-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin: var(--spacing-md) 0;\r\n  position: relative;\r\n  width: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.qr-validity-timer {\r\n  width: 100%;\r\n  margin-bottom: var(--spacing-md);\r\n  text-align: center;\r\n}\r\n\r\n.timer-label {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 5px;\r\n  font-size: 14px;\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.timer-countdown {\r\n  font-weight: bold;\r\n  color: var(--success);\r\n}\r\n\r\n.timer-warning {\r\n  color: var(--danger);\r\n  animation: blink 1s infinite;\r\n}\r\n\r\n@keyframes blink {\r\n  0% { opacity: 1; }\r\n  50% { opacity: 0.5; }\r\n  100% { opacity: 1; }\r\n}\r\n\r\n.progress-bar-container {\r\n  width: 100%;\r\n  height: 6px;\r\n  background-color: var(--bg-secondary);\r\n  border-radius: var(--border-radius-pill);\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-bar {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, var(--success) 0%, var(--primary) 100%);\r\n  border-radius: var(--border-radius-pill);\r\n  transition: width 1s linear;\r\n}\r\n\r\n.progress-warning {\r\n  background: linear-gradient(90deg, var(--danger) 0%, var(--warning) 100%);\r\n}\r\n\r\n.qr-code-container qrcode {\r\n  border: 2px solid var(--primary);\r\n  border-radius: var(--border-radius-md);\r\n  padding: var(--spacing-md);\r\n  background-color: white;\r\n  box-shadow: var(--shadow-md);\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n  max-width: 100%;\r\n  margin-bottom: var(--spacing-md);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.qr-code-container qrcode canvas {\r\n  max-width: 100%;\r\n  height: auto !important;\r\n  display: block;\r\n  image-rendering: -webkit-optimize-contrast; /* Improves QR code clarity on some browsers */\r\n  image-rendering: crisp-edges; /* Modern browsers */\r\n}\r\n\r\n/* QR kod hover animasyonu kaldırıldı - kullanıcı isteği üzerine */\r\n\r\n/* QR Kodunu İndir butonu kaldırıldı (QR kodları 5 dakika geçerli olduğu için)\r\n.qr-actions {\r\n  display: flex;\r\n  width: 100%;\r\n  margin-bottom: var(--spacing-md);\r\n}\r\n\r\n.download-button {\r\n  width: 100%;\r\n  padding: 14px 20px;\r\n  background: linear-gradient(135deg, var(--success) 0%, #219150 100%);\r\n  color: white;\r\n  border: none;\r\n  border-radius: var(--border-radius-pill);\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n  box-shadow: var(--shadow-sm);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 10px;\r\n}\r\n\r\n.download-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.download-button:active {\r\n  transform: translateY(1px);\r\n}\r\n*/\r\n\r\n.qr-info-text {\r\n  width: 100%;\r\n  text-align: center;\r\n  font-size: 13px;\r\n  color: var(--text-secondary);\r\n  padding: 8px;\r\n  background-color: var(--bg-secondary);\r\n  border-radius: var(--border-radius-md);\r\n  border: 1px dashed var(--border-color);\r\n}\r\n\r\n/* Special member styles - Yeni Tasarım */\r\n.special-member {\r\n  background: linear-gradient(135deg, #ff0080, #ff5db1, #ff0080);\r\n  background-size: 200% 200%;\r\n  animation: gradientBG 15s ease infinite;\r\n  padding: var(--spacing-lg);\r\n  border-radius: var(--border-radius-lg);\r\n  box-shadow: 0 0 30px rgba(255, 0, 128, 0.6);\r\n  border: 3px solid rgba(255, 255, 255, 0.8);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Arka plan animasyonu */\r\n@keyframes gradientBG {\r\n  0% { background-position: 0% 50%; }\r\n  50% { background-position: 100% 50%; }\r\n  100% { background-position: 0% 50%; }\r\n}\r\n\r\n/* Arka plan süslemeleri */\r\n.special-member::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -50px;\r\n  right: -50px;\r\n  width: 100px;\r\n  height: 100px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  z-index: 0;\r\n}\r\n\r\n.special-member::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -80px;\r\n  left: -80px;\r\n  width: 160px;\r\n  height: 160px;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  border-radius: 50%;\r\n  z-index: 0;\r\n}\r\n\r\n.special-expired-text {\r\n  background-color: rgba(255, 255, 255, 0.15);\r\n  color: #ffffff;\r\n  border: 2px solid rgba(255, 255, 255, 0.6);\r\n  font-weight: bold;\r\n  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);\r\n  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);\r\n  padding: 15px;\r\n  margin: 15px 0;\r\n  font-size: 18px;\r\n  letter-spacing: 1px;\r\n  animation: glow 2s infinite alternate;\r\n  border-radius: 12px;\r\n}\r\n\r\n@keyframes glow {\r\n  from {\r\n    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5), 0 0 10px rgba(255, 0, 128, 0.3);\r\n    text-shadow: 0 0 3px rgba(255, 255, 255, 0.5);\r\n  }\r\n  to {\r\n    box-shadow: 0 0 15px rgba(255, 255, 255, 0.7), 0 0 20px rgba(255, 0, 128, 0.5);\r\n    text-shadow: 0 0 6px rgba(255, 255, 255, 0.7);\r\n  }\r\n}\r\n\r\n.special-header {\r\n  text-align: center;\r\n  margin-bottom: var(--spacing-md);\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.special-header h2 {\r\n  color: #ffffff !important;\r\n  font-size: 32px !important;\r\n  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);\r\n  margin: 10px 0 !important;\r\n  font-weight: 700;\r\n  letter-spacing: 1px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  padding: 15px;\r\n  border-radius: 15px;\r\n  border: 2px solid rgba(255, 255, 255, 0.5);\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(5px);\r\n  -webkit-backdrop-filter: blur(5px);\r\n}\r\n\r\n.special-header h2::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n  animation: shine 3s infinite;\r\n}\r\n\r\n@keyframes shine {\r\n  0% { left: -100%; }\r\n  20% { left: 100%; }\r\n  100% { left: 100%; }\r\n}\r\n\r\n.hearts {\r\n  font-size: 30px;\r\n  margin: 10px 0;\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 15px;\r\n}\r\n\r\n.hearts span {\r\n  animation: heartbeat 1.5s infinite;\r\n  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.7));\r\n  display: inline-block;\r\n  transform-origin: center;\r\n}\r\n\r\n.hearts span:nth-child(1) {\r\n  animation-delay: 0s;\r\n}\r\n\r\n.hearts span:nth-child(2) {\r\n  animation-delay: 0.5s;\r\n}\r\n\r\n.hearts span:nth-child(3) {\r\n  animation-delay: 1s;\r\n}\r\n\r\n@keyframes heartbeat {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  15% {\r\n    transform: scale(1.3);\r\n  }\r\n  30% {\r\n    transform: scale(1);\r\n  }\r\n  45% {\r\n    transform: scale(1.3);\r\n  }\r\n  60% {\r\n    transform: scale(1);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n.special-info {\r\n  color: #ffffff !important;\r\n  font-size: 18px;\r\n  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n/* Üyelik bilgisi için özel stil */\r\n.special-member .remaining-days {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  color: #ffffff;\r\n  border: 1px solid rgba(255, 255, 255, 0.4);\r\n  font-weight: bold;\r\n  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);\r\n  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);\r\n  padding: 12px;\r\n  margin: 10px 0;\r\n  border-radius: 10px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.special-member .remaining-days:hover {\r\n  background-color: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.special-member .remaining-days strong {\r\n  color: #ffffff;\r\n  font-size: 20px;\r\n  margin: 0 5px;\r\n}\r\n\r\n.special-member .qr-code-container {\r\n  position: relative;\r\n  z-index: 1;\r\n  margin: 20px 0;\r\n}\r\n\r\n.special-member .qr-code-container qrcode {\r\n  border: 5px solid rgba(255, 255, 255, 0.8);\r\n  box-shadow: 0 0 20px rgba(255, 255, 255, 0.4);\r\n  border-radius: 20px;\r\n  padding: 20px;\r\n  transform: scale(1.05);\r\n  transition: all 0.3s ease;\r\n  background-color: white;\r\n  position: relative;\r\n}\r\n\r\n.special-member .qr-code-container qrcode::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -3px;\r\n  left: -3px;\r\n  right: -3px;\r\n  bottom: -3px;\r\n  background: linear-gradient(45deg, #ff0080, #ff5db1, #ff0080);\r\n  background-size: 200% 200%;\r\n  animation: gradientBG 5s ease infinite;\r\n  z-index: -1;\r\n  border-radius: 23px;\r\n}\r\n\r\n/* Özel üye QR kod hover animasyonu kaldırıldı - kullanıcı isteği üzerine */\r\n\r\n/* Özel üye için QR kod geçerlilik süresi göstergesi */\r\n.special-member .qr-validity-timer {\r\n  margin: 15px 0;\r\n}\r\n\r\n.special-member .timer-label {\r\n  color: #ffffff;\r\n  font-weight: 500;\r\n  font-size: 15px;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.special-member .timer-countdown {\r\n  color: #ffffff;\r\n  font-weight: 700;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  padding: 3px 8px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.special-member .timer-warning {\r\n  color: #ffffff;\r\n  background-color: rgba(255, 100, 100, 0.4);\r\n  animation: blinkWarning 1s infinite;\r\n}\r\n\r\n@keyframes blinkWarning {\r\n  0% { background-color: rgba(255, 100, 100, 0.4); }\r\n  50% { background-color: rgba(255, 50, 50, 0.6); }\r\n  100% { background-color: rgba(255, 100, 100, 0.4); }\r\n}\r\n\r\n.special-member .progress-bar-container {\r\n  height: 8px;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.special-member .progress-bar {\r\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));\r\n  height: 100%;\r\n  border-radius: 10px;\r\n}\r\n\r\n.special-member .progress-warning {\r\n  background: linear-gradient(90deg, rgba(255, 100, 100, 0.8), rgba(255, 50, 50, 0.6));\r\n}\r\n\r\n/* Özel üye için bilgi metni */\r\n.special-member .qr-info-text {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  color: #ffffff;\r\n  border: 1px solid rgba(255, 255, 255, 0.4);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);\r\n  font-size: 14px;\r\n  padding: 10px;\r\n  border-radius: 10px;\r\n  margin-top: 15px;\r\n  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n/* QR Kodunu İndir butonu kaldırıldı (QR kodları 5 dakika geçerli olduğu için)\r\n.special-member .download-button {\r\n  background: linear-gradient(135deg, #00c9ff, #92fe9d);\r\n  font-weight: bold;\r\n  color: #333;\r\n  font-size: 18px;\r\n  padding: 16px 24px;\r\n  border: 2px solid #ffffff;\r\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.special-member .download-button:hover {\r\n  background: linear-gradient(135deg, #92fe9d, #00c9ff);\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\r\n}\r\n*/\r\n\r\n@keyframes heartbeat {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.2);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes zoomIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: scale(0.95);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n/* Responsive Styles */\r\n@media (max-width: 767px) {\r\n  .gym-container {\r\n    padding: var(--spacing-md);\r\n  }\r\n\r\n  .gym-panel {\r\n    max-width: 100%;\r\n    padding: var(--spacing-lg);\r\n  }\r\n\r\n  .gym-icon {\r\n    width: 60px;\r\n    height: 60px;\r\n    font-size: 28px;\r\n  }\r\n\r\n  .result-area h2 {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .qr-code-container qrcode {\r\n    max-width: 100%;\r\n    height: auto;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .gym-panel {\r\n    padding: var(--spacing-md);\r\n    border-radius: var(--border-radius-md);\r\n    margin: 10px;\r\n    width: calc(100% - 20px);\r\n  }\r\n\r\n  .gym-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n    font-size: 24px;\r\n  }\r\n\r\n  .result-area {\r\n    padding: var(--spacing-sm);\r\n  }\r\n\r\n  .result-area h2 {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .remaining-days,\r\n  .future-membership,\r\n  .expired-membership,\r\n  .expired-membership-special,\r\n  .frozen-membership {\r\n    font-size: 14px;\r\n    padding: 6px;\r\n    margin: 6px 0;\r\n  }\r\n\r\n  /* QR Kodunu İndir butonu kaldırıldı\r\n  .download-button {\r\n    font-size: 14px;\r\n    padding: 12px 16px;\r\n  }\r\n  */\r\n\r\n  .special-header h2 {\r\n    font-size: 20px !important;\r\n    padding: 10px !important;\r\n  }\r\n\r\n  .hearts {\r\n    font-size: 20px;\r\n    margin: 8px 0;\r\n    gap: 10px;\r\n  }\r\n\r\n  .special-info {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .special-expired-text {\r\n    padding: 10px;\r\n    margin: 10px 0;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .special-member .qr-code-container qrcode {\r\n    padding: 12px;\r\n    border-width: 3px;\r\n  }\r\n\r\n  .special-member .timer-label {\r\n    font-size: 13px;\r\n  }\r\n\r\n  .special-member .timer-countdown {\r\n    padding: 2px 6px;\r\n  }\r\n\r\n  .special-member .progress-bar-container {\r\n    height: 6px;\r\n  }\r\n\r\n  .special-member .qr-info-text {\r\n    font-size: 12px;\r\n    padding: 8px;\r\n  }\r\n\r\n  .special-member .remaining-days {\r\n    padding: 10px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .special-member .remaining-days strong {\r\n    font-size: 16px;\r\n  }\r\n\r\n  /* QR Kodunu İndir butonu kaldırıldı\r\n  .special-member .download-button {\r\n    font-size: 16px;\r\n    padding: 12px 18px;\r\n  }\r\n  */\r\n}\r\n\r\n@media (max-height: 700px) {\r\n  .gym-panel {\r\n    max-height: 90vh;\r\n    overflow-y: auto;\r\n  }\r\n}\r\n\r\n/* Extra small devices */\r\n@media (max-width: 360px) {\r\n  .gym-panel {\r\n    padding: 12px;\r\n    margin: 5px;\r\n    width: calc(100% - 10px);\r\n  }\r\n\r\n  .gym-icon {\r\n    width: 40px;\r\n    height: 40px;\r\n    font-size: 20px;\r\n  }\r\n\r\n  .result-area h2 {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .special-header h2 {\r\n    font-size: 18px !important;\r\n    padding: 8px !important;\r\n  }\r\n\r\n  .hearts {\r\n    font-size: 18px;\r\n    margin: 6px 0;\r\n    gap: 8px;\r\n  }\r\n\r\n  .special-member .timer-label {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .special-member .timer-countdown {\r\n    padding: 2px 4px;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .special-member .progress-bar-container {\r\n    height: 5px;\r\n  }\r\n\r\n  .special-member .qr-info-text {\r\n    font-size: 11px;\r\n    padding: 6px;\r\n  }\r\n\r\n  .special-member .remaining-days {\r\n    padding: 8px;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .special-member .remaining-days strong {\r\n    font-size: 14px;\r\n  }\r\n\r\n  .remaining-days,\r\n  .future-membership,\r\n  .expired-membership,\r\n  .expired-membership-special,\r\n  .frozen-membership,\r\n  .special-expired-text {\r\n    font-size: 12px;\r\n    padding: 5px;\r\n    margin: 5px 0;\r\n  }\r\n\r\n  /* QR Kodunu İndir butonu kaldırıldı\r\n  .download-button,\r\n  .special-member .download-button {\r\n    font-size: 12px;\r\n    padding: 10px 14px;\r\n  }\r\n  */\r\n\r\n  .special-member .qr-code-container qrcode {\r\n    padding: 8px;\r\n    border-width: 2px;\r\n  }\r\n}\r\n\r\n/* Landscape mode for mobile devices */\r\n@media (max-width: 767px) and (orientation: landscape) {\r\n  .gym-container {\r\n    padding: 10px;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: flex-start;\r\n    min-height: auto;\r\n  }\r\n\r\n  .gym-panel {\r\n    max-width: 90%;\r\n    padding: 15px;\r\n    margin: 0;\r\n  }\r\n\r\n  .result-area {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .special-header,\r\n  .result-area h2 {\r\n    width: 100%;\r\n    text-align: center;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .membership-info {\r\n    width: 48%;\r\n    margin-right: 2%;\r\n  }\r\n\r\n  .qr-code-container {\r\n    width: 48%;\r\n    margin: 0;\r\n  }\r\n\r\n  /* QR Kodunu İndir butonu kaldırıldı\r\n  .download-button {\r\n    width: 100%;\r\n    margin-top: 10px;\r\n  }\r\n  */\r\n\r\n  .special-member {\r\n    padding: 15px;\r\n  }\r\n\r\n  .special-header h2 {\r\n    font-size: 20px !important;\r\n    padding: 8px !important;\r\n  }\r\n}\r\n\r\n/* Dark Mode Support */\r\n[data-theme=\"dark\"] .qr-code-container qrcode {\r\n  background-color: white; /* QR code needs to stay white for readability */\r\n}"], "mappings": ";AAEA,CAAC;AACC,aAAW;AACX,UAAQ;AACR,WAAS,IAAI;AACb;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,UAAU;AAAA,MAAE,IAAI;AACxD,cAAY;AACZ,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,eAAa,OAAO,EAAE;AACtB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACX,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,WAAS,IAAI;AACb,cAAY,IAAI;AAChB,WAAS;AACT,kBAAgB;AAChB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,aAAW,OAAO,KAAK,IAAI;AAC3B,YAAU;AACV,YAAU;AACZ;AAEA,CAfC,SAeS;AACR,WAAS;AACT,YAAU;AACV,OAAK;AACL,SAAO;AACP,SAAO;AACP,UAAQ;AACR,cAAY,IAAI;AAChB,WAAS;AACT,iBAAe;AACf,WAAS;AACX;AAEA,CA5BC,SA4BS;AACR,WAAS;AACT,YAAU;AACV,UAAQ;AACR,QAAM;AACN,SAAO;AACP,UAAQ;AACR,cAAY,IAAI;AAChB,WAAS;AACT,iBAAe;AACf,WAAS;AACX;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,iBAAe,IAAI;AACnB,YAAU;AACV,WAAS;AACX;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,IAAI,gBAAgB;AAC3E,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW;AACX,SAAO;AACP,cAAY,EAAE,KAAK,KAAK,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AAC1C,UAAQ,EAAE;AACZ;AAEA,CAAC;AACC,WAAS,IAAI,cAAc,IAAI;AAC/B,iBAAe,IAAI;AACnB,iBAAe,IAAI;AACnB,aAAW;AACX,cAAY;AACZ,YAAU;AACV,WAAS;AACT,aAAW,OAAO,KAAK,IAAI;AAC7B;AAEA,CAXC,YAWY,CAAC;AACZ,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,cAAY;AACZ,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,mBAAiB;AACjB,WAAS,KAAK;AAChB;AAEA,CAAC;AACC,cAAY;AACZ,YAAU;AACV,WAAS;AACT,aAAW,OAAO,KAAK,IAAI;AAC3B,WAAS,IAAI;AACb,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACtB,cAAY,IAAI;AAClB;AAEA,CAXC,YAWY;AACX,UAAQ,EAAE,EAAE,IAAI;AAChB,aAAW;AACX,SAAO,IAAI;AACX,eAAa;AACf;AAEA,CAAC;AACC,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,SAAO,IAAI;AACX,eAAa;AACb,UAAQ,IAAI;AACZ,WAAS;AACT,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,SAAO,IAAI;AACX,eAAa;AACb,UAAQ,IAAI;AACZ,WAAS;AACT,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACxB;AAEA,CAAC;AAAoB,CAAC;AACpB,SAAO,IAAI;AACX,eAAa;AACb,UAAQ,IAAI;AACZ,WAAS;AACT,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,SAAO,IAAI;AACX,eAAa;AACb,UAAQ,IAAI;AACZ,WAAS;AACT,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACxB;AAEA,CAAC;AACC,SAAO,IAAI;AACX,eAAa;AACb,UAAQ,KAAK;AACb,WAAS;AACT,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACtB,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,UAAQ,IAAI,cAAc;AAC1B,YAAU;AACV,SAAO;AACP,YAAU;AACZ;AAEA,CAAC;AACC,SAAO;AACP,iBAAe,IAAI;AACnB,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,iBAAe;AACf,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAAC;AACC,SAAO,IAAI;AACX,aAAW,MAAM,GAAG;AACtB;AAEA,WAHa;AAIX;AAAK,aAAS;AAAG;AACjB;AAAM,aAAS;AAAK;AACpB;AAAO,aAAS;AAAG;AACrB;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,YAAU;AACZ;AAEA,CAAC;AACC,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,KAAK;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,IAAI,WAAW;AACrE,iBAAe,IAAI;AACnB,cAAY,MAAM,GAAG;AACvB;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,KAAK;AAAA,MAAE,IAAI,UAAU,EAAE;AAAA,MAAE,IAAI,WAAW;AACtE;AAEA,CA3DC,kBA2DkB;AACjB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,WAAS,IAAI;AACb,oBAAkB;AAClB,cAAY,IAAI;AAChB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,aAAW;AACX,iBAAe,IAAI;AACnB,WAAS;AACT,mBAAiB;AACjB,eAAa;AACf;AAEA,CAzEC,kBAyEkB,OAAO;AACxB,aAAW;AACX,UAAQ;AACR,WAAS;AACT,mBAAiB;AACjB,mBAAiB;AACnB;AAuCA,CAAC;AACC,SAAO;AACP,cAAY;AACZ,aAAW;AACX,SAAO,IAAI;AACX,WAAS;AACT,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,UAAQ,IAAI,OAAO,IAAI;AACzB;AAGA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,OAAxB;AAAA,MAAiC,OAAjC;AAAA,MAA0C;AACtD,mBAAiB,KAAK;AACtB,aAAW,WAAW,IAAI,KAAK;AAC/B,WAAS,IAAI;AACb,iBAAe,IAAI;AACnB,cAAY,EAAE,EAAE,KAAK,KAAK,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;AACvC,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,YAAU;AACV,YAAU;AACZ;AAGA,WAVa;AAWX;AAAK,yBAAqB,GAAG;AAAK;AAClC;AAAM,yBAAqB,KAAK;AAAK;AACrC;AAAO,yBAAqB,GAAG;AAAK;AACtC;AAGA,CApBC,cAoBc;AACb,WAAS;AACT,YAAU;AACV,OAAK;AACL,SAAO;AACP,SAAO;AACP,UAAQ;AACR,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,iBAAe;AACf,WAAS;AACX;AAEA,CAhCC,cAgCc;AACb,WAAS;AACT,YAAU;AACV,UAAQ;AACR,QAAM;AACN,SAAO;AACP,UAAQ;AACR,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,iBAAe;AACf,WAAS;AACX;AAEA,CAAC;AACC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,SAAO;AACP,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,eAAa;AACb,eAAa,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC,cAAY,EAAE,EAAE,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACzC,WAAS;AACT,UAAQ,KAAK;AACb,aAAW;AACX,kBAAgB;AAChB,aAAW,KAAK,GAAG,SAAS;AAC5B,iBAAe;AACjB;AAEA,WAJa;AAKX;AACE,gBAAY,EAAE,EAAE,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;AACzE,iBAAa,EAAE,EAAE,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3C;AACA;AACE,gBAAY,EAAE,EAAE,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;AAC1E,iBAAa,EAAE,EAAE,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3C;AACF;AAEA,CAAC;AACC,cAAY;AACZ,iBAAe,IAAI;AACnB,YAAU;AACV,WAAS;AACX;AAEA,CAPC,eAOe;AACd,SAAO;AACP,aAAW;AACX,eAAa,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC,UAAQ,KAAK;AACb,eAAa;AACb,kBAAgB;AAChB,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,WAAS;AACT,iBAAe;AACf,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,YAAU;AACV,YAAU;AACV,mBAAiB,KAAK;AACtB,2BAAyB,KAAK;AAChC;AAEA,CAxBC,eAwBe,EAAE;AAChB,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,KAAK;AAAA,MAAE,WAAW;AAAA,MAAE,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;AAAA,MAAE;AAC1E,aAAW,MAAM,GAAG;AACtB;AAEA,WAHa;AAIX;AAAK,UAAM;AAAO;AAClB;AAAM,UAAM;AAAM;AAClB;AAAO,UAAM;AAAM;AACrB;AAEA,CAAC;AACC,aAAW;AACX,UAAQ,KAAK;AACb,WAAS;AACT,mBAAiB;AACjB,OAAK;AACP;AAEA,CARC,OAQO;AACN,aAAW,UAAU,KAAK;AAC1B,UAAQ,YAAY,EAAE,EAAE,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChD,WAAS;AACT,oBAAkB;AACpB;AAEA,CAfC,OAeO,IAAI;AACV,mBAAiB;AACnB;AAEA,CAnBC,OAmBO,IAAI;AACV,mBAAiB;AACnB;AAEA,CAvBC,OAuBO,IAAI;AACV,mBAAiB;AACnB;AAEA,WAlBa;AAmBX;AACE,eAAW,MAAM;AACnB;AACA;AACE,eAAW,MAAM;AACnB;AACA;AACE,eAAW,MAAM;AACnB;AACA;AACE,eAAW,MAAM;AACnB;AACA;AACE,eAAW,MAAM;AACnB;AACA;AACE,eAAW,MAAM;AACnB;AACF;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACX,eAAa,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC,YAAU;AACV,WAAS;AACX;AAGA,CAxKC,eAwKe,CAxVf;AAyVC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,SAAO;AACP,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,eAAa;AACb,eAAa,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC,cAAY,EAAE,EAAE,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACzC,WAAS;AACT,UAAQ,KAAK;AACb,iBAAe;AACf,cAAY,IAAI,KAAK;AACvB;AAEA,CArLC,eAqLe,CArWf,cAqW8B;AAC7B,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC7C;AAEA,CA3LC,eA2Le,CA3Wf,eA2W+B;AAC9B,SAAO;AACP,aAAW;AACX,UAAQ,EAAE;AACZ;AAEA,CAjMC,eAiMe,CAnUf;AAoUC,YAAU;AACV,WAAS;AACT,UAAQ,KAAK;AACf;AAEA,CAvMC,eAuMe,CAzUf,kBAyUkC;AACjC,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,cAAY,EAAE,EAAE,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACzC,iBAAe;AACf,WAAS;AACT,aAAW,MAAM;AACjB,cAAY,IAAI,KAAK;AACrB,oBAAkB;AAClB,YAAU;AACZ;AAEA,CAlNC,eAkNe,CApVf,kBAoVkC,MAAM;AACvC,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,KAAhB;AAAA,MAAuB,OAAvB;AAAA,MAAgC,OAAhC;AAAA,MAAyC;AACrD,mBAAiB,KAAK;AACtB,aAAW,WAAW,GAAG,KAAK;AAC9B,WAAS;AACT,iBAAe;AACjB;AAKA,CAnOC,eAmOe,CA3Vf;AA4VC,UAAQ,KAAK;AACf;AAEA,CAvOC,eAuOe,CAzVf;AA0VC,SAAO;AACP,eAAa;AACb,aAAW;AACX,eAAa,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC,iBAAe;AACjB;AAEA,CA/OC,eA+Oe,CAzVf;AA0VC,SAAO;AACP,eAAa;AACb,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,WAAS,IAAI;AACb,iBAAe;AACf,cAAY,EAAE,EAAE,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3C;AAEA,CAxPC,eAwPe,CA7Vf;AA8VC,SAAO;AACP,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,aAAW,aAAa,GAAG;AAC7B;AAEA,WAHa;AAIX;AAAK,sBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAAM;AACjD;AAAM,sBAAkB,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAAM;AAChD;AAAO,sBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAAM;AACrD;AAEA,CApQC,eAoQe,CA9Vf;AA+VC,UAAQ;AACR,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,iBAAe;AACf,YAAU;AACV,cAAY,EAAE,EAAE,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACzC,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,CA7QC,eA6Qe,CA/Vf;AAgWC;AAAA,IAAY;AAAA,MAAgB,KAAhB;AAAA,MAAuB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAA3C;AAAA,MAAiD,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACjF,UAAQ;AACR,iBAAe;AACjB;AAEA,CAnRC,eAmRe,CA9Vf;AA+VC;AAAA,IAAY;AAAA,MAAgB,KAAhB;AAAA,MAAuB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAA3C;AAAA,MAAiD,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AACjF;AAGA,CAxRC,eAwRe,CApSf;AAqSC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,SAAO;AACP,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,eAAa,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC,aAAW;AACX,WAAS;AACT,iBAAe;AACf,cAAY;AACZ,cAAY,EAAE,EAAE,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3C;AAqBA,WA/La;AAgMX;AACE,eAAW,MAAM;AACnB;AACA;AACE,eAAW,MAAM;AACnB;AACA;AACE,eAAW,MAAM;AACnB;AACF;AAEA,WA/hBa;AAgiBX;AACE,aAAS;AACX;AACA;AACE,aAAS;AACX;AACF;AAEA,WAtmBa;AAumBX;AACE,aAAS;AACT,eAAW,MAAM;AACnB;AACA;AACE,aAAS;AACT,eAAW,MAAM;AACnB;AACF;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA1oBD;AA2oBG,aAAS,IAAI;AACf;AAEA,GAjoBD;AAkoBG,eAAW;AACX,aAAS,IAAI;AACf;AAEA,GAplBD;AAqlBG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AAEA,GA9iBD,YA8iBc;AACX,eAAW;AACb;AAEA,GA9eD,kBA8eoB;AACjB,eAAW;AACX,YAAQ;AACV;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAvpBD;AAwpBG,aAAS,IAAI;AACb,mBAAe,IAAI;AACnB,YAAQ;AACR,WAAO,KAAK,KAAK,EAAE;AACrB;AAEA,GA5mBD;AA6mBG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AAEA,GAtkBD;AAukBG,aAAS,IAAI;AACf;AAEA,GA1kBD,YA0kBc;AACX,eAAW;AACb;AAEA,GAxjBD;AAAA,EAyjBC,CAhjBD;AAAA,EAijBC,CAxiBD;AAAA,EAyiBC,CAziBoB;AAAA,EA0iBpB,CAjiBD;AAkiBG,eAAW;AACX,aAAS;AACT,YAAQ,IAAI;AACd;AASA,GAnVD,eAmViB;AACd,eAAW;AACX,aAAS;AACX;AAEA,GA/SD;AAgTG,eAAW;AACX,YAAQ,IAAI;AACZ,SAAK;AACP;AAEA,GArQD;AAsQG,eAAW;AACb;AAEA,GA5XD;AA6XG,aAAS;AACT,YAAQ,KAAK;AACb,eAAW;AACb;AAEA,GA9aD,eA8aiB,CAhjBjB,kBAgjBoC;AACjC,aAAS;AACT,kBAAc;AAChB;AAEA,GAnbD,eAmbiB,CAriBjB;AAsiBG,eAAW;AACb;AAEA,GAvbD,eAubiB,CAjiBjB;AAkiBG,aAAS,IAAI;AACf;AAEA,GA3bD,eA2biB,CArhBjB;AAshBG,YAAQ;AACV;AAEA,GA/bD,eA+biB,CA3cjB;AA4cG,eAAW;AACX,aAAS;AACX;AAEA,GApcD,eAociB,CApnBjB;AAqnBG,aAAS;AACT,eAAW;AACb;AAEA,GAzcD,eAyciB,CAznBjB,eAynBiC;AAC9B,eAAW;AACb;AAQF;AAEA,OAAO,CAAC,UAAU,EAAE;AAClB,GA1vBD;AA2vBG,gBAAY;AACZ,gBAAY;AACd;AACF;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAlwBD;AAmwBG,aAAS;AACT,YAAQ;AACR,WAAO,KAAK,KAAK,EAAE;AACrB;AAEA,GAttBD;AAutBG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AAEA,GAhrBD,YAgrBc;AACX,eAAW;AACb;AAEA,GAxaD,eAwaiB;AACd,eAAW;AACX,aAAS;AACX;AAEA,GApYD;AAqYG,eAAW;AACX,YAAQ,IAAI;AACZ,SAAK;AACP;AAEA,GAzfD,eAyfiB,CA3mBjB;AA4mBG,eAAW;AACb;AAEA,GA7fD,eA6fiB,CAvmBjB;AAwmBG,aAAS,IAAI;AACb,eAAW;AACb;AAEA,GAlgBD,eAkgBiB,CA5lBjB;AA6lBG,YAAQ;AACV;AAEA,GAtgBD,eAsgBiB,CAlhBjB;AAmhBG,eAAW;AACX,aAAS;AACX;AAEA,GA3gBD,eA2gBiB,CA3rBjB;AA4rBG,aAAS;AACT,eAAW;AACb;AAEA,GAhhBD,eAghBiB,CAhsBjB,eAgsBiC;AAC9B,eAAW;AACb;AAEA,GApsBD;AAAA,EAqsBC,CA5rBD;AAAA,EA6rBC,CAprBD;AAAA,EAqrBC,CArrBoB;AAAA,EAsrBpB,CA7qBD;AAAA,EA8qBC,CA7eD;AA8eG,eAAW;AACX,aAAS;AACT,YAAQ,IAAI;AACd;AAUA,GAviBD,eAuiBiB,CAzqBjB,kBAyqBoC;AACjC,aAAS;AACT,kBAAc;AAChB;AACF;AAGA,OAAO,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,WAAW,EAAE;AAC1C,GAh2BD;AAi2BG,aAAS;AACT,aAAS;AACT,qBAAiB;AACjB,iBAAa;AACb,gBAAY;AACd;AAEA,GA31BD;AA41BG,eAAW;AACX,aAAS;AACT,YAAQ;AACV;AAEA,GAnwBD;AAowBG,aAAS;AACT,eAAW;AACX,qBAAiB;AACjB,iBAAa;AACf;AAEA,GA9fD;AAAA,EA+fC,CA3wBD,YA2wBc;AACX,WAAO;AACP,gBAAY;AACZ,mBAAe;AACjB;AAEA,GA/vBD;AAgwBG,WAAO;AACP,kBAAc;AAChB;AAEA,GAltBD;AAmtBG,WAAO;AACP,YAAQ;AACV;AASA,GA5lBD;AA6lBG,aAAS;AACX;AAEA,GA1hBD,eA0hBiB;AACd,eAAW;AACX,aAAS;AACX;AACF;AAGA,CAAC,iBAAmB,CAzuBnB,kBAyuBsC;AACrC,oBAAkB;AACpB;", "names": []}