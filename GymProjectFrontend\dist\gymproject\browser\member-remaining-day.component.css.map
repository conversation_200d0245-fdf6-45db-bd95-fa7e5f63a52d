{"version": 3, "sources": ["src/app/components/member-remaining-day/member-remaining-day.component.css"], "sourcesContent": ["/* Member Remaining Day Component Styles */\r\n\r\n/* Loading Spinner */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(255, 255, 255, 0.8);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(3px);\r\n}\r\n\r\n.spinner-container {\r\n  text-align: center;\r\n}\r\n\r\n/* Content Blur */\r\n.content-blur {\r\n  filter: blur(3px);\r\n  pointer-events: none;\r\n}\r\n\r\n/* Fade In Animation */\r\n.fade-in {\r\n  animation: fadeIn 0.5s ease-out;\r\n}\r\n\r\n/* Modern Stats Cards */\r\n.modern-stats-card {\r\n  border-radius: var(--border-radius-lg);\r\n  padding: 1.5rem;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.modern-stats-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.modern-stats-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 1.5rem;\r\n  margin-right: 1rem;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  flex-shrink: 0;\r\n}\r\n\r\n.modern-stats-info {\r\n  flex-grow: 1;\r\n}\r\n\r\n.modern-stats-value {\r\n  font-size: 1.75rem;\r\n  font-weight: 700;\r\n  margin-bottom: 0.25rem;\r\n}\r\n\r\n.modern-stats-label {\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.modern-stats-subtext {\r\n  font-size: 0.875rem;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* Background Gradients */\r\n.bg-warning-gradient {\r\n  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);\r\n  color: white;\r\n}\r\n\r\n.bg-danger-gradient {\r\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\r\n  color: white;\r\n}\r\n\r\n.bg-info-gradient {\r\n  background: linear-gradient(135deg, #0dcaf0 0%, #0097b2 100%);\r\n  color: white;\r\n}\r\n\r\n/* Member Avatar */\r\n.member-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.member-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-weight: 600;\r\n  margin-right: 0.75rem;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* Status Badge */\r\n.status-badge {\r\n  display: inline-block;\r\n  padding: 0.35rem 0.75rem;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  line-height: 1;\r\n  text-align: center;\r\n  white-space: nowrap;\r\n  vertical-align: baseline;\r\n  border-radius: 50rem;\r\n}\r\n\r\n.status-danger {\r\n  background-color: var(--danger-light);\r\n  color: var(--danger);\r\n}\r\n\r\n.status-warning {\r\n  background-color: var(--warning-light);\r\n  color: var(--warning);\r\n}\r\n\r\n.status-success {\r\n  background-color: var(--success-light);\r\n  color: var(--success);\r\n}\r\n\r\n/* Search Box */\r\n.search-box {\r\n  max-width: 300px;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n/* Table Styles */\r\n.modern-table {\r\n  width: 100%;\r\n  border-collapse: separate;\r\n  border-spacing: 0;\r\n}\r\n\r\n.modern-table th {\r\n  background-color: var(--bg-secondary);\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  font-size: 0.75rem;\r\n  letter-spacing: 0.5px;\r\n  padding: 1rem;\r\n  border-bottom: 2px solid var(--border-color);\r\n}\r\n\r\n.modern-table td {\r\n  padding: 1rem;\r\n  vertical-align: middle;\r\n  border-bottom: 1px solid var(--border-color);\r\n}\r\n\r\n.modern-table tbody tr {\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.modern-table tbody tr:hover {\r\n  background-color: var(--primary-light);\r\n}\r\n\r\n/* Sort Button */\r\n.sort-button {\r\n  background: none;\r\n  border: none;\r\n  cursor: pointer;\r\n  color: var(--text-secondary);\r\n  padding: 0.25rem;\r\n  border-radius: 4px;\r\n  transition: all 0.2s ease;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.sort-button:hover {\r\n  background-color: var(--primary-light);\r\n  color: var(--primary);\r\n}\r\n\r\n.sort-button:focus {\r\n  outline: none;\r\n  box-shadow: 0 0 0 2px var(--primary-light);\r\n}\r\n\r\n/* Empty State */\r\n.empty-state {\r\n  padding: 3rem 1rem;\r\n  text-align: center;\r\n  color: var(--text-secondary);\r\n}\r\n\r\n/* Animations */\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(10px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n/* Dark Mode Support */\r\n[data-theme=\"dark\"] .loading-overlay {\r\n  background-color: rgba(18, 18, 18, 0.8);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-table th {\r\n  background-color: var(--bg-tertiary);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-table tbody tr:hover {\r\n  background-color: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n/* Responsive Adjustments */\r\n@media (max-width: 767.98px) {\r\n  .modern-stats-card {\r\n    margin-bottom: 1rem;\r\n  }\r\n  \r\n  .modern-stats-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n    font-size: 1.25rem;\r\n  }\r\n  \r\n  .modern-stats-value {\r\n    font-size: 1.5rem;\r\n  }\r\n  \r\n  .header-actions {\r\n    margin-top: 1rem;\r\n    width: 100%;\r\n  }\r\n  \r\n  .search-box {\r\n    max-width: 100%;\r\n    width: 100%;\r\n  }\r\n  \r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n}\r\n"], "mappings": ";AAGA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACxB;AAEA,CAAC;AACC,cAAY;AACd;AAGA,CAAC;AACC,UAAQ,KAAK;AACb,kBAAgB;AAClB;AAGA,CAAC;AACC,aAAW,OAAO,KAAK;AACzB;AAGA,CAAC;AACC,iBAAe,IAAI;AACnB,WAAS;AACT,UAAQ;AACR,WAAS;AACT,eAAa;AACb,cAAY,IAAI,KAAK;AACrB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACrC,iBAAe;AACjB;AAEA,CAXC,iBAWiB;AAChB,aAAW,WAAW;AACtB,cAAY,EAAE,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACxC;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW;AACX,gBAAc;AACd,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,eAAa;AACf;AAEA,CAAC;AACC,aAAW;AACb;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,iBAAe;AACjB;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,iBAAe;AACjB;AAEA,CAAC;AACC,aAAW;AACX,WAAS;AACX;AAGA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACT;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACT;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACT;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACf;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,eAAa;AACb,gBAAc;AACd,eAAa;AACf;AAGA,CAAC;AACC,WAAS;AACT,WAAS,QAAQ;AACjB,aAAW;AACX,eAAa;AACb,eAAa;AACb,cAAY;AACZ,eAAa;AACb,kBAAgB;AAChB,iBAAe;AACjB;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAGA,CAAC;AACC,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAGA,CAAC;AACC,SAAO;AACP,mBAAiB;AACjB,kBAAgB;AAClB;AAEA,CANC,aAMa;AACZ,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,eAAa;AACb,kBAAgB;AAChB,aAAW;AACX,kBAAgB;AAChB,WAAS;AACT,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CAjBC,aAiBa;AACZ,WAAS;AACT,kBAAgB;AAChB,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAEA,CAvBC,aAuBa,MAAM;AAClB,cAAY,iBAAiB,KAAK;AACpC;AAEA,CA3BC,aA2Ba,MAAM,EAAE;AACpB,oBAAkB,IAAI;AACxB;AAGA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,UAAQ;AACR,SAAO,IAAI;AACX,WAAS;AACT,iBAAe;AACf,cAAY,IAAI,KAAK;AACrB,WAAS;AACT,eAAa;AACb,mBAAiB;AACnB;AAEA,CAbC,WAaW;AACV,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAlBC,WAkBW;AACV,WAAS;AACT,cAAY,EAAE,EAAE,EAAE,IAAI,IAAI;AAC5B;AAGA,CAAC;AACC,WAAS,KAAK;AACd,cAAY;AACZ,SAAO,IAAI;AACb;AAGA,WAhMa;AAiMX;AAAO,aAAS;AAAG,eAAW,WAAW;AAAO;AAChD;AAAK,aAAS;AAAG,eAAW,WAAW;AAAI;AAC7C;AAGA,CAAC,iBAAmB,CAhOnB;AAiOC,oBAAkB,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACrC;AAEA,CAAC,iBAAmB,CAzEnB,aAyEiC;AAChC,oBAAkB,IAAI;AACxB;AAEA,CAAC,iBAAmB,CA7EnB,aA6EiC,MAAM,EAAE;AACxC,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAhND;AAiNG,mBAAe;AACjB;AAEA,GApMD;AAqMG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AAEA,GAzLD;AA0LG,eAAW;AACb;AAEA,GAxGD;AAyGG,gBAAY;AACZ,WAAO;AACT;AAEA,GAjHD;AAkHG,eAAW;AACX,WAAO;AACT;AAEA,GAAC;AACC,oBAAgB;AAChB,iBAAa;AACf;AACF;", "names": []}