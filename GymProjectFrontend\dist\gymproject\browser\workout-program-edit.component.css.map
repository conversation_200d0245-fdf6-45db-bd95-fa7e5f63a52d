{"version": 3, "sources": ["src/app/components/workout-programs/workout-program-edit.component.css"], "sourcesContent": ["/* Workout Program Edit Specific Styles */\n\n/* Sticky Header */\n.sticky-header {\n  position: sticky;\n  top: 0;\n  z-index: 1020;\n  background-color: var(--bg-primary);\n  border-bottom: 1px solid var(--border-color);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  transition: all var(--transition-speed) var(--transition-timing);\n}\n\n[data-theme=\"dark\"] .sticky-header {\n  background-color: var(--bg-primary);\n  border-bottom-color: var(--border-color);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.main-content {\n  padding-top: 0;\n}\n\n.day-card {\n  border: 1px solid var(--border-color);\n  border-radius: var(--border-radius-lg);\n  background-color: var(--bg-primary);\n  transition: all var(--transition-speed) var(--transition-timing);\n}\n\n.day-card:hover {\n  box-shadow: var(--shadow-sm);\n  transform: translateY(-2px);\n}\n\n.day-header {\n  padding: 1rem 1.25rem;\n  border-bottom: 1px solid var(--border-color);\n  background-color: var(--bg-secondary);\n  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;\n}\n\n.day-body {\n  padding: 1.25rem;\n}\n\n.day-number {\n  font-weight: 600;\n  color: var(--primary);\n}\n\n.day-name-preview {\n  font-weight: 500;\n  color: var(--text-secondary);\n}\n\n.exercise-summary {\n  padding: 0.75rem 1rem;\n  background-color: var(--bg-secondary);\n  border-radius: var(--border-radius-md);\n  border: 1px solid var(--border-color);\n}\n\n.form-check-input:checked {\n  background-color: var(--primary);\n  border-color: var(--primary);\n}\n\n.form-check-input:focus {\n  border-color: var(--primary);\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem var(--primary-light);\n}\n\n.form-check-label {\n  font-weight: 500;\n  margin-left: 0.5rem;\n}\n\n.is-invalid {\n  border-color: var(--danger);\n}\n\n.invalid-feedback {\n  display: block;\n  width: 100%;\n  margin-top: 0.25rem;\n  font-size: 0.875rem;\n  color: var(--danger);\n}\n\n.alert {\n  padding: 0.75rem 1rem;\n  margin-bottom: 0;\n  border: 1px solid transparent;\n  border-radius: var(--border-radius-md);\n}\n\n.alert-warning {\n  color: var(--warning);\n  background-color: var(--warning-light);\n  border-color: rgba(var(--warning-rgb), 0.2);\n}\n\n.spinner-border {\n  width: 3rem;\n  height: 3rem;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .sticky-header .modern-card-header {\n    padding: 0.75rem 1rem;\n  }\n\n  .sticky-header .modern-card-header .d-flex.gap-2 {\n    flex-direction: row;\n    gap: 0.25rem !important;\n  }\n\n  .sticky-header .modern-btn {\n    padding: 0.375rem 0.75rem;\n    font-size: 0.875rem;\n  }\n\n  .sticky-header h4 {\n    font-size: 1.1rem;\n  }\n\n  .day-header {\n    padding: 0.75rem 1rem;\n  }\n\n  .day-body {\n    padding: 1rem;\n  }\n\n  .modern-btn-sm {\n    padding: 0.25rem 0.5rem;\n    font-size: 0.75rem;\n  }\n\n  .d-flex.gap-2:not(.sticky-header .d-flex.gap-2) {\n    flex-direction: column;\n    gap: 0.5rem !important;\n  }\n\n  .day-name-preview {\n    display: block;\n    margin-top: 0.25rem;\n  }\n}\n\n/* Dark mode specific adjustments */\n[data-theme=\"dark\"] .day-card {\n  background-color: var(--bg-secondary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .day-header {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .exercise-summary {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .form-check-input {\n  background-color: var(--bg-tertiary);\n  border-color: var(--border-color);\n}\n\n[data-theme=\"dark\"] .form-check-input:checked {\n  background-color: var(--primary);\n  border-color: var(--primary);\n}\n\n[data-theme=\"dark\"] .alert-warning {\n  background-color: var(--warning-light);\n  border-color: rgba(var(--warning-rgb), 0.3);\n  color: var(--warning);\n}\n"], "mappings": ";AAGA,CAAC;AACC,YAAU;AACV,OAAK;AACL,WAAS;AACT,oBAAkB,IAAI;AACtB,iBAAe,IAAI,MAAM,IAAI;AAC7B,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAAC,iBAAmB,CAVnB;AAWC,oBAAkB,IAAI;AACtB,uBAAqB,IAAI;AACzB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAAC;AACC,eAAa;AACf;AAEA,CAAC;AACC,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,oBAAkB,IAAI;AACtB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAPC,QAOQ;AACP,cAAY,IAAI;AAChB,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,WAAS,KAAK;AACd,iBAAe,IAAI,MAAM,IAAI;AAC7B,oBAAkB,IAAI;AACtB,iBAAe,IAAI,oBAAoB,IAAI,oBAAoB,EAAE;AACnE;AAEA,CAAC;AACC,WAAS;AACX;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS,QAAQ;AACjB,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC,gBAAgB;AACf,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CALC,gBAKgB;AACf,gBAAc,IAAI;AAClB,WAAS;AACT,cAAY,EAAE,EAAE,EAAE,QAAQ,IAAI;AAChC;AAEA,CAAC;AACC,eAAa;AACb,eAAa;AACf;AAEA,CAAC;AACC,gBAAc,IAAI;AACpB;AAEA,CAAC;AACC,WAAS;AACT,SAAO;AACP,cAAY;AACZ,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS,QAAQ;AACjB,iBAAe;AACf,UAAQ,IAAI,MAAM;AAClB,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,gBAAc,KAAK,IAAI,cAAc,EAAE;AACzC;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACV;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA5GD,cA4GgB,CAAC;AACd,aAAS,QAAQ;AACnB;AAEA,GAhHD,cAgHgB,CAJC,mBAImB,CAAC,MAAM,CAAC;AACzC,oBAAgB;AAChB,SAAK;AACP;AAEA,GArHD,cAqHgB,CAAC;AACd,aAAS,SAAS;AAClB,eAAW;AACb;AAEA,GA1HD,cA0HgB;AACb,eAAW;AACb;AAEA,GA9FD;AA+FG,aAAS,QAAQ;AACnB;AAEA,GA3FD;AA4FG,aAAS;AACX;AAEA,GAAC;AACC,aAAS,QAAQ;AACjB,eAAW;AACb;AAEA,GA3BoC,MA2B7B,CA3BoC,KA2B9B,KAAK,CA3InB,cA2IkC,CA3BG,MA2BI,CA3BG;AA4BzC,oBAAgB;AAChB,SAAK;AACP;AAEA,GAhGD;AAiGG,aAAS;AACT,gBAAY;AACd;AACF;AAGA,CAAC,iBAAmB,CAnInB;AAoIC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CA5HnB;AA6HC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CA5GnB;AA6GC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CA1GnB;AA2GC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CA/GnB,gBA+GoC;AACnC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAjFnB;AAkFC,oBAAkB,IAAI;AACtB,gBAAc,KAAK,IAAI,cAAc,EAAE;AACvC,SAAO,IAAI;AACb;", "names": []}